'use client'
import { RegisterOptions, useForm } from 'react-hook-form'
import apiRequest from '@/lib/auth/client/request'
import { enqueueSnackbar as notify } from 'notistack'
import { handleError } from '@/lib/error'
import { Suspense } from 'react'
import Input from '../inputs/Input'
import TextFieldInput from '../inputs/TextFieldInput'

interface FormData {
    name: string
    email: string
    message: string
}

function ContactFormMain() {
    const {
        register,
        handleSubmit,
        setError,
        reset,
        formState: { errors, isSubmitting },
    } = useForm<FormData>()

    const inputArray: {
        label: string
        name: keyof FormData
        type: string
        placeholder: string
        validation: RegisterOptions<FormData, keyof FormData>
    }[] = [
        {
            label: 'Full  Name*',
            name: 'name',
            type: 'text',
            placeholder: 'Enter your full name',
            validation: {
                required: {
                    value: true,
                    message: `Full name is required`,
                },
            },
        },
        {
            label: 'Email*',
            name: 'email',
            type: 'email',
            placeholder: 'Enter your email',
            validation: {
                required: {
                    value: true,
                    message: `Email is required`,
                },
            },
        },
    ]

    const onSubmit = async (data: FormData) => {
        const formData = {
            name: data.name,
            email: data.email,
            message: data.message,
        }
        try {
            const request = await apiRequest(false).post(
                '/api/misc/contact',
                formData
            )
            const { message } = request.data
            reset()
            notify(message)
        } catch (err: unknown) {
            const error: ApiWithFormError<FormData> = handleError(err)
            if (error.errors) {
                error.errors.forEach((e) => {
                    setError(e.path, {
                        message: e.message,
                    })
                })
            }
            if (error.message && !error.errors) {
                notify(error.message)
            }
        }
    }

    return (
        <form
            method="POST"
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-[2.4rem]"
        >
            <div className="flex flex-col gap-5">
                {inputArray.map((input) => (
                    <Input<FormData>
                        key={input.name}
                        type={input.type}
                        label={input.label}
                        registerName={input.name}
                        placeholder={input.placeholder}
                        register={register}
                        errors={errors}
                        validation={input.validation}
                    />
                ))}
                <TextFieldInput
                    label={'Your Message'}
                    registerName={'message'}
                    placeholder={'Enter your full name'}
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: `Message is required`,
                        },
                        minLength: {
                            value: 10,
                            message: 'Message is too short',
                        },
                    }}
                />
            </div>

            <div className="flex flex-col gap-4">
                <button
                    disabled={isSubmitting}
                    type="submit"
                    className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] disabled:opacity-50`}
                >
                    {isSubmitting ? (
                        <svg
                            className="animate-spin h-5 w-5 mr-3 border-white border-t-2 border-b-2 border-r-2 border-[rgba(255,255,255,0.2)] rounded-full"
                            viewBox="0 0 24 24"
                        />
                    ) : null}
                    Contact
                </button>
            </div>
        </form>
    )
}

export default function ContactForm() {
    return (
        <Suspense>
            <ContactFormMain />
        </Suspense>
    )
}
