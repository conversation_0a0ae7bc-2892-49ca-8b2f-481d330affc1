'use client'

import Image from 'next/image'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { DatePicker } from '@mantine/dates'
import { Modal } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import DatePick from './DatePick'
interface FormData {
    search: string
}

export default function SearchAndFilter() {
    const {
        register,
        handleSubmit,
        watch,
        // formState: { errors },
    } = useForm<FormData>()

    const searchParams = useSearchParams()
    const path = usePathname()
    const { replace } = useRouter()
    const status = searchParams.get('status')?.toString() || ''
    const search = watch('search')
    const [opened, { open, close }] = useDisclosure(false)

    const [dateValue, setDateValue] = useState<[Date | null, Date | null]>([
        searchParams.get('startDate')
            ? new Date(searchParams.get('startDate')!)
            : null,
        searchParams.get('endDate')
            ? new Date(searchParams.get('endDate')!)
            : null,
    ])

    const onSubmit = useCallback(
        (data: FormData) => {
            const params = new URLSearchParams(searchParams)
            if (data.search) params.set('search', data.search)
            else params.delete('search')
            replace(`${path}?${params.toString()}`)
        },
        [replace, path, searchParams]
    )

    const setStatusParam = (status: string) => {
        const params = new URLSearchParams(searchParams)
        if (status) params.set('status', status)
        else params.delete('status')
        replace(`${path}?${params.toString()}`)
    }

    const setStartDateParam = (date: Date, params: URLSearchParams) => {
        const startDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
        if (startDate) params.set('startDate', startDate as unknown as string)
        else params.delete('startDate')
    }

    const setEndDateParam = (date: Date, params: URLSearchParams) => {
        const endDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
        if (endDate) params.set('endDate', endDate as unknown as string)
        else params.delete('endDate')
    }

    useEffect(() => {
        if (!search) {
            onSubmit({ search: search })
        }
    }, [search, onSubmit])

    return (
        <>
            <div className="pb-8 md:border-b border-[#F5F5F5] flex flex-col xl:flex-row items-start gap-5 justify-between w-full">
                <Modal
                    opened={opened}
                    onClose={close}
                    withCloseButton={false}
                    size="auto"
                    centered
                >
                    <div className="flex flex-col p-5">
                        <DatePicker
                            type="range"
                            allowSingleDateInRange
                            value={dateValue}
                            onChange={setDateValue}
                        />
                        <div className="py-4 flex items-center justify-between gap-3 w-full">
                            <button
                                className="py-[.625rem] px-[.875rem] rounded-lg text-sm text-[#344054] border border-[#D0D5DD]"
                                onClick={close}
                            >
                                Cancel
                            </button>
                            <button
                                className="py-[.625rem] px-[.875rem] rounded-lg text-sm text-white bg-[var(--pColor)]"
                                onClick={() => {
                                    const params = new URLSearchParams(
                                        searchParams
                                    )
                                    if (dateValue[0])
                                        setStartDateParam(dateValue[0], params)
                                    if (dateValue[1])
                                        setEndDateParam(dateValue[1], params)
                                    replace(`${path}?${params.toString()}`)
                                    close()
                                }}
                            >
                                Apply
                            </button>
                        </div>
                    </div>
                </Modal>
                <div>
                    <div className="flex rounded-lg border border-[#D0D5DD] divide-x divide-[#D0D5DD] text-center">
                        {['All', 'Success', 'Failed', 'Pending'].map((item) => {
                            return (
                                <div
                                    key={item}
                                    onClick={() => {
                                        if (item === 'All') setStatusParam('')
                                        else setStatusParam(item.toUpperCase())
                                    }}
                                    className={`py-2 px-[.625rem] text-[#344054] cursor-pointer md:px-8 hover:bg-[#F9FAFB] ${item.toLowerCase() === (status?.toLowerCase() || 'all') && 'bg-[#F9FAFB]'}`}
                                >
                                    {item}
                                </div>
                            )
                        })}
                    </div>
                    <DatePick open={open} />
                </div>
                <div className="w-full xl:max-w-[50%] relative">
                    <div className="flex gap-[.375rem] md:gap-3 w-full">
                        <div className="flex items-center w-full border border-[#D0D5DD] rounded-lg overflow-hidden pl-[.875rem] gap-2">
                            <div className="w-5">
                                <Image
                                    height={20}
                                    width={20}
                                    alt="search"
                                    src="/icons/search.svg"
                                    className="w-full h-auto"
                                />
                            </div>
                            <input
                                type="search"
                                placeholder="Search"
                                defaultValue={searchParams
                                    .get('search')
                                    ?.toString()}
                                {...register('search', {
                                    required: {
                                        value: false,
                                        message: `Input can't be empty`,
                                    },
                                })}
                                className="placeholder:text-[#667085] outline-none appearance-none outline-0 ring-0  p-[.875rem] pl-0 w-full"
                            />
                        </div>
                        <button
                            className="py-[.625rem] px-6 md:px-8 bg-primary rounded-lg text-white"
                            onClick={handleSubmit(onSubmit)}
                        >
                            Search
                        </button>
                    </div>
                    {/* {errors?.search && (
                    <span className="text-sm text-[#475467] absolute top-[calc(100%+4px)]">
                        {errors.search?.message as string}
                    </span>
                )} */}
                </div>
            </div>
        </>
    )
}
