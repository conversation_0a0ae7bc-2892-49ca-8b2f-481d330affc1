import Image from 'next/image'
import ContactForm from './ContactForm'

export default function Content() {
    return (
        <div className="px-4 max-w-6xl mx-auto md:mb-[4rem]">
            <div className="flex flex-col md:flex-row gap-8 py-[2rem] md:items-center">
                <div className="md:basis-1/2 text-center md:text-left gap-4 md:gap-[1.4375rem] flex flex-col items-center md:items-start relative justify-center">
                    <div className="w-[300px] h-[300px] rounded-full absolute bottom-1/2 md:bottom-[89%] left-1/2 translate-y-1/2 -translate-x-1/2 md:translate-x-0 md:translate-y-[70%] bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-[#C4FFCF4D] to-[#FFFFFF4D] z-[6]"></div>
                    <h1
                        className={`font-brico max-w-[39.8125rem] font-extrabold text-[2.75rem] leading-[3.25rem] lg:text-[4.6875rem] lg:leading-[5rem] text-[#0C111D] z-[8]`}
                    >
                        {`We’d `}
                        <span className="relative">
                            <span>Love </span>
                            <Image
                                src="/icons/greenCrayon.svg"
                                alt="simple crayon"
                                height={42}
                                width={312}
                                className="absolute -translate-x-1/2 -translate-y-1/2 top-1/2 left-[62%] z-[-1]"
                            />
                        </span>
                        <span className="ml-3">to Hear From You</span>
                    </h1>
                    <p className="leading-[21.6px] text-[#344054] md:text-[24px] md:leading-[32px] z-[6]">
                        {`Have questions or feedback? Reach out, and we’ll get back to you as soon as possible.`}
                    </p>
                    <p className="text-[#344054] text-sm md:text-base z-[6]">
                        {`Contact us for:`}
                    </p>
                    <div className="flex items-center gap-3 md:gap-2 md:flex-col md:items-start">
                        {[
                            {
                                color: '#004EEC',
                                text: 'Bill Payment Issues',
                                new: false,
                            },
                            {
                                color: '#01DD3F',
                                text: 'Wallet Funding Issues',
                                new: false,
                            },
                            {
                                color: '#815CEB',
                                text: 'Other Enquiries',
                                new: false,
                            },
                        ].map((service) => (
                            <div
                                key={service.text}
                                className="flex items-center gap-2"
                            >
                                <span
                                    className="w-[.375rem] h-[.375rem] rounded-full"
                                    style={{ backgroundColor: service.color }}
                                ></span>{' '}
                                <p className="text-[#475467] text-[.75rem] leading-[1.0125rem] md:leading-[1.35rem]">
                                    {service.text}
                                </p>{' '}
                                {service.new && (
                                    <span className="py-[.1875rem] px-[.375rem] rounded border-[.0313rem] border-primary text-primary text-[.625rem] leading-[.7562rem] md:leading-[.9075rem] md:text-[.75rem] font-inter">
                                        New
                                    </span>
                                )}
                            </div>
                        ))}
                    </div>
                </div>
                <div className="md:basis-1/2 flex">
                    <div className="max-w-lg mx-auto border border-white rounded-3xl p-6 lg:p-8 w-full">
                        <ContactForm />
                    </div>
                </div>
            </div>
        </div>
    )
}
