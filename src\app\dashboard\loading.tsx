'use client'
import { motion } from 'framer-motion'
import Image from 'next/image'

export default function Loading() {
    return (
        <div className="h-full w-full flex items-center justify-center grow">
            <motion.div
                className="max-w-3xl"
                animate={{
                    scale: [1, 1.1, 1.2, 1.1, 1],
                    opacity: [0.6, 0.8, 1, 0.8, 0.6],
                }}
                transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
            >
                <Image
                    alt="Logo"
                    src="/img/logo.png"
                    width={85.6}
                    height={32}
                    className="w-full h-auto max-w-full"
                />
            </motion.div>
        </div>
    )
}
