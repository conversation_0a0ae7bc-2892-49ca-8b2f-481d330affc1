import {
    RegisterOptions,
    UseFormRegister,
    FieldValues,
    Path,
    FieldErrors,
} from 'react-hook-form'

type InputProps<T extends FieldValues> = {
    label?: string
    placeholder: string
    isDisabled?: boolean
    defaultValue?: string
    registerName: Path<T>
    register: UseFormRegister<T>
    validation?: RegisterOptions<T, Path<T>>
    errors?: FieldErrors<T>
}

export default function TextFieldInput<T extends FieldValues>({
    label,
    placeholder,
    isDisabled,
    defaultValue,
    registerName,
    register,
    validation = {},
    errors,
}: InputProps<T>) {
    return (
        <div className="gap-[.375rem] flex flex-col">
            {label && (
                <label
                    htmlFor={`input${registerName}`}
                    className="font-medium text-sm leading-[1.1875rem]"
                >
                    {label}
                </label>
            )}
            <textarea
                id={`input${registerName}`}
                {...register(registerName, validation)}
                placeholder={placeholder}
                defaultValue={defaultValue}
                disabled={isDisabled}
                className="w-full border border-[#D0D5DD] px-4 py-[.875rem] rounded-lg placeholder:text-[#667085] outline-none appearance-none outline-0 ring-0  resize-none"
                rows={5}
            ></textarea>
            {errors?.[registerName] && (
                <span className="text-sm text-[#475467]">
                    {errors[registerName]?.message as string}
                </span>
            )}
        </div>
    )
}
