import UserInfo from '@/components/dashboard/account/UserInfo'
import DashboardPagesShell from '@/components/dashboard/DashboardPagesShell'
import apiRequest from '@/lib/auth/server/request'

export default async function Account() {
    const request = await apiRequest()
    const response = await request.get(`/api/users/profile`)
    const { data } = response.data
    return (
        <DashboardPagesShell
            innerComponentContent="Edit your profile details and personalize your settings."
            innerComponentHeading="My account"
        >
            <UserInfo fetchedData={data} />
        </DashboardPagesShell>
    )
}
