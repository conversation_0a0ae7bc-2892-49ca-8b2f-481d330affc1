import { getDisplayNairaAmount } from '@/utilities'
import React from 'react'
import AccountExpiryWarning from '../dashboard/fund/AccountExpiryWarning'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import apiRequest from '@/lib/auth/client/request'
import { handleError } from '@/lib/error'
import pThrottle from 'p-throttle'
import { enqueueSnackbar as notify } from 'notistack'

const throttle = pThrottle({
    limit: 10,
    interval: 60000,
})

function TransferCard({
    walletData,
    amountToPay,
    resetter,
}: {
    amountToPay: string
    walletData: WalletAccountData
    resetter: (failed: boolean) => void
}) {
    const router = useRouter()
    const [isSubmittingTrigger, setIsSubmittingTrigger] = useState(false)
    const [trackPayment, setTrackPayment] = useState(false)

    const cPayment = throttle(async (triggered = false) => {
        try {
            setIsSubmittingTrigger(true)
            const response = await apiRequest(false).get(
                `/api/bank/check-transfer?reference=${walletData.reference}`
            )

            const { data } = response.data
            const { status, message } = data

            if (status === 'success') {
                setTrackPayment(false)
                resetter(false)
            }

            if (status === 'pending') {
                if (triggered) {
                    notify(
                        'We are currently confirming your transaction. Please wait..'
                    )
                }
            }

            if (status === 'failed') {
                if (message.includes('pending')) {
                    if (triggered) {
                        notify(
                            'We are currently confirming your transaction. Please wait..'
                        )
                    }
                    return
                }

                notify(
                    'Payment failed. Please do not send money to the old account',
                    {
                        variant: 'error',
                        autoHideDuration: 30000,
                    }
                )
                setTrackPayment(false)
                resetter(true)
            }
        } catch (err: unknown) {
            const error: ApiWithError = handleError(err)
            if (error.message && !error.errors) {
                notify(error.message)
            }
        } finally {
            setIsSubmittingTrigger(false)
        }
    })

    const confirmPayment = React.useCallback(
        async (triggered = false) => {
            try {
                await cPayment(triggered)
            } catch {}
        },
        [cPayment]
    )

    React.useEffect(() => {
        // confirm payment every 5 seconds once there is a reference
        const interval = setInterval(() => {
            if (walletData.reference && trackPayment) {
                confirmPayment()
            }
        }, 10000)
        return () => clearInterval(interval)
    }, [walletData.reference, trackPayment, confirmPayment])

    return (
        <div>
            <h3 className="text-[#667085] text-[1.5rem] md:text-[2rem] text-center leading-normal md:leading-[2.7rem] p-6 border-b border-[#EAECF0]">
                Make Payment
            </h3>
            <div className="p-3 md:p-[2.5rem] flex flex-col gap-2">
                <p className="text-lg mb-3 text-center">
                    Make a bank transfer to the account below to complete the
                    transaction
                </p>
                <div className="flex flex-col gap-[2.75rem] max-w-md mx-auto md:mx-0 w-full">
                    <div className="flex flex-col gap-5 md:gap-3">
                        {[
                            {
                                heading: 'Account Number',
                                value: walletData.account_number,
                                size: 'text-2xl',
                            },
                            {
                                heading: 'Bank Name',
                                value: walletData.bank_name,
                                size: 'text-xl',
                            },
                            {
                                heading: 'Account Name',
                                value: walletData.account_name,
                                size: 'text-lg',
                            },
                            {
                                heading: 'Amount to transfer',
                                value: getDisplayNairaAmount(
                                    parseInt(amountToPay)
                                ),
                                size: '',
                                additionalClassName: 'text-2xl font-semibold',
                                additionalText: ' (charges added)',
                            },
                        ].map((bank) => (
                            <p
                                key={bank.heading}
                                className={`text-center text-[#667085] ${bank.size} ${bank.additionalClassName}`}
                            >
                                {bank.value}
                                <br />
                                {bank.additionalText && (
                                    <span className="text-sm text-[#475467]">
                                        {bank.additionalText}
                                    </span>
                                )}
                            </p>
                        ))}
                    </div>

                    <p className="text-[#475467] text-center -mb-7">
                        {walletData.expires_at ? (
                            <AccountExpiryWarning
                                expires_at={walletData.expires_at}
                            />
                        ) : (
                            <span>
                                Payment made to this account reflect in your
                                wallet balance after a few seconds automatically
                            </span>
                        )}
                    </p>

                    <div className="flex flex-col gap-4">
                        {walletData.expires_at ? (
                            <button
                                disabled={isSubmittingTrigger}
                                type="button"
                                className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem]`}
                                onClick={() => {
                                    setTrackPayment(true)
                                    confirmPayment(true)
                                }}
                            >
                                {isSubmittingTrigger ? (
                                    <svg
                                        className="animate-spin h-5 w-5 mr-3 border-white border-t-2 border-b-2 border-r-2 border-[rgba(255,255,255,0.2)] rounded-full"
                                        viewBox="0 0 24 24"
                                    />
                                ) : null}
                                I have made payment
                            </button>
                        ) : (
                            <button
                                className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem]`}
                                onClick={() => router.push('/dashboard')}
                            >
                                I understand
                            </button>
                        )}
                    </div>
                </div>
                <p className="text-[#475467] text-center">
                    Need help?{' '}
                    <Link href="/contact" className="text-primary font-medium">
                        Contact support
                    </Link>
                </p>
            </div>
        </div>
    )
}

export default TransferCard
