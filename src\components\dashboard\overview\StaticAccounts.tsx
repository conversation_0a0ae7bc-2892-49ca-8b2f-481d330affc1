'use client'
import { Circle<PERSON><PERSON>, Copy } from 'lucide-react'
import { enqueueSnackbar as notify } from 'notistack'
import { useDisclosure } from '@mantine/hooks'
import { Modal } from '@mantine/core'
import Input from '@/components/inputs/Input'
import { useForm } from 'react-hook-form'
import { useSession } from '@/lib/auth/client/session'
import apiRequest from '@/lib/auth/client/request'
import { handleError } from '@/lib/error'
import { revalidateDashboard } from '@/components/action/action'
import { useRouter } from 'next/navigation'

const providerMap = {
    Palmpay: 'PAYMENTPOINT',
}
interface FormData {
    provider: keyof typeof providerMap
    name: string
    phone_number: string
}

const FAST_BANKS = ['palmpay']

const StaticAccounts = ({
    staticAccountsData: walletData,
}: {
    staticAccountsData: WalletData
}) => {
    const bankName = 'Palmpay' // Hardcoded bank name for now
    const [opened, { open, close }] = useDisclosure(false) // Mantine hook for modal control
    const { session } = useSession()
    const {
        register,
        handleSubmit,
        setError,
        reset,
        formState: { errors, isSubmitting },
    } = useForm<FormData>()
    const router = useRouter()

    if (!session) {
        return (
            <div className="px-4 py-6 md:p-[26px] flex flex-col gap-6 bg-white rounded-xl">
                <p className="text-center text-gray-500">
                    Unable to fetch your static accounts...
                </p>
            </div>
        )
    }

    const onSubmit = async (data: FormData) => {
        const { provider, name, phone_number } = data

        const providerName = providerMap[provider]
        try {
            await apiRequest().post('/api/bank/virtual-account', {
                provider: providerName,
                name,
                phone_number,
            })
            notify('Account number created successfully', {
                variant: 'success',
            })
            await revalidateDashboard()
            reset()
            router.refresh()
            close()
        } catch (err) {
            const error: ApiWithFormError<FormData> = handleError(err)
            if (error.errors) {
                error.errors.forEach((e) => {
                    setError(e.path, {
                        message: e.message,
                    })
                })
            }
            if (error.message && !error.errors) {
                notify(error.message, { variant: 'error' })
            }
        }
    }
    return (
        <>
            <div className="px-4 py-6 md:p-[26px] flex flex-col gap-6 bg-white rounded-xl">
                <div className="gap-1 flex flex-col">
                    <h3 className="text-[1.125rem] font-medium text-[#182230]">
                        Static account numbers
                    </h3>
                    <p className="text-[#667085] text-sm">
                        Fund your wallet easily with any of the options below
                    </p>

                    <div className="pt-6 border-t border-[F5F5F5]">
                        {walletData.static_accounts.length === 0 && (
                            <p className="text-[.9rem] text-[#475467] text-center p-8">
                                No static account numbers yet...
                            </p>
                        )}
                        <div className="grid grid-cols-1 md:grid-cols-2  gap-6">
                            {walletData.static_accounts.map((account) => (
                                <div
                                    key={account.account_number}
                                    className="min-w-md space-y-2 p-4 rounded-xl border"
                                >
                                    <p className="text-[.9rem] text-[#475467] font-medium flex items-center justify-between">
                                        <span>{account.bank_name}</span>

                                        {FAST_BANKS.includes(
                                            account.bank_name.toLocaleLowerCase()
                                        ) && (
                                            <span className="rounded-2xl bg-blue-100 px-3 p-1 text-xs text-blue-600 animate-pulse">
                                                Fastest
                                            </span>
                                        )}
                                    </p>
                                    <p className="text-sm">
                                        {account.account_name}
                                    </p>
                                    <div className="flex items-center space-x-2">
                                        <p className="text-[1.5rem] font-bold">
                                            {account.account_number}
                                        </p>
                                        <Copy
                                            size={18}
                                            className="text-gray-600 cursor-pointer"
                                            onClick={() => {
                                                navigator.clipboard.writeText(
                                                    account.account_number
                                                )
                                                notify('Account number copied')
                                            }}
                                        />
                                    </div>
                                </div>
                            ))}
                            <div className="flex items-center justify-center md:col-span-2">
                                <button
                                    onClick={open}
                                    type="button"
                                    className="flex items-center py-8 px-3 gap-[.8rem] text-[#0C111D]"
                                >
                                    <CirclePlus
                                        strokeWidth={1}
                                        className="text-primary w-10 h-10"
                                    />
                                    <span className="text-primary font-bold">
                                        Generate New
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* New account modal  */}

            <Modal
                opened={opened}
                onClose={close}
                size="md"
                withCloseButton={false}
                centered
            >
                <form
                    onSubmit={handleSubmit(onSubmit)}
                    className="flex flex-col gap-4 p-4 "
                >
                    <p className="mb-3 font-semibold">
                        Fill the form below to generate new bank account
                    </p>
                    {/* hardcoding the bank name as Palmpay for now */}
                    <Input<FormData>
                        label="Bank name"
                        isDisabled={true}
                        placeholder="Bank Name"
                        name="provider"
                        type="text"
                        required
                        registerName="provider"
                        register={register}
                        defaultValue={bankName}
                        errors={errors}
                    />

                    <Input<FormData>
                        label="Account name"
                        placeholder="Enter your full name"
                        name="name"
                        type="text"
                        required
                        registerName="name"
                        register={register}
                        defaultValue={session?.user?.full_name || ''}
                        errors={errors}
                    />
                    <Input<FormData>
                        label="Phone number"
                        placeholder="Enter your phone number"
                        name="phone_number"
                        type="tel"
                        required
                        registerName="phone_number"
                        register={register}
                        errors={errors}
                        validation={{
                            required: {
                                value: true,
                                message: `Phone number is required`,
                            },
                            pattern: {
                                value: /^(?:\+?234|0)[789][01]\d{8}$/,
                                message:
                                    'Please enter a valid Nigerian phone number',
                            },
                        }}
                        defaultValue={session?.user?.phonenumber || ''}
                    />
                    {isSubmitting && (
                        <p className="text-sm text-gray-500 text-center">
                            Please wait, this may take a few seconds...
                        </p>
                    )}
                    <button
                        disabled={isSubmitting}
                        type="submit"
                        className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] disabled:opacity-50`}
                    >
                        {isSubmitting ? (
                            <svg
                                className="animate-spin h-5 w-5 mr-3 border-white border-t-2 border-b-2 border-r-2 border-[rgba(255,255,255,0.2)] rounded-full"
                                viewBox="0 0 24 24"
                            />
                        ) : null}
                        Create {bankName} account
                    </button>
                </form>
            </Modal>
        </>
    )
}
export default StaticAccounts
