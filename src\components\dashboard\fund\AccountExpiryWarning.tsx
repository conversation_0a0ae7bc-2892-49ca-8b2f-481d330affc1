import React from 'react'
import Countdown from 'react-countdown'
interface RendererProps {
    days: number
    hours: number
    minutes: number
    seconds: number
    completed: boolean
}

const AccountExpiryWarning = ({ expires_at }: { expires_at: string }) => {
    const expiryDate = new Date(expires_at)
    const now = new Date()
    const isExpired = expiryDate <= now

    // Renderer for countdown
    const renderer = ({
        days,
        hours,
        minutes,
        seconds,
        completed,
    }: RendererProps) => {
        if (completed) {
            return (
                <span className="text-red-500 font-bold">
                    Account Expired. Do not make any transfer to this account
                </span>
            )
        } else {
            // Format time display
            const timeString = `${days > 0 ? `${days}d ` : ''}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`

            return (
                <span className="text-red-500 font-semibold">
                    Account will expire in {timeString}
                </span>
            )
        }
    }

    // If already expired, show expired message directly
    if (isExpired) {
        return (
            <span className="text-red-500 font-bold">
                Account Expired. Do not make any transfer to this account
            </span>
        )
    }

    return <Countdown date={expiryDate} renderer={renderer} />
}

export default AccountExpiryWarning
