'use client'
import Image from 'next/image'
import { useEffect, useState, useRef } from 'react'
import { getDisplayNairaAmount } from '@/utilities'

export const WalletBalance = ({ balance = 0 }: { balance?: number }) => {
    const [show, setShow] = useState(false)
    const initialTimeoutRef = useRef<NodeJS.Timeout | null>(null)
    const returnTimeoutRef = useRef<NodeJS.Timeout | null>(null)
    useEffect(() => {
        const container = document.getElementById('dash')
        if (container) {
            initialTimeoutRef.current = setTimeout(() => {
                container.scrollTo({
                    left: container.scrollWidth - container.clientWidth,
                    behavior: 'smooth',
                })

                returnTimeoutRef.current = setTimeout(() => {
                    container.scrollTo({
                        left: 0,
                        behavior: 'smooth',
                    })
                }, 1200)
            }, 1200)
        }

        return () => {
            if (initialTimeoutRef.current)
                clearTimeout(initialTimeoutRef.current)
            if (returnTimeoutRef.current) clearTimeout(returnTimeoutRef.current)
        }
    }, [])
    return (
        <div className="flex flex-col gap-1">
            <div className="flex gap-3 items-center text-[1.25rem] text-[#475467]">
                <p className="md:text-[1.25rem] text-white">Wallet balance</p>
                <div className="w-6">
                    <Image
                        src={show ? '/icons/hide.svg' : '/icons/eyes.svg'}
                        width={24}
                        height={24}
                        alt="hide/show"
                        role="button"
                        className="cursor-pointer"
                        onClick={() => setShow((value) => !value)}
                    />
                </div>
            </div>
            <p className="text-white text-[1.875rem] md:text-[2.25rem] font-semibold md:min-w-[13rem] ">
                {show ? `${getDisplayNairaAmount(balance)}` : '----'}
            </p>
        </div>
    )
}
