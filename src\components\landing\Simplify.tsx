import Image from 'next/image'
import Link from 'next/link'
import TryItNow from './TryItNow'

export default function Simplify() {
    return (
        <div className="px-4 max-w-6xl mx-auto md:mb-[4rem]">
            <div className="flex flex-col md:flex-row gap-[3.25rem] md:gap-5 py-[2rem]">
                <div className="md:basis-1/2 text-center md:text-left gap-4 md:gap-[1.4375rem] flex flex-col items-center md:items-start relative justify-center">
                    <div className="w-[18.75rem] h-[18.75rem] rounded-full absolute bottom-1/2 md:bottom-[89%] left-1/2 translate-y-1/2 -translate-x-1/2 md:translate-x-0 md:translate-y-[70%] bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-[#C4FFCF4D] to-[#FFFFFF4D] -z-10"></div>
                    <h1
                        className={`font-brico max-w-[39.8125rem] font-extrabold text-[2.75rem] leading-[3.25rem] md:text-[4.6875rem] md:leading-[5rem] text-[#0C111D] z-[8]`}
                    >
                        <span className="relative">
                            <span>Simplify</span>
                            <Image
                                src="/icons/greenCrayon.svg"
                                alt="simple crayon"
                                height={42}
                                width={312}
                                className="absolute -translate-x-1/2 -translate-y-1/2 top-1/2 left-[62%] z-[-1]"
                            />
                        </span>
                        <span className="ml-3">Your Bill Payments</span>
                    </h1>
                    <p className="leading-[1.35rem] text-[#344054] md:text-[1.5rem] md:leading-[2rem] z-[6]">
                        Top up your airtime, pay utility bills, renew TV
                        subscriptions, and more with just a few clicks.
                    </p>
                    <div className="flex items-center gap-3 md:gap-2 md:flex-col md:items-start">
                        {[
                            {
                                color: '#004EEC',
                                text: 'Data top-up and airtime',
                                new: false,
                            },
                            {
                                color: '#01DD3F',
                                text: 'Electricity',
                                new: false,
                            },
                            { color: '#815CEB', text: 'Cable TV', new: false },
                        ].map((service) => (
                            <div
                                key={service.text}
                                className="flex items-center gap-2"
                            >
                                <span
                                    className="w-[.375rem] h-[.375rem] rounded-full"
                                    style={{ backgroundColor: service.color }}
                                ></span>{' '}
                                <p className="text-[#475467] text-[.75rem] leading-[1.0125rem] md:leading-[1.35rem]">
                                    {service.text}
                                </p>{' '}
                                {service.new && (
                                    <span className="py-[.1875rem] px-[.375rem] rounded border-[.0313rem] border-primary text-primary text-[.625rem] leading-[.7562rem] md:leading-[.9075rem] md:text-[.75rem] font-inter">
                                        New
                                    </span>
                                )}
                            </div>
                        ))}
                    </div>
                    <div className="flex items-center gap-6">
                        {[
                            {
                                img: '/img/googleStore.svg',
                                href: 'https://play.google.com/store/apps/details?id=com.ciptopup',
                            },
                            {
                                img: '/img/appleStore.svg',
                                href: 'https://apps.apple.com/ng/app/ciptopup-app/id1600469000',
                            },
                        ].map((link) => (
                            <Link
                                target="_blank"
                                href={link.href}
                                key={link.img}
                            >
                                <Image
                                    src={link.img}
                                    alt=""
                                    height={48}
                                    width={135}
                                    className="max-w-full w-auto h-[2.75rem] md:h-[3rem]"
                                />
                            </Link>
                        ))}
                    </div>
                    <p className="text-xl text-[#344054] flex items-center gap-2">
                        <span>How it works?</span>
                        <TryItNow />
                    </p>
                </div>
                <div className="md:basis-1/2 flex justify-center items-center overflow-hidden">
                    <Image
                        alt="preview"
                        src="/img/landingTop.svg"
                        height={633}
                        width={556}
                        priority
                        className="w-full h-auto object-cover"
                    />
                </div>
            </div>
        </div>
    )
}
