import Image from 'next/image'
import Link from 'next/link'

export default function HowItWorks() {
    return (
        <div className="rounded-3xl md:p-[2.125rem] flex flex-col bg-white p-6 gap-[2.75rem] items-center">
            <div className="flex flex-col gap-6 items-center w-full">
                <div className="flex items-center w-full justify-between pb-3 border-b border-[#F2F4F7]">
                    <div className="flex flex-col gap-1 text-center md:text-left">
                        <h2 className="font-brico text-[#182230] text-[1.25rem] font-semibold">
                            How it works
                        </h2>
                        <p className="text-[#475467]">
                            Earn a bonus for every successful referral!
                        </p>
                    </div>
                    <Link
                        href="/dashboard/referral/terms"
                        className="md:flex text-sm font-medium hidden items-center gap-1 border border-primary text-primary rounded-lg p-[.625rem]"
                    >
                        <span>Referral Terms and Condition</span>
                        <div className="w-6">
                            <Image
                                height={24}
                                width={24}
                                alt=""
                                src="/icons/blueArrow.svg"
                                className="w-full h-auto"
                            />
                        </div>
                    </Link>
                </div>
                {/*  */}
                <div className="flex flex-col md:flex-row gap-8 justify-between items-center *:max-w-[15.625rem]">
                    {[
                        {
                            icon: '/icons/share.svg',
                            heading: 'Share Your Link',
                            content:
                                'Copy your unique referral link and share it with friends',
                        },
                        {
                            icon: '/icons/people.svg',
                            heading: 'Friends Sign Up & Transact',
                            content:
                                'When they sign up and make their first purchase, you earn a reward',
                        },
                        {
                            icon: '/icons/gift.svg',
                            heading: 'Get Rewarded Instantly',
                            content:
                                'Your bonus is added to your wallet—withdraw or use it anytime',
                        },
                    ].map((how) => (
                        <div
                            key={how.heading}
                            className="gap-3 flex flex-col items-center"
                        >
                            <div className="w-12 p-3 rounded-full bg-cipLight">
                                <Image
                                    src={how.icon}
                                    height={24}
                                    width={24}
                                    alt={how.heading}
                                    className="w-full h-auto"
                                />
                            </div>
                            <div className="flex flex-col gap-1 text-center">
                                <h4 className="text-[#182230] font-medium">
                                    {how.heading}
                                </h4>
                                <p className="text-[#475467] text-sm">
                                    {how.content}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            <Link
                href="/dashboard/referral/terms"
                className=" md:hidden text-sm font-medium flex items-center gap-1 border border-primary text-primary rounded-lg p-[.625rem]"
            >
                <span>Referral Terms and Condition</span>
                <div className="w-6">
                    <Image
                        height={24}
                        width={24}
                        alt=""
                        src="/icons/blueArrow.svg"
                        className="w-full h-auto"
                    />
                </div>
            </Link>
        </div>
    )
}
