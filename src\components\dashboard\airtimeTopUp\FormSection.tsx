'use client'
import { useEffect, useState } from 'react'
import { FormData } from './type'
import ActionForm from './ActionForm'
import ConfirmTransaction from '../ConfirmTransaction'
import { handleError } from '@/lib/error'
import { enqueueSnackbar as notify } from 'notistack'
import apiRequest from '@/lib/auth/client/request'
import { useDisclosure } from '@mantine/hooks'
import { Modal } from '@mantine/core'
import { CheckCircle } from 'lucide-react'
import { revalidateTransactions } from '@/components/action/action'

export default function FormSection() {
    const [hasClickedButton, setHasClickedButton] = useState(false)
    const [clearForm, setClearForm] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [opened, { open, close }] = useDisclosure(false)

    // const cashDiscount = 3

    // const calculateCashback = (amount: number) => {
    //     const upper = 100 - cashDiscount
    //     const perc = (upper / 100) * amount
    //     return Math.ceil(perc)
    // }

    useEffect(() => {
        const container = document.getElementById('scrollRel')
        if (container) {
            container.scrollTo({
                top: 0,
                behavior: 'smooth',
            })
        }
    }, [hasClickedButton])

    const [formData, setFormData] = useState<FormData>({
        network: '',
        phone: '',
        amount: '',
    })

    const runRequest = async () => {
        setIsSubmitting(true)
        const form = {
            network: formData.network,
            phone: formData.phone,
            amount: parseInt(formData.amount),
        }
        try {
            await apiRequest().post(`/api/airtime`, form)
            // when successfull will clear form and reset it all
            setHasClickedButton(false)
            setClearForm((value) => !value) //will clear the form in as much as it value change whether true or false
            open()
        } catch (err: unknown) {
            const error: ApiWithError = handleError(err)
            if (error.message) {
                notify(error.message, { variant: 'error' })
            }
        } finally {
            setIsSubmitting(false)
            revalidateTransactions()
        }
    }

    return (
        <div className="grow flex flex-col lg:flex-row">
            <Modal
                opened={opened}
                onClose={close}
                withCloseButton={false}
                centered
            >
                <div className="w-full max-w-md bg-white overflow-hidden p-6">
                    <div className="flex flex-col items-center text-center">
                        <div className="mb-4 bg-green-100 p-3 rounded-full">
                            <CheckCircle className="h-12 w-12 text-green-600" />
                        </div>

                        <h1 className="text-2xl font-bold text-gray-800 mb-2">
                            Payment Successful!
                        </h1>
                        <p className="text-gray-600 mb-6">
                            Thank you for your payment. Your transaction has
                            been completed successfully.
                        </p>
                    </div>
                </div>
            </Modal>
            <div className="lg:pr-[3.125rem] lg:border-r lg:border-[#F5F5F5] lg:basis-1/2  lg:h-full">
                <div
                    className={`${hasClickedButton && 'hidden lg:block'} relative h-full`}
                >
                    <div
                        className={`${hasClickedButton ? 'block' : 'hidden'} absolute top-0 bottom-0 left-0 right-0 bg-[#E8E8E880] backdrop-blur-[.625rem] z-[2]`}
                    ></div>
                    {/* Form goes here */}
                    <ActionForm
                        setFormData={setFormData}
                        setHasClickedButton={setHasClickedButton}
                        clearForm={clearForm}
                    />
                </div>
            </div>
            <div className="lg:pl-[3.125rem] lg:basis-1/2">
                {hasClickedButton && (
                    <ConfirmTransaction
                        runRequest={runRequest}
                        goBack={() => {
                            setHasClickedButton(false)
                        }}
                        isSubmitting={isSubmitting}
                        details={[
                            { detail: 'Network', value: formData.network },
                            {
                                detail: 'Phone Number',
                                value: formData.phone,
                            },
                            {
                                detail: 'Amount',
                                value: `₦${formData.amount}`,
                            },
                            // { detail: 'Cashback', value: '3%' },
                            {
                                detail: 'Amount to pay',
                                value: `₦${formData.amount}`,
                            },
                        ]}
                    />
                )}
            </div>
        </div>
    )
}
