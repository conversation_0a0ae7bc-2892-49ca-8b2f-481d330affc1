'use client'
import Image from 'next/image'
import { RegisterOptions, useForm } from 'react-hook-form'

import Link from 'next/link'
import apiRequest from '@/lib/auth/client/request'
import { enqueueSnackbar as notify } from 'notistack'
import { GOOGLE_AUTH_URL } from '@/lib/common'
import { handleError } from '@/lib/error'
import { setSession } from '@/lib/auth/client/session'
import { useRouter } from 'next/navigation'
import { Suspense, useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import Input from '../inputs/Input'
import { Eye, EyeOff } from 'lucide-react'

interface FormData {
    name: string
    phone: string
    email: string
    password: string
}

const inputArray: {
    label: string
    name: keyof FormData
    type: string
    placeholder: string
    validation: RegisterOptions<FormData, keyof FormData>
}[] = [
    {
        label: 'Email or Phone number*',
        name: 'email',
        type: 'text',
        placeholder: 'Enter your email address or phone number',
        validation: {
            required: {
                value: true,
                message: `Email address or phone number is required`,
            },
            pattern: {
                value: /^(?:\+?234|0)[789][01]\d{8}$|^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Enter a valid email or Nigerian phone number',
            },
        },
    },
    {
        label: 'Password*',
        name: 'password',
        type: 'password',
        placeholder: 'Enter your password',
        validation: {
            required: {
                value: true,
                message: `Password is required`,
            },
        },
    },
]

type CallBackError = 'google_auth_failed' | 'session_expired'

const callback_error_message = {
    google_auth_failed: 'Unable to authenticate with Google. Please try again',
    session_expired: 'Your session has expired, please sign in again',
}

function SignInFormMain() {
    const {
        register,
        handleSubmit,
        setError,
        formState: { errors },
    } = useForm<FormData>()
    const [googleFormLoading, setGoogleFormLoading] = useState(false)
    const searchParam = useSearchParams()
    const router = useRouter()
    const [loading, setLoading] = useState(false)
    const [showPassword, setShowPassword] = useState(false)

    const onSubmit = async (data: FormData) => {
        try {
            setLoading(true)
            const request = await apiRequest(false).post(
                '/api/auth/login',
                data
            )
            const { data: responseData } = request.data
            const sessionData = {
                user: {
                    id: responseData.user.id,
                    email: responseData.user.email,
                    full_name: responseData.user.full_name,
                    phonenumber: responseData.user?.phonenumber,
                },
                token: responseData.token,
            }
            setSession(sessionData)
            router.push('/dashboard')
        } catch (err: unknown) {
            const error: ApiWithFormError<FormData> = handleError(err)
            if (error.errors) {
                error.errors.forEach((e) => {
                    setError(e.path, {
                        message: e.message,
                    })
                })
            }
            if (error.message && !error.errors) {
                if (error.message.includes('email verification')) {
                    return router.push(`/verify-email?email=${data.email}`)
                }
                notify(error.message)
            }
            setLoading(false)
        }
    }

    const signInWithGoogle = () => {
        setGoogleFormLoading(true)
        return router.push(GOOGLE_AUTH_URL)
    }

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword)
    }

    useEffect(() => {
        if (searchParam.get('callback_error')) {
            notify(
                callback_error_message[
                    searchParam.get('callback_error') as CallBackError
                ],
                {
                    variant: 'error',
                }
            )
        }
        setGoogleFormLoading(false)
    }, [searchParam])

    return (
        <form
            method="POST"
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-[2.75rem]"
        >
            <div className="flex flex-col gap-5">
                {/* Email input */}
                <Input<FormData>
                    key="email"
                    type="text"
                    label="Email or Phone number*"
                    registerName="email"
                    placeholder="Enter your email address or phone number"
                    register={register}
                    errors={errors}
                    validation={inputArray[0].validation}
                />

                {/* Password input with eye icon */}
                <div className="relative">
                    <Input<FormData>
                        key="password"
                        type={showPassword ? 'text' : 'password'}
                        label="Password*"
                        registerName="password"
                        placeholder="Enter your password"
                        register={register}
                        errors={errors}
                        validation={inputArray[1].validation}
                    />
                    <button
                        type="button"
                        onClick={togglePasswordVisibility}
                        className="absolute right-4 top-1/2 transform text-gray-500 hover:text-gray-700 focus:outline-none"
                        aria-label={
                            showPassword ? 'Hide password' : 'Show password'
                        }
                    >
                        {showPassword ? (
                            <EyeOff size={20} />
                        ) : (
                            <Eye size={20} />
                        )}
                    </button>
                </div>

                <div className="w-full flex justify-end -translate-y-[.625rem]">
                    <Link href="/forgot" className="text-sm text-primary">
                        Forgot password
                    </Link>
                </div>
            </div>

            <div className="flex flex-col gap-4">
                <button
                    disabled={loading}
                    type="submit"
                    className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] disabled:opacity-50`}
                >
                    {loading ? (
                        <svg
                            className="animate-spin h-5 w-5 mr-3 border-white border-t-2 border-b-2 border-r-2 border-[rgba(255,255,255,0.2)] rounded-full"
                            viewBox="0 0 24 24"
                        />
                    ) : null}
                    Sign in
                </button>
                <button
                    disabled={googleFormLoading}
                    type="button"
                    onClick={signInWithGoogle}
                    className={`font-vietnam py-3 px-4 rounded-lg bg-white text-[#344054] border border-[#D0D5DD] w-full flex items-center justify-center leading-[1.5rem] gap-3`}
                >
                    {googleFormLoading ? (
                        <svg
                            className="animate-spin h-5 w-5 mr-3 border-[#344054] border-t-2 border-b-2 border-r-2 rounded-full text-black"
                            viewBox="0 0 24 24"
                        />
                    ) : (
                        <Image
                            alt="google"
                            src="/icons/googleIcon.svg"
                            width={24}
                            height={24}
                            className="w-6 aspect-square"
                        />
                    )}
                    Sign in with Google
                </button>
            </div>
        </form>
    )
}

export default function SignInForm() {
    return (
        <Suspense>
            <SignInFormMain />
        </Suspense>
    )
}
