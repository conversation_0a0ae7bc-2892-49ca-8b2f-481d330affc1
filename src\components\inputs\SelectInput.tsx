import Image from 'next/image'
import { InputHTMLAttributes } from 'react'
import {
    RegisterOptions,
    UseFormRegister,
    FieldValues,
    Path,
    FieldErrors,
} from 'react-hook-form'

type InputProps<T extends FieldValues> = {
    label?: string
    placeholder?: string
    disabled?: boolean
    inputOptions: { option: string; value?: string | number }[] | null
    registerName: Path<T>
    register: UseFormRegister<T>
    validation?: RegisterOptions<T, Path<T>>
    errors?: FieldErrors<T>
} & Omit<
    InputHTMLAttributes<HTMLSelectElement>,
    'type' | 'placeholder' | 'defaultValue' | 'disabled' | 'id'
>

export default function SelectInput<T extends FieldValues>({
    label,
    placeholder,
    disabled,
    inputOptions,
    registerName,
    register,
    validation = {},
    errors,
    ...props
}: InputProps<T>) {
    return (
        <div className="gap-[.375rem] flex flex-col">
            {label && (
                <label
                    htmlFor={`input${registerName}`}
                    className="font-medium text-sm leading-[1.1875rem]"
                >
                    {label}
                </label>
            )}
            <div className="relative flex items-center w-full border border-[#D0D5DD] rounded-lg overflow-hidden pr-[.875rem] gap-2 cursor-pointer">
                <select
                    defaultValue=""
                    id={`input${registerName}`}
                    disabled={disabled}
                    {...props}
                    {...register(registerName, validation)}
                    className="px-4 py-[.875rem] placeholder:text-[#667085] outline-none appearance-none outline-0 ring-0  pr-0 w-full bg-transparent z-[1] cursor-pointer"
                >
                    <option value="" disabled>
                        {placeholder || label}
                    </option>
                    {inputOptions &&
                        inputOptions.map((option, index) => (
                            <option
                                key={`${option.value}${index}`}
                                value={option.value || option.option}
                            >
                                {option.option}
                            </option>
                        ))}
                </select>
                <div className="w-5 absolute top-1/2 -translate-y-1/2 right-[.875rem]">
                    <Image
                        height={20}
                        width={20}
                        alt="chevron"
                        src="/icons/chevron.svg"
                        className="w-full h-auto"
                    />
                </div>
            </div>

            {errors?.[registerName] && (
                <span className="text-sm text-[#475467]">
                    {errors[registerName]?.message as string}
                </span>
            )}
        </div>
    )
}
