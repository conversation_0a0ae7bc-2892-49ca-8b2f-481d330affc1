'use client'
import { useForm } from 'react-hook-form'
import React, { SetStateAction, useState } from 'react'
import Input from '@/components/inputs/Input'
import { FormData } from './type'
import SelectInput from '@/components/inputs/SelectInput'
import InsertInput from '@/components/inputs/InsertInput'
import { enqueueSnackbar as notify } from 'notistack'
import apiRequest from '@/lib/auth/client/request'
import { handleError } from '@/lib/error'
import ToastWrapper from '@/components/ToastWrapper'

const options = [
    {
        id: 'ikeja-electric',
        name: 'IKEDC',
    },
    {
        id: 'eko-electric',
        name: 'EKEDC',
    },
    {
        id: 'portharcourt-electric',
        name: 'PHED',
    },
    {
        id: 'kaduna-electric',
        name: 'Kaduna Electric',
    },
    {
        id: 'jos-electric',
        name: 'J<PERSON>',
    },
    {
        id: 'ibadan-electric',
        name: 'IBEDC',
    },
    {
        id: 'kano-electric',
        name: '<PERSON><PERSON><PERSON>',
    },
    {
        id: 'enugu-electric',
        name: 'EEDC',
    },
    {
        id: 'abuja-electric',
        name: 'AEDC',
    },
    {
        id: 'benin-electric',
        name: 'BEDC',
    },
    {
        id: 'aba-electric',
        name: 'ABA',
    },
    {
        id: 'yola-electric',
        name: 'YEDC',
    },
]

export default function ActionForm({
    setFormData,
    setHasClickedButton,
    username,
    setUsername,
}: {
    setFormData: React.Dispatch<SetStateAction<FormData>>
    setHasClickedButton: React.Dispatch<SetStateAction<boolean>>
    username: string
    setUsername: React.Dispatch<SetStateAction<string>>
}) {
    const [isLoading, setIsLoading] = useState(false)
    const [minAmount, setMinAmount] = useState(500)

    const {
        register,
        handleSubmit,
        getValues,
        setValue,
        watch,
        setError,
        formState: { errors },
        trigger,
    } = useForm<FormData>()

    const meterNumber = watch('meterNumber')
    const meterType = watch('paymentType')
    const biller = watch('biller')

    const onSubmit = (data: FormData) => {
        setFormData(data)
        setHasClickedButton(true)
    }

    //validate meter
    const validateMeter = async () => {
        setUsername('')
        const form = {
            meter_number: meterNumber,
            provider_id: biller,
            meter_type: meterType,
        }
        const isValid = await trigger(['meterNumber', 'biller', 'paymentType'])
        if (!isValid) trigger(['meterNumber', 'biller', 'paymentType'])

        if (isValid) {
            try {
                setIsLoading(true)
                const response = await apiRequest().post(
                    `/api/electricity/validate`,
                    form
                )
                const { data } = response.data
                setUsername(data.name)
                setMinAmount(
                    Number(data.min_purchase) > 500
                        ? Number(data.min_purchase)
                        : 500
                )
            } catch (err: unknown) {
                const error: ApiWithFormError<FormData> = handleError(err)
                if (error.errors) {
                    error.errors.forEach((e) => {
                        setError(e.path, {
                            message: e.message,
                        })
                    })
                }
                if (error.message && !error.errors) {
                    notify(error.message, { variant: 'error' })
                }
            } finally {
                setIsLoading(false)
            }
        }
    }

    const disableValidate = !(
        meterNumber?.length > 8 &&
        !isLoading &&
        Boolean(biller) &&
        Boolean(meterType)
    )
    return (
        <form
            method="POST"
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-[2.75rem] max-w-md mx-auto md:mx-0"
        >
            <div className="flex flex-col gap-5 md:gap-7">
                <ToastWrapper />
                <SelectInput<FormData>
                    label={'Biller Name'}
                    inputOptions={options.map((option) => ({
                        option: option.name,
                        value: option.id,
                    }))}
                    registerName={'biller'}
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: `Biller name is required`,
                        },
                    }}
                    onInput={() => setUsername('')}
                />
                <SelectInput<FormData>
                    label={'Choose Payment Type'}
                    inputOptions={[
                        { option: 'Prepaid', value: 'prepaid' },
                        { option: 'Postpaid', value: 'postpaid' },
                    ]}
                    registerName={'paymentType'}
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: `Payment type is required`,
                        },
                    }}
                    onInput={() => setUsername('')}
                />
                <Input<FormData>
                    type={'number'}
                    label={'Meter Number'}
                    registerName={'meterNumber'}
                    placeholder={'Enter meter number'}
                    defaultValue=""
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: `Meter number is required`,
                        },
                        minLength: {
                            value: 8,
                            message: 'Input a valid meter number',
                        },
                    }}
                    onInput={() => setUsername('')}
                />

                {username && (
                    <>
                        <Input<FormData>
                            type={'text'}
                            label={'Name on Meter'}
                            registerName={'username'}
                            placeholder={'username'}
                            isDisabled={true}
                            defaultValue={username}
                            register={register}
                            errors={errors}
                            validation={{
                                required: {
                                    value: false,
                                    message: `Username is required`,
                                },
                            }}
                        />

                        <Input<FormData>
                            type={'tel'}
                            label={'Phone Number'}
                            registerName={'phone'}
                            placeholder={'Enter your phone number'}
                            register={register}
                            errors={errors}
                            validation={{
                                required: {
                                    value: true,
                                    message: `Phone number is required`,
                                },
                                pattern: {
                                    value: /^(?:\+?234|0)[789][01]\d{8}$/,
                                    message:
                                        'Please enter a valid Nigerian phone number',
                                },
                            }}
                        />
                        <InsertInput<FormData>
                            trigger={trigger}
                            label={'Amount'}
                            inputOptions={[
                                { option: '₦500', value: 500 },
                                { option: '₦1000', value: 1000 },
                                { option: '₦2000', value: 2000 },
                                { option: '₦5000', value: 5000 },
                                { option: '₦10000', value: 10000 },
                            ]}
                            placeholder="Enter amount"
                            getValues={getValues}
                            watch={watch}
                            setValue={setValue}
                            registerName={'amount'}
                            register={register}
                            errors={errors}
                            validation={{
                                required: {
                                    value: true,
                                    message: `Amount is required`,
                                },
                                min: {
                                    value: minAmount,
                                    message: `You can't purchase electricity less than ₦${minAmount} for this meter`,
                                },
                            }}
                        />
                    </>
                )}
            </div>

            <div className="flex flex-col gap-4">
                {username ? (
                    <button
                        disabled={username ? false : true}
                        type="submit"
                        className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] ${!username && 'opacity-20 hover:opacity-20'}`}
                    >
                        Buy now
                    </button>
                ) : (
                    <button
                        type="button"
                        onClick={validateMeter}
                        disabled={disableValidate}
                        className="font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] disabled:opacity-60 hover:disabled:opacity-60 disabled:cursor-not-allowed"
                    >
                        {isLoading ? (
                            <span>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className="lucide lucide-loader-circle animate-spin"
                                >
                                    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                </svg>
                            </span>
                        ) : (
                            'Validate Meter Number'
                        )}
                    </button>
                )}
            </div>
        </form>
    )
}
