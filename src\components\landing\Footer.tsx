import Image from 'next/image'
import Link from 'next/link'

export default function Footer() {
    return (
        <div className="relative flex flex-col items-center mt-[8.125rem] md:mt-[12.25rem]">
            <Image
                alt="logo"
                src="/img/fadedLogo.svg"
                width={1138}
                height={240}
                className="max-w-5xl w-full h-auto absolute top-0 z-[-1] -translate-y-3/4 px-4"
            />
            <div className="border-y border-[#E3E3E3] z-[1] bg-white w-full">
                <div className="max-w-6xl mx-auto items-start flex flex-col px-4 pt-6 pb-10 md:py-6 md:pb-6 gap-[2.75rem] md:gap-8">
                    <div className="grid gap-[2.75rem] grid-cols-1 md:grid-cols-3 justify-between  ">
                        <div className="flex flex-col gap-3" data-aos="fade-up">
                            <Link href="/" className="w-[5rem]">
                                <Image
                                    alt="Logo"
                                    src="/img/logo.png"
                                    width={85.6}
                                    height={32}
                                    className="w-full h-auto"
                                />
                            </Link>
                            <p className="text-[1.25rem] leading-[1.6875rem] text-[#475467]">
                                Your trusted platform for seamless airtime,
                                data, and utility payments.
                            </p>
                        </div>
                        {/*  */}
                        {/* <div
                            className="flex flex-col gap-[15px]"
                            data-aos="fade-up"
                        >
                            <Link
                                href=""
                                className="text-[1.25rem] leading-[1.6875rem] text-[#0C111D]"
                            >
                                Privacy policy
                            </Link>
                            <Link
                                href=""
                                className="text-[1.25rem] leading-[1.6875rem] text-[#0C111D]"
                            >
                                Terms and Condition
                            </Link>
                        </div> */}
                        {/*  */}
                        <div
                            className="flex flex-col gap-5 md:items-center"
                            data-aos="fade-up"
                        >
                            <p className="text-[1.25rem] leading-[1.6875rem] text-[#0C111D]">
                                Social links
                            </p>
                            <div className="flex items-center gap-[1.125rem]">
                                {[
                                    { icons: '/icons/facebook.svg', href: '' },
                                    { icons: '/icons/twitter.svg', href: '' },
                                    { icons: '/icons/instagram.svg', href: '' },
                                ].map((link) => (
                                    <a
                                        key={link.icons}
                                        href={link.href}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                    >
                                        <Image
                                            src={link.icons}
                                            alt=""
                                            width={24}
                                            height={24}
                                            className="w-6 aspect-square"
                                        />
                                    </a>
                                ))}
                            </div>
                        </div>
                        {/*  */}
                        <div
                            className="flex flex-col gap-5 md:items-end"
                            data-aos="fade-up"
                        >
                            <Link
                                href=""
                                className="text-[1.25rem] leading-[1.6875rem] text-[#0C111D]"
                            >
                                Get mobile app
                            </Link>
                            <div className="flex flex-col gap-3">
                                {[
                                    {
                                        img: '/img/googleStore.svg',
                                        href: 'https://play.google.com/store/apps/details?id=com.ciptopup',
                                    },
                                    {
                                        img: '/img/appleStore.svg',
                                        href: 'https://apps.apple.com/ng/app/ciptopup-app/id1600469000',
                                    },
                                ].map((link) => (
                                    <Link
                                        target="_blank"
                                        href={link.href}
                                        key={link.img}
                                    >
                                        <Image
                                            src={link.img}
                                            alt=""
                                            height={48}
                                            width={135}
                                            className="max-w-full h-auto w-[9.375rem]"
                                        />
                                    </Link>
                                ))}
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-col items-start gap-4">
                        <h3 className="text-5 leading-[1.6875rem] text-[#0C111D]">
                            Contact Us
                        </h3>
                        <p className="flex items-center gap-1 leading-[21px] text-[#182230]">
                            <Image
                                alt="location"
                                height={36}
                                width={36}
                                src="/icons/locationI.svg"
                                className="w-[2.25rem] aspect-square"
                            />{' '}
                            <span>
                                Suite SF20, Anafaara Plaza, Gwarimpa, Abuja.
                            </span>
                        </p>
                        <a
                            href="mailto:<EMAIL>"
                            className="flex items-center gap-1 leading-[21px] text-[#182230]"
                        >
                            <Image
                                alt="mail"
                                height={36}
                                width={36}
                                src="/icons/mailI.svg"
                                className="w-[2.25rem] aspect-square"
                            />{' '}
                            <span><EMAIL></span>
                        </a>
                        <a
                            href="tel:+2349137574484"
                            className="flex items-center gap-1 leading-[21px] text-[#182230]"
                        >
                            <Image
                                alt="phone"
                                height={36}
                                width={36}
                                src="/icons/phoneI.svg"
                                className="w-[2.25rem] aspect-square"
                            />{' '}
                            <span>09137574484</span>
                        </a>
                    </div>
                </div>
            </div>

            <div className="p-5 pb-2 md:pb-5 flex-col-reverse gap-6 md:flex-row flex justify-center items-center text-[#A4A4A4] text-[.875rem] leading-[1.1875rem] w-full">
                {/* <p className="basis-1/3"></p> */}
                <p>Copyright 2025. All Rights Reserved.</p>
                {/* <div className="flex items-center gap-[2.75rem]">
                    <Link href="">Privacy policy</Link>
                    <Link href="">Terms and Condition</Link>
                </div> */}
            </div>
        </div>
    )
}
