import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

// For desktop
export default function SidebarLinksD({
    heading,
    href,
    isExpanded,
    icons,
    altIcons,
}: {
    heading: string
    href: string
    isExpanded: boolean
    icons: string
    altIcons: string
}) {
    const path = usePathname()
    return (
        <Link
            href={href}
            className={`${path === href && 'bg-primary text-white'} ${!isExpanded && 'justify-center'} group flex items-center py-2 px-3 gap-2 rounded-[.375rem] ${path !== href && 'hover:bg-blue-50'} text-[#475467]`}
        >
            <div className="w-6">
                <Image
                    src={path === href ? altIcons : icons}
                    alt={icons}
                    width={12}
                    height={12}
                    className="w-full h-auto"
                />
            </div>
            <span className={`${!isExpanded && 'hidden'}`}>{heading}</span>
        </Link>
    )
}
