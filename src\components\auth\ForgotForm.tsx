'use client'
import { RegisterOptions, useForm } from 'react-hook-form'
import apiRequest from '@/lib/auth/client/request'
import { enqueueSnackbar as notify } from 'notistack'
import { handleError } from '@/lib/error'
import { useRouter } from 'next/navigation'
import { Suspense } from 'react'
import Input from '../inputs/Input'

interface FormData {
    email: string
}

const inputArray: {
    label: string
    name: keyof FormData
    type: string
    placeholder: string
    validation: RegisterOptions<FormData, keyof FormData>
}[] = [
    {
        label: 'Email or Phone Number*',
        name: 'email',
        type: 'text',
        placeholder: 'Enter your email address or phone number',
        validation: {
            required: {
                value: true,
                message: `Email address or phone number is required`,
            },
        },
    },
]

function ForgotFormMain() {
    const {
        register,
        handleSubmit,
        setError,
        formState: { errors, isSubmitting },
    } = useForm<FormData>()

    const router = useRouter()

    const onSubmit = async (data: FormData) => {
        try {
            const response = await apiRequest(false).post(
                '/api/auth/forgot-password',
                data
            )
            const { email } = response.data.data

            router.push(`/check?email=${encodeURIComponent(email)}`)
        } catch (err: unknown) {
            const error: ApiWithFormError<FormData> = handleError(err)
            if (error.errors) {
                error.errors.forEach((e) => {
                    setError(e.path, {
                        message: e.message,
                    })
                })
            }
            if (error.message && !error.errors) {
                notify(error.message)
            }
        }
    }

    return (
        <form
            method="POST"
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-[2.4rem]"
        >
            <div className="flex flex-col gap-5">
                {inputArray.map((input) => (
                    <Input<FormData>
                        key={input.name}
                        type={input.type}
                        label={input.label}
                        registerName={input.name}
                        placeholder={input.placeholder}
                        register={register}
                        errors={errors}
                        validation={input.validation}
                    />
                ))}
            </div>

            <div className="flex flex-col gap-4">
                <button
                    disabled={isSubmitting}
                    type="submit"
                    className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] disabled:opacity-50`}
                >
                    {isSubmitting ? (
                        <svg
                            className="animate-spin h-5 w-5 mr-3 border-white border-t-2 border-b-2 border-r-2 border-[rgba(255,255,255,0.2)] rounded-full"
                            viewBox="0 0 24 24"
                        />
                    ) : null}
                    Forgot Password
                </button>
            </div>
        </form>
    )
}

export default function ForgotForm() {
    return (
        <Suspense>
            <ForgotFormMain />
        </Suspense>
    )
}
