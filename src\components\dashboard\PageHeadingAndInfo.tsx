'use client'
import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

export default function PageHeadingAndInfo({
    heading,
    content,
}: {
    heading: string
    content: string
}) {
    const path = usePathname()

    return (
        <div
            className={`md:sticky top-0 z-[3] flex justify-between gap-5 ${['/dashboard', '/dashboard/referral'].includes(path) ? 'p-4' : 'bg-white  p-0'} md:bg-white md:p-5 mb-[2.25rem]`}
        >
            <div className="flex gap-1 flex-col">
                <p className="font-brico font-semibold md:text-[1.25rem] text-[#101828]">
                    {heading}
                </p>
                <p className="text-sm text-[#475467] md:text-[1rem]">
                    {content}
                </p>
            </div>
            <div className="hidden md:flex gap-8 items-center">
                {/* <NotifyButton /> */}
                <Link
                    href="/dashboard/account"
                    className="rounded-full flex bg-[#F2F4F7] p-[.625rem]"
                >
                    <div className="w-6">
                        <Image
                            alt="setting"
                            src="/icons/myAccount.svg"
                            width={24}
                            height={24}
                            className="w-full h-auto"
                        />
                    </div>
                </Link>
            </div>
        </div>
    )
}
