import PageHeadingAndInfo from '@/components/dashboard/PageHeadingAndInfo'
import AccountAndLink from '@/components/dashboard/referral/AccountAndLink'
import HowItWorks from '@/components/dashboard/referral/HowItWorks'
import ReferralCommissionHistory from '@/components/dashboard/referral/ReferralCommissionHistory'
import { fetchCommissionData } from './server'

export default async function Referral() {
    const { summaryData, referralsData } = await fetchCommissionData()

    return (
        <>
            <PageHeadingAndInfo
                content="Refer and get rewarded for every successful sign-up"
                heading="Earn More by Inviting Friends!"
            />

            <div className="flex flex-col gap-6 max-w-5xl px-4">
                <AccountAndLink summaryData={summaryData} />
                <HowItWorks />
                <ReferralCommissionHistory
                    referralsInitialData={referralsData}
                />
            </div>
        </>
    )
}
