import Image from 'next/image'

export default function DatePick({
    open,
    title,
}: {
    open: () => void
    title?: string
}) {
    return (
        <div
            onClick={open}
            className={`p-2 flex items-center justify-center cursor-pointer text-[#344054] hover:bg-[#F9FAFB] text-[.75rem] md:text-sm gap-1 rounded-[.375rem] border border-[#D0D5DD] w-fit px-4 mt-2`}
        >
            <div className="w-5">
                <Image
                    src="/icons/calendar.svg"
                    height={20}
                    width={20}
                    alt="date"
                    className="w-full h-auto max-w-full"
                />
            </div>
            <span>{title ? title : 'Date'}</span>
        </div>
    )
}
