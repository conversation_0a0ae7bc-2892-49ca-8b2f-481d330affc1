import Link from 'next/link'
import SignUpForm from '@/components/auth/SignUpForm'
import { Suspense } from 'react'

export default function SignUp() {
    return (
        <div className="flex md:max-w-[65%] lg:max-w-[75%] mx-auto gap-8 flex-col">
            <div className="flex flex-col gap-[.5rem]">
                <h2 className="font-brico font-semibold text-[1.5rem] leading-[1.75rem] md:font-bold md:text-[1.875rem] md:leading-[2.25rem] text-[#101828]">
                    Get Started in Seconds
                </h2>
                <p className="leading-[1.3125rem] text-[#475467]">
                    Create your free account in seconds and unlock seamless
                    transactions
                </p>
            </div>

            {/* Form goes here */}
            <Suspense>
                <SignUpForm />
            </Suspense>

            {/* Sign in link */}

            <p className="text-[#475467] leading-[1.3125rem]">
                Already have an account?{' '}
                <Link href="/signin" className="text-primary">
                    Log in
                </Link>
            </p>
        </div>
    )
}
