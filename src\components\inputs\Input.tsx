import {
    RegisterOptions,
    UseFormRegister,
    FieldValues,
    Path,
    FieldErrors,
} from 'react-hook-form'
import { InputHTMLAttributes } from 'react'

type InputProps<T extends FieldValues> = {
    label?: string
    type: string
    placeholder: string
    isDisabled?: boolean
    defaultValue?: string
    registerName: Path<T>
    register: UseFormRegister<T>
    validation?: RegisterOptions<T, Path<T>>
    errors?: FieldErrors<T>
} & Omit<
    InputHTMLAttributes<HTMLInputElement>,
    'type' | 'placeholder' | 'defaultValue' | 'disabled' | 'id'
>

export default function Input<T extends FieldValues>({
    label,
    type,
    placeholder,
    isDisabled,
    defaultValue,
    registerName,
    register,
    validation = {},
    errors,
    // accept every other props and pass it to the input element
    ...props
}: InputProps<T>) {
    return (
        <div className="gap-[.375rem] flex flex-col">
            {label && (
                <label
                    htmlFor={`input${registerName}`}
                    className="font-medium text-sm leading-[1.1875rem]"
                >
                    {label}
                </label>
            )}
            <input
                type={type}
                id={`input${registerName}`}
                {...register(registerName, validation)}
                placeholder={placeholder}
                defaultValue={defaultValue}
                disabled={isDisabled}
                className="w-full border border-[#D0D5DD] px-4 py-[.875rem] rounded-lg placeholder:text-[#667085] outline-none appearance-none outline-0 ring-0 "
                onWheel={(e) => e.currentTarget.blur()} // Prevent scroll on input
                {...props} // Spread any additional props to the input element
            />

            {errors?.[registerName] && (
                <span className="text-sm text-[#475467]">
                    {errors[registerName]?.message as string}
                </span>
            )}
        </div>
    )
}
