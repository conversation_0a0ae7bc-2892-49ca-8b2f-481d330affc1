'use client'
import { AnimatePresence, motion } from 'framer-motion'
import { Plus } from 'lucide-react'
import { useState } from 'react'

export default function IndividualFAQ({
    heading,
    content,
}: {
    heading: string
    content: string
}) {
    const [show, setShow] = useState(false)

    return (
        <div
            className={`${
                show && 'bg-[#F9FAFB]'
            } p-5 md:p-8 flex flex-col rounded-2xl gap-2 w-full max-w-3xl`}
        >
            <div
                className="flex items-center justify-between gap-6 cursor-pointer"
                onClick={() => setShow((value) => !value)}
            >
                <h3 className="text-[1.125rem] leading-[1.5rem] text-[#101828]">
                    {heading}
                </h3>
                <motion.div
                    animate={{ rotate: show ? -45 : 0 }}
                    className={`${show ? 'bg-primary' : 'bg-[#F9FAFB]'} p-3 rounded-full`}
                >
                    <Plus
                        size={24}
                        className={`${show ? 'text-white' : 'text-[#475467]'}`}
                    />
                </motion.div>
            </div>
            {show && (
                <AnimatePresence>
                    <motion.p
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="leading-[1.35rem] text-[#475467]"
                    >
                        <span
                            dangerouslySetInnerHTML={{ __html: content }}
                        ></span>
                    </motion.p>
                </AnimatePresence>
            )}
        </div>
    )
}
