'use client'
import { getDisplayNairaAmount } from '@/utilities'
import Image from 'next/image'
import { useState } from 'react'
import { enqueueSnackbar as notify } from 'notistack'

export default function AccountAndLink({
    summaryData,
}: {
    summaryData: ReferralSummary
}) {
    const { balance } = summaryData
    const [show, setShow] = useState(false)
    return (
        <div className="rounded-3xl md:p-6 gap-6 flex flex-col md:flex-row bg-white px-5 py-6 overflow-x-auto md:flex-wrap lg:flex-nowrap md:*:w-full">
            <div className="rounded-xl bg-[#F9FAFB] flex gap-5 items-start justify-between">
                <div className="basis-[70%] flex flex-col gap-3 items-start px-4 py-6 md:px-6">
                    <div className="w-[3rem] p-3 rounded-full bg-cipLight">
                        <Image
                            height={48}
                            width={48}
                            alt=""
                            src="/icons/gift.svg"
                            className="w-full h-auto"
                        />
                    </div>
                    <div className="gap-2 flex flex-col">
                        {/*  */}
                        <div className="flex gap-3 items-center">
                            <p className="text-[#475467]">Commission earned</p>
                            <div
                                className="w-6 cursor-pointer"
                                onClick={() => setShow((value) => !value)}
                            >
                                <Image
                                    src={
                                        show
                                            ? '/icons/hide1.svg'
                                            : '/icons/eyes1.svg'
                                    }
                                    height={24}
                                    width={24}
                                    alt=""
                                    className="w-full h-auto"
                                />{' '}
                            </div>
                        </div>
                        {/*  */}
                        <p className="min-w-[12.1875rem] text-[#182230] text-[1.875rem] font-semibold">
                            {show
                                ? `${getDisplayNairaAmount(balance)}`
                                : '----'}
                        </p>
                    </div>
                </div>
                <div className="basis-[30%]">
                    <Image
                        height={120}
                        width={120}
                        alt=""
                        src="/img/fancyButton.svg"
                        className="w-full h-auto"
                    />
                </div>
            </div>

            <div className="rounded-xl bg-[#F9FAFB] px-4 py-6 md:px-6 grow flex flex-col items-start gap-5 md:gap-[1.5625rem]">
                <div className="flex flex-col gap-1 md:gap-2">
                    <h4 className="font-brico font-medium text-[#182230]">
                        Your Unique Referral Link
                    </h4>
                    <p className="text-[#475467]">
                        Copy, share, and start earning today.
                    </p>
                </div>
                <div className="flex items-center gap-4 pl-[1.3125rem] p-[.625rem] rounded-full bg-[#182230] max-w-[85%] md:max-w-[80%]">
                    <p className="text-[#FCFCFD] text-[.75rem] md:text-[1rem] overflow-hidden whitespace-nowrap">
                        {summaryData.referral_link}
                    </p>
                    <button
                        className="bg-white p-[.625rem] md:px-[1.5rem] text-[#182230] gap-1 rounded-full flex items-center"
                        aria-label="Copy referral link"
                        onClick={() => {
                            navigator.clipboard.writeText(
                                summaryData.referral_link
                            )
                            notify(
                                'Referral link copied to clipboard. You can now share it with your friends!'
                            )
                        }}
                    >
                        <div className="w-6">
                            <Image
                                src="/icons/copy.svg"
                                height={24}
                                width={24}
                                alt="copy"
                            />
                        </div>
                        <span className="hidden md:block">Copy</span>
                    </button>
                </div>
                <div className="flex gap-2 items-center">
                    <p>Referral Code: </p>
                    <p className="text-[#182230] font-semibold text-lg">
                        {summaryData.referral_code}
                    </p>
                </div>
            </div>
        </div>
    )
}
