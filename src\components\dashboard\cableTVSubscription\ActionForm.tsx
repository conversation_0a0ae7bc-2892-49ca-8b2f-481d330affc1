'use client'
import { useForm } from 'react-hook-form'
import React, { SetStateAction, useState } from 'react'
import Input from '@/components/inputs/Input'
import { FormData } from './type'
import SelectInput from '@/components/inputs/SelectInput'
import apiRequest from '@/lib/auth/client/request'
import { handleError } from '@/lib/error'
import { enqueueSnackbar as notify } from 'notistack'
import { getDisplayNairaAmount } from '@/utilities'
import { NO_SMARTCARD, NO_SUBTYPE } from '@/components/constants'

export default function ActionForm({
    setFormData,
    setHasClickedButton,
    username,
    setUsername,
}: {
    setFormData: React.Dispatch<SetStateAction<FormData>>
    setHasClickedButton: React.Dispatch<SetStateAction<boolean>>
    username: string
    setUsername: React.Dispatch<SetStateAction<string>>
}) {
    const [options, setOptions] = useState<
        | {
              name: string
              price: number
              code: string
          }[]
        | null
    >(null)
    const [isLoading, setIsLoading] = useState<{
        smartCardLoading: boolean
        plansLoading: boolean
    }>({
        smartCardLoading: false,
        plansLoading: false,
    })

    const {
        register,
        handleSubmit,
        setValue,
        watch,
        setError,
        formState: { errors },
        trigger,
    } = useForm<FormData>()

    const smartCardNumber = watch('smartCardNumber')
    const biller = watch('biller')
    const plan = watch('plan')

    const onSubmit = (data: FormData) => {
        setFormData(data)
        setHasClickedButton(true)
    }

    //validate Smartcard number
    const validateSmartCardNumber = async () => {
        setUsername('')
        const form = {
            smartCardNumber: smartCardNumber,
            biller: biller,
        }
        const isValid = await trigger(['smartCardNumber', 'biller', 'plan'])
        if (!isValid) trigger(['smartCardNumber', 'biller', 'plan'])

        if (isValid) {
            try {
                setIsLoading((prev) => ({
                    ...prev,
                    smartCardLoading: true,
                }))
                const response = await apiRequest().post(`/api/tv/verify`, form)
                const { data } = response.data
                setUsername(data.name)
            } catch (err: unknown) {
                const error: ApiWithFormError<FormData> = handleError(err)
                if (error.errors) {
                    error.errors.forEach((e) => {
                        setError(e.path, {
                            message: e.message,
                        })
                    })
                }
                if (error.message && !error.errors) {
                    notify(error.message, { variant: 'error' })
                }
            } finally {
                setIsLoading((prev) => ({
                    ...prev,
                    smartCardLoading: false,
                }))
            }
        }
    }

    const fetchTvPlans = async (biller: string) => {
        setIsLoading((prev) => ({
            ...prev,
            plansLoading: true,
        }))
        setValue('plan', '')
        try {
            const response = await apiRequest().get(`/api/tv?biller=${biller}`)
            const { data } = response.data
            setOptions(data)
        } catch (err: unknown) {
            const error: ApiWithError = handleError(err)
            if (error.message && !error.errors) {
                notify(error.message)
            }
        } finally {
            setIsLoading((prev) => ({
                ...prev,
                plansLoading: false,
            }))
        }
    }

    const disableValidate = !(
        smartCardNumber?.length > 8 &&
        !isLoading.smartCardLoading &&
        Boolean(biller) &&
        Boolean(plan)
    )

    return (
        <form
            method="POST"
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-[2.75rem] max-w-md mx-auto md:mx-0"
        >
            <div className="flex flex-col gap-5 md:gap-7">
                <SelectInput<FormData>
                    label={'Biller Name'}
                    inputOptions={[
                        { option: 'DSTV', value: 'DSTV' },
                        { option: 'GOTV', value: 'GOTV' },
                        { option: 'STARTIMES', value: 'STARTIMES' },
                        { option: 'SHOWMAX', value: 'SHOWMAX' },
                    ]}
                    onInput={(e) => {
                        const biller = e.currentTarget.value
                        setUsername('')
                        setValue('biller', biller)
                        setOptions(null)
                        if (biller) {
                            fetchTvPlans(biller)
                        }
                    }}
                    registerName={'biller'}
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: `Biller name is required`,
                        },
                    }}
                />
                <SelectInput<FormData>
                    label={'Choose Plan'}
                    placeholder={isLoading.plansLoading ? 'Please wait' : ''}
                    disabled={isLoading.plansLoading || !options}
                    inputOptions={
                        options &&
                        options.map((option) => ({
                            option: `${option.name.split(/\sN\d+/)[0]} @ ${getDisplayNairaAmount(option.price)}`,
                            value: `${option.code}|${option.name.split(/\sN\d+/)[0]} @ ${getDisplayNairaAmount(option.price)}`,
                        }))
                    }
                    registerName={'plan'}
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: 'Please choose a plan',
                        },
                    }}
                />

                {!NO_SMARTCARD.includes(biller) && (
                    <Input<FormData>
                        type={'number'}
                        label={'Smart Card Number'}
                        registerName={'smartCardNumber'}
                        placeholder={'Enter smart card number'}
                        register={register}
                        errors={errors}
                        validation={{
                            required: {
                                value: true,
                                message: `Smart card number is required`,
                            },
                            minLength: {
                                value: 8,
                                message: 'Input a valid meter number',
                            },
                        }}
                        onInput={() => setUsername('')}
                    />
                )}

                {(username || NO_SMARTCARD.includes(biller)) && (
                    <>
                        {!NO_SMARTCARD.includes(biller) && (
                            <Input<FormData>
                                type={'text'}
                                label={'Account Name'}
                                registerName={'username'}
                                placeholder={'username'}
                                isDisabled={true}
                                defaultValue={username}
                                register={register}
                                errors={errors}
                                validation={{
                                    required: {
                                        value: false,
                                        message: 'Username is required',
                                    },
                                }}
                            />
                        )}

                        {!NO_SUBTYPE.includes(biller) && (
                            <SelectInput<FormData>
                                label={'Subscription Type'}
                                placeholder="Choose subscription type"
                                inputOptions={[
                                    {
                                        option: 'I want to change bouquet',
                                        value: 'CHANGE',
                                    },
                                    {
                                        option: 'I want to keep my current bouquet',
                                        value: 'RENEW',
                                    },
                                ]}
                                registerName={'subscriptionType'}
                                register={register}
                                errors={errors}
                                validation={{
                                    required: {
                                        value: true,
                                        message: `Subscription type is required`,
                                    },
                                }}
                            />
                        )}

                        <Input<FormData>
                            type={'tel'}
                            label={`Phone Number${biller === 'SHOWMAX' ? ' Attached to Account' : ''} `}
                            registerName={'phone'}
                            placeholder={'Enter your phone number'}
                            register={register}
                            errors={errors}
                            validation={{
                                required: {
                                    value: true,
                                    message: `Phone number is required`,
                                },
                                pattern: {
                                    value: /^(?:\+?234|0)[789][01]\d{8}$/,
                                    message:
                                        'Please enter a valid Nigerian phone number',
                                },
                            }}
                        />
                    </>
                )}
            </div>

            <div className="flex flex-col gap-4">
                {username || NO_SMARTCARD.includes(biller) ? (
                    <button
                        type="submit"
                        className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem]`}
                    >
                        Buy now
                    </button>
                ) : (
                    <button
                        type="button"
                        onClick={validateSmartCardNumber}
                        disabled={disableValidate}
                        className="font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] disabled:opacity-60 hover:disabled:opacity-60 disabled:cursor-not-allowed"
                    >
                        {isLoading.smartCardLoading ? (
                            <span>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className="lucide lucide-loader-circle animate-spin"
                                >
                                    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                </svg>
                            </span>
                        ) : (
                            'Validate Smart Card Number'
                        )}
                    </button>
                )}
            </div>
        </form>
    )
}
