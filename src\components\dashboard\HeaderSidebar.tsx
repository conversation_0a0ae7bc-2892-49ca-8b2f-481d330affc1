'use client'
import Image from 'next/image'
import Link from 'next/link'
import { useState, useRef, useEffect } from 'react'
import Sidebar from './Sidebar'

export default function HeaderSidebar({
    children,
}: {
    children: React.ReactNode
}) {
    const [showSideBar, setShowSideBar] = useState(false)
    const burger = useRef<HTMLImageElement | null>(null)
    const sidebar = useRef<HTMLDivElement | null>(null)
    // const path = usePathname()

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            const target = e.target as Node
            if (
                burger.current &&
                sidebar.current &&
                !burger.current.contains(target) &&
                !sidebar.current.contains(target)
            ) {
                setShowSideBar(false)
            }
        }

        document.body.addEventListener('click', handleClickOutside)
        return () => {
            document.body.removeEventListener('click', handleClickOutside)
        }
    }, [])
    return (
        <div className="h-screen max-h-screen flex">
            <Sidebar
                showSideBar={showSideBar}
                setShowSideBar={setShowSideBar}
                sidebar={sidebar}
            />
            <div className="flex flex-col overflow-y-auto grow" id="scrollRel">
                {/* header and page */}
                <div className="top-0 z-20 bg-white sticky flex md:hidden">
                    <div className="p-4 flex items-center gap-5 justify-between w-full">
                        <Link href="/" className="w-[5.5rem] block">
                            <Image
                                alt="Logo"
                                src="/img/logo.png"
                                width={85.6}
                                height={32}
                                className="w-full h-auto max-w-full"
                            />
                        </Link>
                        <Image
                            src={
                                showSideBar
                                    ? '/icons/cancel.svg'
                                    : '/icons/menu.svg'
                            }
                            height={24}
                            width={24}
                            ref={burger}
                            role="button"
                            alt="burger nav"
                            className={`${showSideBar && 'hidden'} w-[1.5rem] aspect-square block md:hidden cursor-pointer`}
                            onClick={() => setShowSideBar((value) => !value)}
                        />
                    </div>
                </div>
                <div className="bg-[#F9F9F9] pb-6 grow">{children}</div>
            </div>
        </div>
    )
}
