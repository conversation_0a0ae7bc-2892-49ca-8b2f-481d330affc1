'use client'
import { useForm } from 'react-hook-form'
import React, { SetStateAction, useEffect } from 'react'
import Input from '@/components/inputs/Input'
import { FormData } from './type'
import SelectInput from '@/components/inputs/SelectInput'
import InsertInput from '@/components/inputs/InsertInput'

const networkPrefixes: Record<string, string[]> = {
    MTN: [
        '0803',
        '0806',
        '0703',
        '0706',
        '0813',
        '0816',
        '0810',
        '0814',
        '0903',
        '0906',
        '0913',
        '0702',
        '0704',
    ],
    AIRTEL: [
        '0802',
        '0808',
        '0708',
        '0812',
        '0701',
        '0902',
        '0901',
        '0907',
        '0912',
    ],
    '9MOBILE': ['0809', '0818', '0817', '0908', '0909'],
    GLO: ['0805', '0807', '0705', '0811', '0815', '0905', '0915'],
}

export default function ActionForm({
    setFormData,
    setHasClickedButton,
    clearForm,
}: {
    setFormData: React.Dispatch<SetStateAction<FormData>>
    setHasClickedButton: React.Dispatch<SetStateAction<boolean>>
    clearForm: boolean
}) {
    const {
        register,
        handleSubmit,
        reset,
        watch,
        setValue,
        setError,
        clearErrors,
        getValues,
        trigger,
        formState: { errors },
    } = useForm<FormData>()

    const network = watch('network')
    const phone = watch('phone')

    const onSubmit = (data: FormData) => {
        setFormData(data)
        setHasClickedButton(true)
    }

    // throw warning when right number is not selected
    useEffect(() => {
        if (network && phone) {
            const allowedPrefixes = networkPrefixes[network] || []
            const phonePrefix = phone.replace(/^\+234/, '0').slice(0, 4) // Normalize +234 format

            if (
                allowedPrefixes.length &&
                !allowedPrefixes.includes(phonePrefix)
            ) {
                setError('phone', {
                    type: 'custom',
                    message: `Crosscheck if this is a/an ${network} number`,
                })
            } else {
                clearErrors('phone')
            }
        }
    }, [network, phone, setError, clearErrors])

    //clear form on transaction complete
    useEffect(() => {
        reset()
    }, [clearForm, reset])

    return (
        <form
            method="POST"
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-[2.75rem] max-w-md mx-auto md:mx-0"
        >
            <div className="flex flex-col gap-5 md:gap-7">
                <SelectInput<FormData>
                    label={'Choose Network'}
                    inputOptions={[
                        { option: 'MTN' },
                        { option: 'GLO' },
                        { option: 'AIRTEL' },
                        { option: '9MOBILE' },
                    ]}
                    registerName={'network'}
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: `Network is required`,
                        },
                    }}
                />
                <Input<FormData>
                    type={'tel'}
                    label={'Phone Number'}
                    registerName={'phone'}
                    placeholder={'Enter your phone number'}
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: `Phone number is required`,
                        },
                        pattern: {
                            value: /^(?:\+?234|0)[789][01]\d{8}$/,
                            message:
                                'Please enter a valid Nigerian phone number',
                        },
                    }}
                />
                <InsertInput<FormData>
                    trigger={trigger}
                    label={'Amount'}
                    inputOptions={[
                        { option: '₦100', value: 100 },
                        { option: '₦200', value: 200 },
                        { option: '₦500', value: 500 },
                        { option: '₦1000', value: 1000 },
                        { option: '₦2000', value: 2000 },
                        { option: '₦5000', value: 5000 },
                    ]}
                    placeholder="Enter amount"
                    getValues={getValues}
                    watch={watch}
                    setValue={setValue}
                    registerName={'amount'}
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: `Amount is required`,
                        },
                        min: {
                            value: 50,
                            message: `Minimum amount is ₦50`,
                        },
                    }}
                />
            </div>

            <div className="flex flex-col gap-4">
                <button
                    type="submit"
                    className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem]`}
                >
                    Buy now
                </button>
            </div>
        </form>
    )
}
