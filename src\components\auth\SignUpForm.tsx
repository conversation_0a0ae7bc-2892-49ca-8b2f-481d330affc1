'use client'
import Image from 'next/image'
import { RegisterOptions, useForm } from 'react-hook-form'
import Input from '../inputs/Input'
import { enqueueSnackbar as notify } from 'notistack'
import { handleError } from '@/lib/error'
import apiRequest from '@/lib/auth/client/request'
import { useRouter, useSearchParams } from 'next/navigation'
import { useState } from 'react'

interface FormData {
    full_name: string
    phonenumber: string
    email: string
    password: string
    rePassword?: string
    referral_code?: string
}

export default function SignUpForm() {
    const referral_code = useSearchParams().get('ref')
    const inputArray: {
        label: string
        name: keyof FormData
        type: string
        placeholder: string
        validation: RegisterOptions<FormData, keyof FormData>
        defaultValue?: string
    }[] = [
        {
            label: 'Name*',
            name: 'full_name',
            type: 'text',
            placeholder: 'Enter your name',
            validation: {
                required: {
                    value: true,
                    message: `Name is required`,
                },
            },
        },
        {
            label: 'Phone number*',
            name: 'phonenumber',
            type: 'tel',
            placeholder: 'Enter your phone number',
            validation: {
                required: {
                    value: true,
                    message: `Phone number is required`,
                },
            },
        },
        {
            label: 'Email*',
            name: 'email',
            type: 'email',
            placeholder: 'Enter your email',
            validation: {
                required: {
                    value: true,
                    message: `Email is required`,
                },
                pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Enter a valid email address',
                },
            },
        },
        {
            label: 'Password*',
            name: 'password',
            type: 'password',
            placeholder: 'Enter your password',
            validation: {
                required: {
                    value: true,
                    message: `Password is required`,
                },
                minLength: {
                    value: 8,
                    message: 'Password must be at least 8 characters',
                },
            },
        },
        {
            label: 'Confirm Password*',
            name: 'rePassword',
            type: 'password',
            placeholder: 'Enter your password',
            validation: {
                required: {
                    value: true,
                    message: `Password is required`,
                },
                validate: (value) =>
                    value === password || 'Passwords do not match',
            },
        },
        {
            label: 'Referral Code',
            name: 'referral_code',
            type: 'text',
            placeholder: 'Enter referral code (optional)',
            validation: {
                required: false,
                pattern: {
                    value: /^[A-Z0-9]{6}$/,
                    message: 'Invalid referral code format',
                },
            },
            defaultValue: referral_code || '',
        },
    ]
    const {
        register,
        handleSubmit,
        setError,
        watch,
        formState: { errors, isSubmitting },
    } = useForm<FormData>()

    const router = useRouter()
    const password = watch('password')
    const [googleFormLoading, setGoogleFormLoading] = useState(false)
    const signupState = encodeURIComponent(
        JSON.stringify({
            referral_code: referral_code || '',
        })
    )
    const CALLBACK_URL = process.env.NEXT_PUBLIC_GOOGLE_CALLBACK_URL
    const GOOGLE_CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID
    const GOOGLE_AUTH_URL = `https://accounts.google.com/o/oauth2/auth?response_type=code&client_id=${GOOGLE_CLIENT_ID}&redirect_uri=${CALLBACK_URL}&scope=profile%20email&state=${signupState}`

    const onSubmit = async (data: FormData) => {
        try {
            delete data.rePassword // Remove rePassword from the data sent to the API
            const request = await apiRequest(false).post(
                '/api/auth/register',
                data
            )
            const { data: responseData } = request.data
            router.push(`/verify-email?email=${responseData.email}`)
        } catch (err: unknown) {
            const error: ApiWithFormError<FormData> = handleError(err)
            if (error.errors) {
                error.errors.forEach((e) => {
                    setError(e.path, {
                        message: e.message,
                    })
                })
            }
            if (error.message && !error.errors) {
                notify(error.message, { variant: 'error' })
            }
        }
    }

    const signInWithGoogle = () => {
        setGoogleFormLoading(true)
        router.push(GOOGLE_AUTH_URL)
    }

    return (
        <form
            method="POST"
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-[2.75rem]"
        >
            <button
                disabled={googleFormLoading}
                type="button"
                onClick={signInWithGoogle}
                className={`font-vietnam py-3 px-4 rounded-lg bg-white text-[#344054] border border-[#D0D5DD] w-full flex items-center justify-center leading-[1.5rem] gap-3`}
            >
                {googleFormLoading ? (
                    <svg
                        className="animate-spin h-5 w-5 mr-3 border-[#344054] border-t-2 border-b-2 border-r-2 rounded-full"
                        viewBox="0 0 24 24"
                    />
                ) : (
                    <Image
                        alt="google"
                        src="/icons/googleIcon.svg"
                        width={24}
                        height={24}
                        className="w-6 aspect-square"
                    />
                )}
                Sign up with Google
            </button>

            <p className="text-center text-[#475467] leading-[1.3125rem]">
                or sign up with your email address
            </p>
            <div className="flex flex-col gap-5">
                {inputArray.map((input) => (
                    <Input<FormData>
                        key={input.name}
                        type={input.type}
                        label={input.label}
                        registerName={input.name}
                        placeholder={input.placeholder}
                        register={register}
                        errors={errors}
                        validation={input.validation}
                        defaultValue={input.defaultValue}
                        isDisabled={
                            input.name === 'referral_code' && !!referral_code
                        }
                    />
                ))}
            </div>

            <button
                disabled={isSubmitting}
                type="submit"
                className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] disabled:opacity-50`}
            >
                {isSubmitting ? (
                    <svg
                        className="animate-spin h-5 w-5 mr-3 border-white border-t-2 border-b-2 border-r-2 border-[rgba(255,255,255,0.2)] rounded-full"
                        viewBox="0 0 24 24"
                    />
                ) : null}
                Get started
            </button>
        </form>
    )
}
