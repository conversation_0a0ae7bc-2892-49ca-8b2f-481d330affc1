import DashboardPagesShell from '@/components/dashboard/DashboardPagesShell'
import apiRequest from '@/lib/auth/server/request'
import Image from 'next/image'

export const revalidate = 3600 // 1 hour
interface ActionData {
    action: string
    points: string
}

interface InstructionsResponse {
    first_prize: string
    second_prize: string
    third_prize: string
    action_data: ActionData[]
}

const TableSection = ({ tableData }: { tableData: ActionData[] }) => {
    return (
        <div className="overflow-x-auto text-[#182230] text-sm py-8 md:py-[44px] text-left">
            <table className="table-auto border-collapse max-w-2xl w-full">
                <thead>
                    <tr className="border-b border-[#EAECF0] bg-[#F2F4F7]">
                        <th className="p-4 min-w-[10rem] first:rounded-tl-lg">
                            Action
                        </th>
                        <th className="p-4 min-w-6">Points</th>
                    </tr>
                </thead>
                <tbody>
                    {tableData.map((item, index) => (
                        <tr
                            key={index}
                            className="border-b border-[#EAECF0] text-[#475467]"
                        >
                            <td className="p-4 min-w-[10rem] space-x-2">
                                {item.action}
                            </td>
                            <td className="p-4 min-w-6">{item.points}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    )
}

export default async function RewardsPage() {
    const request = await apiRequest(false)
    const { data } = (await request
        .get('/api/rewards/instructions')
        .then((res) => res.data)) as { data: InstructionsResponse }

    return (
        <DashboardPagesShell
            innerComponentContent="See how your app activity turns into real rewards."
            innerComponentHeading="How to Earn, Climb & Win "
        >
            <div className="text-[#667085] font-general font-medium text-[1rem] max-w-3xl">
                <Image
                    src="/icons/gold-cup.svg"
                    alt="Gold Cup"
                    width={187}
                    height={159}
                />
                <h3 className="mt-8 mb-3 text-[#182230] font-semibold font-brico text-3xl">
                    Welcome to CIP — Your Reward Journey Starts Here
                </h3>
                <p>
                    Ever imagined earning something valuable just by using the
                    app you already love? With CIP, our{' '}
                    <b>Customer Incentive Program</b>, now you can.
                </p>
                <br />
                <p>
                    We created CIP to give back — rewarding you for your
                    everyday activity on the platform. Whether you’re paying
                    bills, funding your card, or simply exploring new services,
                    every action counts toward your growth on the leaderboard.
                </p>
                <h3 className="mt-8 mb-3 text-[#182230] font-semibold font-brico text-3xl">
                    What is CIP?
                </h3>
                <p>
                    CIP is our loyalty points system that tracks your in-app
                    activity and ranks you on a monthly leaderboard.
                </p>
                <p>
                    As you complete key actions, you earn points. These points
                    determine your position on the leaderboard — and the top
                    performers win real rewards.
                </p>
                <h3 className="mt-8 mb-3 text-[#182230] font-semibold font-brico text-3xl">
                    What Can You Win?
                </h3>
                <p>
                    At the end of every month, our top users win cash rewards:
                </p>{' '}
                <br />
                <p>🥇 1st Place — {data.first_prize} cash</p> <br />
                <p>🥈 2nd Place — {data.second_prize} cash</p> <br />
                <p>🥉 3rd Place — {data.third_prize} cash</p> <br />
                <p className="italic">
                    Plus, active users may receive bonus giveaways and data
                    rewards throughout the month.
                </p>
                <h3 className="mt-8 mb-3 text-[#182230] font-semibold font-brico text-3xl">
                    How Points Are Earned
                </h3>
                <TableSection tableData={data.action_data} />
                <h3 className="mt-8 mb-3 text-[#182230] font-semibold font-brico text-3xl">
                    Why Join CIP?
                </h3>
                <p>➤ Earn rewards from actions you already do</p> <br />
                <p>➤ Compete monthly — everyone starts fresh</p> <br />
                <p>➤ Enjoy gamified progress and visibility</p> <br />
                <p>➤ Win real cash and exclusive perks</p>
            </div>
        </DashboardPagesShell>
    )
}
