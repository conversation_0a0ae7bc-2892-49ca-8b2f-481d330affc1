'use client'
import { useEffect, useState } from 'react'
import { FormData } from './type'
import ActionForm from './ActionForm'
import ConfirmTransaction from '../ConfirmTransaction'
import { handleError } from '@/lib/error'
import { enqueueSnackbar as notify } from 'notistack'
import apiRequest from '@/lib/auth/client/request'
import { useDisclosure } from '@mantine/hooks'
import { Modal } from '@mantine/core'
import { CheckCircle } from 'lucide-react'
import { revalidateTransactions } from '@/components/action/action'
import { NO_SMARTCARD, NO_SUBTYPE } from '@/components/constants'

const defaultInlineData: FormData = {
    biller: '',
    smartCardNumber: '',
    subscriptionType: '',
    phone: '',
    plan: '',
}

export default function FormSection() {
    const [hasClickedButton, setHasClickedButton] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [username, setUsername] = useState('')
    const [opened, { open, close }] = useDisclosure(false)
    const [actionKey, setActionKey] = useState('cableTVSubscriptionForm')

    const [inlineErrors, setInlineErrors] =
        useState<FormData>(defaultInlineData)

    useEffect(() => {
        const container = document.getElementById('scrollRel')
        if (container) {
            container.scrollTo({
                top: 0,
                behavior: 'smooth',
            })
        }
    }, [hasClickedButton])

    const [formData, setFormData] = useState<FormData>(defaultInlineData)

    const runRequest = async () => {
        setInlineErrors(defaultInlineData)
        setIsSubmitting(true)
        const [plan_id] = formData.plan.split('|')
        const form = {
            smartCardNumber: NO_SMARTCARD.includes(formData.biller)
                ? formData.phone
                : formData.smartCardNumber,
            biller: formData.biller,
            phoneNumber: formData.phone,
            subscriptionType: NO_SUBTYPE.includes(formData.biller)
                ? undefined
                : formData.subscriptionType,
            planCode: plan_id,
        }

        try {
            await apiRequest().post(`/api/tv`, form)
            setHasClickedButton(false)
            setUsername('')
            open()
        } catch (err: unknown) {
            const error: ApiWithFormError<FormData> = handleError(err)
            if (error.message && !error.errors) {
                notify(error.message, { variant: 'error' })
            }
            if (error.errors) {
                error.errors.forEach((err) => {
                    setInlineErrors((prev) => ({
                        ...prev,
                        [err.path]: err.message,
                    }))
                })
            }
        } finally {
            setIsSubmitting(false)
            revalidateTransactions()
        }
    }
    const closeModal = () => {
        close()
        setActionKey(new Date().toISOString())
    }
    return (
        <div className="grow flex flex-col lg:flex-row">
            <Modal
                opened={opened}
                onClose={closeModal}
                withCloseButton={false}
                centered
            >
                <div className="w-full max-w-md bg-white overflow-hidden p-6">
                    <div className="flex flex-col items-center text-center">
                        <div className="mb-4 bg-green-100 p-3 rounded-full">
                            <CheckCircle className="h-12 w-12 text-green-600" />
                        </div>

                        <h1 className="text-2xl font-bold text-gray-800 mb-2">
                            Payment Successful!
                        </h1>
                        <p className="text-gray-600 mb-6">
                            Thank you for your payment. Your transaction has
                            been completed successfully.
                        </p>
                    </div>
                </div>
            </Modal>
            <div className="lg:pr-[3.125rem] lg:border-r lg:border-[#F5F5F5] lg:basis-1/2 lg:h-full">
                <div
                    className={`${hasClickedButton && 'hidden lg:block'} relative h-full`}
                >
                    <div
                        className={`${hasClickedButton ? 'block' : 'hidden'} absolute top-0 bottom-0 left-0 right-0 bg-[#E8E8E880] backdrop-blur-[.625rem] z-[2]`}
                    ></div>
                    {/* Form goes here */}
                    <ActionForm
                        key={actionKey}
                        setFormData={setFormData}
                        setHasClickedButton={setHasClickedButton}
                        username={username}
                        setUsername={setUsername}
                    />
                </div>
            </div>
            <div className="lg:pl-[3.125rem] lg:basis-1/2">
                {hasClickedButton && (
                    <ConfirmTransaction
                        runRequest={runRequest}
                        goBack={() => {
                            setHasClickedButton(false)
                        }}
                        isSubmitting={isSubmitting}
                        details={[
                            {
                                detail: 'Biller Name',
                                value: formData.biller,
                                error: inlineErrors.biller,
                            },
                            {
                                detail: 'Account Name',
                                value: username,
                                hidden: NO_SMARTCARD.includes(formData.biller),
                            },
                            {
                                detail: 'Smart Card Number',
                                value: formData.smartCardNumber,
                                hidden: NO_SMARTCARD.includes(formData.biller),
                                error: inlineErrors.smartCardNumber,
                            },
                            {
                                detail: 'Plan',
                                value: formData.plan.split('|')[1],
                                error: inlineErrors.plan,
                            },
                        ]}
                    />
                )}
            </div>
        </div>
    )
}
