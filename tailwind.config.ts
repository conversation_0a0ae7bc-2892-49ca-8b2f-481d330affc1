import type { Config } from 'tailwindcss'

export default {
    darkMode: 'class',
    content: [
        './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
        './src/components/**/*.{js,ts,jsx,tsx,mdx}',
        './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    ],
    theme: {
        extend: {
            colors: {
                background: 'var(--background)',
                foreground: 'var(--foreground)',
                primary: '#004EEC',
                cipDark: '#FFFFFF',
                cipLight: '#F0F5FF',
            },
            fontFamily: {
                brico: ['var(--font-brico)', 'sans'],
                inter: ['var(--font-inter)', 'sans'],
                vietnam: ['var(--font-vietnam)', 'sans'],
                helv: ['var(--font-helvetica)', 'sans'],
                general: ['var(--font-general)', 'sans'],
            },
        },
    },
    plugins: [],
} satisfies Config
