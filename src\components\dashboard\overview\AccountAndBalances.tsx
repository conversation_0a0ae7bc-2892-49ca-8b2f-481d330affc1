import { getDisplayNairaAmount } from '@/utilities'
import Image from 'next/image'
import Link from 'next/link'
import StaticAccounts from './StaticAccounts'
import { fetchWalletData } from '@/app/dashboard/fund/server'
import { WalletBalance } from './WalletBalance'
import { ReferralBanner } from './Referral'

export default async function AccountAndBalances() {
    const walletData = await fetchWalletData()
    const progressWidth = () =>
        Math.ceil(
            (walletData.daily_transaction_sum / walletData.transaction_limit) *
                100
        )
    return (
        <div className="flex flex-col gap-6">
            <div className="flex gap-6 overflow-x-auto" id="dash">
                <div className="flex w-full bg-primary rounded-[.75rem] gap-2">
                    <div className="p-[1.875rem] flex min-w-[15rem] flex-col gap-8 justify-center items-start">
                        <WalletBalance balance={walletData.balance} />
                        <Link
                            href="/dashboard/fund"
                            className="p-[.625rem] gap-1 flex items-center bg-white font-medium text-primary rounded-lg"
                        >
                            <div className="w-6">
                                <Image
                                    src="/icons/add.svg"
                                    alt="fund"
                                    width={24}
                                    height={24}
                                    className="w-full h-auto"
                                />{' '}
                            </div>
                            <span>Fund Wallet</span>
                        </Link>
                    </div>
                    <div className="w-[12.5rem] flex items-end ">
                        <Image
                            alt=""
                            width={234}
                            height={234}
                            className="w-full h-auto"
                            src="/img/button.svg"
                        />
                    </div>
                </div>

                <div className="bg-cipLight p-[1.875rem] rounded-[.75rem] flex flex-col gap-8  justify-between w-full">
                    <p className="flex gap-3 text-[#475467]">
                        Account type:{' '}
                        <span className="text-primary font-medium">
                            Level {walletData.account_level}
                        </span>
                    </p>

                    <div className="flex flex-col gap-1">
                        <div className="flex gap-3 items-center text-[1.25rem] text-[#475467]">
                            <p className="md:text-[1.25rem] text-[#475467]">
                                Daily limit
                            </p>{' '}
                        </div>
                        <p className="text-[#182230] text-[1.5625rem] md:text-[1.875rem] font-semibold flex items-center gap-2">
                            <span>
                                {getDisplayNairaAmount(
                                    walletData.daily_transaction_sum
                                )}
                            </span>{' '}
                            <span>\</span>{' '}
                            <span>
                                {getDisplayNairaAmount(
                                    walletData.transaction_limit
                                )}
                            </span>
                        </p>
                        <div className="h-2 rounded-full bg-[#EAECF0] w-full max-w-[17.8125rem] overflow-hidden">
                            <div
                                className="h-full bg-primary"
                                style={{ width: `${progressWidth()}%` }}
                            ></div>
                        </div>
                    </div>

                    <Link
                        // href="/dashboard/upgrade"\
                        href="#"
                        className="text-primary font-medium gap-1 flex items-center"
                    >
                        <span>Upgrade account now</span>{' '}
                        <div className="w-5">
                            <Image
                                alt="arrow"
                                src="/icons/blueArrow.svg"
                                width={18}
                                height={18}
                                className="w-full h-auto"
                            />{' '}
                        </div>
                    </Link>
                </div>
            </div>
            <ReferralBanner />
            <StaticAccounts staticAccountsData={walletData} />
        </div>
    )
}
