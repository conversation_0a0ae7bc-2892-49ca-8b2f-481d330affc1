'use client'
import Image from 'next/image'
// import Link from 'next/link'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'

export default function Pagination({
    pagination,
    paginationParams,
}: {
    pagination: Pagination
    paginationParams: { page?: string | undefined } | undefined
}) {
    const searchParams = useSearchParams()

    const path = usePathname()
    const { replace } = useRouter()
    const currentPageNumber = Number(paginationParams?.page) || 1
    const currentPage =
        currentPageNumber > pagination.total_pages
            ? pagination.total_pages
            : currentPageNumber

    const setPageParam = (page: number) => {
        const params = new URLSearchParams(searchParams)
        if (page) params.set('page', page.toString())
        else params.delete('page')
        replace(`${path}?${params.toString()}`)
    }

    return (
        <>
            {/* for mobile */}
            <div className="items-center justify-between gap-5 flex md:hidden">
                {!(currentPage - 1 < 0) && (
                    <button
                        onClick={() => setPageParam(currentPage - 1)}
                        className="p-2 border border-[#EAECF0] rounded-lg bg-white"
                    >
                        <div className="w-5">
                            {' '}
                            <Image
                                alt="prev"
                                src="/icons/prev.svg"
                                width={20}
                                height={20}
                                className="w-full h-auto max-w-full"
                            />
                        </div>
                    </button>
                )}
                <p>
                    Page {currentPage} of {pagination.total_pages}
                </p>

                {currentPage <= pagination.total_pages && (
                    <button
                        onClick={() => setPageParam(currentPage + 1)}
                        className="p-2 border border-[#EAECF0] rounded-lg bg-white"
                    >
                        <div className="w-5">
                            {' '}
                            <Image
                                alt="next"
                                src="/icons/next.svg"
                                width={20}
                                height={20}
                                className="w-full h-auto max-w-full"
                            />
                        </div>
                    </button>
                )}
            </div>
            {/* for desktop */}
            <div className="items-center justify-between gap-5 hidden md:flex">
                <p>
                    Page {currentPage} of {pagination.total_pages}
                </p>
                <div className="flex items-center gap-3">
                    {[
                        {
                            bName: 'Previous',
                            onClick: () => setPageParam(currentPage - 1),
                            disabled: currentPage <= 1,
                        },
                        {
                            bName: 'Next',
                            onClick: () => setPageParam(currentPage + 1),
                            disabled: currentPage >= pagination.total_pages,
                        },
                    ].map(
                        (btn) =>
                            !btn.disabled && (
                                <button
                                    key={btn.bName}
                                    onClick={btn.onClick}
                                    className={`${btn.disabled && 'invisible'} py-2 px-3 border border-[#EAECF0] rounded-lg bg-white`}
                                >
                                    {btn.bName}
                                </button>
                            )
                    )}
                </div>
            </div>
        </>
    )
}
