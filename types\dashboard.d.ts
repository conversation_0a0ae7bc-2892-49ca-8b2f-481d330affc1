interface Transaction {
    id: string
    amount: number
    status: 'FAILED' | 'SUCCESS' | 'PENDING' | 'REVERSED'
    type: 'DEBIT' | 'CREDIT'
    source: 'AIRTIME' | 'CABLE TV' | 'WALLET FUNDING' | 'DATA'
    remarks: string
    date_created: string
    date_updated: string
}

interface Pagination {
    total_items: number
    total_pages: number
    current_page: number
    page_size: number
    has_next: boolean
    has_previous: boolean
}

type Param =
    | {
          page?: string | undefined
          search?: string | undefined
          status?: string | undefined
          endDate?: string | undefined
          startDate?: string | undefined
      }
    | undefined
interface WalletAccountData {
    account_name: string
    account_number: string
    bank_code: string
    bank_name: string
    provider: string
    reference?: string
    expires_at?: string
}

interface WalletData {
    account_level: number
    balance: number
    daily_transaction_sum: number
    transaction_limit: number
    legacy_balance: number
    migrated: boolean
    migrated_balance: number
    static_accounts: Array<{
        account_number: string
        account_name: string
        bank_name: string
    }>
}

interface Referral {
    full_name: string
    email: string
    date_created: string
    is_verified: boolean
    claimed: boolean
}

interface ReferralResponse {
    referrals: Referral[]
    pagination: Pagination
}

interface ReferralSummary {
    balance: number
    referral_link: string
    referral_code: string
}

interface RewardData {
    position: number
    name: string
    points: number
    you: boolean
}
