export function TrustedBy() {
    return (
        <div className="px-4 max-w-6xl mx-auto pb-[4.125rem] md:pb-[6.25rem] my-[2rem]">
            <div
                className="px-4 py-10 bg-[#0C111D] md:bg-white rounded-2xl gap-8 flex flex-col items-center"
                data-aos="fade-up"
            >
                {/* copy and modify */}
                <div className="flex max-w-3xl gap-4 md:gap-3 flex-col text-center">
                    <h2
                        className={`font-brico font-bold text-[1.5rem] leading-[1.75rem] text-white md:text-[2rem] md:leading-[2.375rem] md:text-[#101828]`}
                    >
                        Trusted by Thousands, Powered by Results
                    </h2>
                    <p className="text-[.875rem] leading-[1.125rem] text-[#98A2B3] md:text-[1.25rem] md:leading-[1.6875rem] md:text-[#475467]">
                        Our numbers speak for themselves—join a growing
                        community that values speed, reliability, and
                        affordability
                    </p>
                </div>
                {/* dynam */}
                <div className="gap-8 flex flex-col md:flex-row justify-between w-full items-center md:px-[3rem] md:py-[2.75rem] bg-[#0C111D] rounded-2xl">
                    {[
                        { metric: 'Successful Transactions', value: '1M+' },
                        { metric: 'Customer Satisfaction Rate', value: '98%' },
                        { metric: 'Average Processing Time', value: '<30s' },
                    ].map((metric) => (
                        <div
                            key={metric.metric}
                            className="flex flex-col gap-3 text-white text-center"
                        >
                            <p className="text-[3rem] leading-[3.5625rem] font-semibold md:text-[3.75rem] md:leading-[4.4375rem] md:font-bold">
                                {metric.value}
                            </p>
                            <p
                                className={`font-brico leading-[1.1875rem] md:text-[1.25rem] md:leading-[1.5rem]`}
                            >
                                {metric.metric}
                            </p>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
}
