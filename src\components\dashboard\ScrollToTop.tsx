'use client'
import { usePathname } from 'next/navigation'
import { useEffect } from 'react'

export default function ScrollToTop() {
    const path = usePathname()

    useEffect(() => {
        const container = document.getElementById('scrollRel')
        if (container) {
            container.scrollTo({
                top: 0,
                behavior: 'smooth',
            })
        }
    }, [path])

    return null
}
