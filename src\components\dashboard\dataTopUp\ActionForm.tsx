'use client'
import { useForm } from 'react-hook-form'
import React, { SetStateAction, useEffect, useState } from 'react'
import Input from '@/components/inputs/Input'
import { FormData } from './type'
import SelectInput from '@/components/inputs/SelectInput'
import { enqueueSnackbar as notify } from 'notistack'
import apiRequest from '@/lib/auth/client/request'
import { handleError } from '@/lib/error'

const networkPrefixes: Record<string, string[]> = {
    MTN: [
        '0803',
        '0806',
        '0703',
        '0706',
        '0813',
        '0816',
        '0810',
        '0814',
        '0903',
        '0906',
        '0913',
        '0702',
        '0704',
    ],
    AIRTEL: [
        '0802',
        '0808',
        '0708',
        '0812',
        '0701',
        '0902',
        '0901',
        '0907',
        '0912',
    ],
    '9MOBILE': ['0809', '0818', '0817', '0908', '0909'],
    GLO: ['0805', '0807', '0705', '0811', '0815', '0905', '0915'],
}

export default function ActionForm({
    setFormData,
    setHasClickedButton,
}: {
    setFormData: React.Dispatch<SetStateAction<FormData>>
    setHasClickedButton: React.Dispatch<SetStateAction<boolean>>
}) {
    const [options, setOptions] = useState<
        | {
              id: string
              name: string
              network: string
              price: number
              size: string
              type: string
              validity: string
          }[]
        | null
    >(null)
    const [isLoading, setIsLoading] = useState(false)
    const {
        register,
        handleSubmit,
        watch,
        setValue,
        setError,
        clearErrors,
        formState: { errors },
    } = useForm<FormData>()

    const network = watch('network')
    const phone = watch('phone')
    const type = watch('type')

    const onSubmit = (data: FormData) => {
        setFormData(data)
        setHasClickedButton(true)
    }

    // throw warning when right number is not selected
    useEffect(() => {
        if (network && phone) {
            const allowedPrefixes = networkPrefixes[network] || []
            const phonePrefix = phone.replace(/^\+234/, '0').slice(0, 4) // Normalize +234 format

            if (
                allowedPrefixes.length &&
                !allowedPrefixes.includes(phonePrefix)
            ) {
                setError('phone', {
                    type: 'custom',
                    message: `Crosscheck if this is a/an ${network} number`,
                })
            } else {
                clearErrors('phone')
            }
        }
    }, [network, phone, setError, clearErrors])

    const fetchDataPlans = async (network: string, type: string) => {
        if (!(network && type)) return

        setIsLoading(true)
        setValue('plan', '')
        try {
            const response = await apiRequest().get(
                `api/data/plans?network=${network}&type=${type}` // type is either AWOOF, GIFTING or SME
            )
            const { data } = response.data
            setOptions(data)
        } catch (err: unknown) {
            const error: ApiWithError = handleError(err)
            if (error.message && !error.errors) {
                notify(error.message)
            }
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <form
            method="POST"
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-[2.75rem] max-w-md mx-auto md:mx-0"
        >
            <div className="flex flex-col gap-5 md:gap-7">
                <SelectInput<FormData>
                    label={'Choose Network'}
                    inputOptions={[
                        { option: 'MTN', value: 'MTN' },
                        { option: 'GLO', value: 'GLO' },
                        { option: 'AIRTEL', value: 'AIRTEL' },
                        { option: '9MOBILE', value: '9MOBILE' },
                    ]}
                    registerName={'network'}
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: `Network is required`,
                        },
                    }}
                    onInput={(e) => {
                        const selectedNetwork = e.currentTarget.value
                        setValue('network', selectedNetwork)
                        setValue('plan', '')
                        fetchDataPlans(selectedNetwork, type)
                    }}
                />
                <SelectInput<FormData>
                    label={'Choose Data Type'}
                    inputOptions={[
                        { option: 'Awoof', value: 'AWOOF' },
                        { option: 'Gifting', value: 'GIFTING' },
                        { option: 'SME', value: 'SME' },
                        { option: 'DataShare', value: 'DATASHARE' },
                    ]}
                    registerName={'type'}
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: `Data type is required`,
                        },
                    }}
                    onInput={(e) => {
                        const selectedType = e.currentTarget.value
                        setValue('type', selectedType)
                        setValue('plan', '')
                        fetchDataPlans(network, selectedType)
                    }}
                />
                <SelectInput<FormData>
                    label={'Choose Plan'}
                    placeholder={isLoading ? 'Please wait' : ''}
                    disabled={isLoading}
                    inputOptions={
                        options &&
                        options.map((option) => ({
                            option: `${option.name} - ${option.type} - ${option.validity} @ ₦${option.price / 100}`,
                            value: `${option.id}|${option.name} - ${option.type} - ${option.validity} @ ₦${option.price / 100}|₦${option.price / 100}`,
                        }))
                    }
                    registerName={'plan'}
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: `Plan is required`,
                        },
                    }}
                />
                <Input<FormData>
                    type={'tel'}
                    label={'Phone Number'}
                    registerName={'phone'}
                    placeholder={'Enter your phone number'}
                    register={register}
                    errors={errors}
                    validation={{
                        required: {
                            value: true,
                            message: `Phone number is required`,
                        },
                        pattern: {
                            value: /^(?:\+?234|0)[789][01]\d{8}$/,
                            message:
                                'Please enter a valid Nigerian phone number',
                        },
                    }}
                />
            </div>

            <div className="flex flex-col gap-4">
                <button
                    type="submit"
                    className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem]`}
                >
                    Buy now
                </button>
            </div>
        </form>
    )
}
