'use client'
import { useEffect, useState } from 'react'
import { FormData } from './type'
import ActionForm from './ActionForm'
import ConfirmTransaction from '../ConfirmTransaction'
import { handleError } from '@/lib/error'
import { enqueueSnackbar as notify } from 'notistack'
import apiRequest from '@/lib/auth/client/request'
import { useDisclosure } from '@mantine/hooks'
import { Modal } from '@mantine/core'
import { CheckCircle } from 'lucide-react'
import { revalidateTransactions } from '@/components/action/action'

const defaultInlineErrors: FormData = {
    biller: '',
    paymentType: '',
    meterNumber: '',
    phone: '',
    amount: '',
}

export default function FormSection() {
    const [hasClickedButton, setHasClickedButton] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [electricityToken, setElectricityToken] = useState('')
    const [username, setUsername] = useState('')
    const [opened, { open, close }] = useDisclosure(false)
    const [actionKey, setActionKey] = useState('electricityBillForm')

    useEffect(() => {
        const container = document.getElementById('scrollRel')
        if (container) {
            container.scrollTo({
                top: 0,
                behavior: 'smooth',
            })
        }
    }, [hasClickedButton])

    const [inlineErrors, setInlineErrors] =
        useState<FormData>(defaultInlineErrors)
    const [formData, setFormData] = useState<FormData>({
        biller: '',
        paymentType: '',
        meterNumber: '',
        phone: '',
        amount: '',
    })

    const runRequest = async () => {
        setInlineErrors(defaultInlineErrors)
        setIsSubmitting(true)
        const form = {
            meter_number: formData.meterNumber,
            provider_id: formData.biller,
            meter_type: formData.paymentType,
            phone: formData.phone,
            amount: parseInt(formData.amount),
        }

        try {
            const response = await apiRequest().post(`/api/electricity`, form)
            const { data } = response.data
            setElectricityToken(data.token)
            setHasClickedButton(false)
            setUsername('')
            open()
        } catch (err: unknown) {
            const error: ApiWithFormError<FormData> = handleError(err)
            if (error.message && !error.errors) {
                notify(error.message, { variant: 'error' })
            }
            if (error.errors) {
                error.errors.forEach((err) => {
                    setInlineErrors((prev) => ({
                        ...prev,
                        [err.path]: err.message,
                    }))
                })
            }
        } finally {
            setIsSubmitting(false)
            revalidateTransactions()
        }
    }

    const closeModal = () => {
        close()
        setActionKey(new Date().toISOString())
    }

    return (
        <div className="grow flex flex-col lg:flex-row">
            <Modal
                opened={opened}
                onClose={closeModal}
                withCloseButton={false}
                centered
            >
                <div className="w-full max-w-md bg-white overflow-hidden p-6">
                    <div className="flex flex-col items-center text-center">
                        <div className="mb-4 bg-green-100 p-3 rounded-full">
                            <CheckCircle className="h-12 w-12 text-green-600" />
                        </div>

                        <h1 className="text-2xl font-bold text-gray-800 mb-2">
                            Payment Successful!
                        </h1>
                        <p className="text-gray-600 mb-6">
                            Thank you for your payment. Your transaction has
                            been completed successfully.
                        </p>
                        {electricityToken && (
                            <p className="text-gray-600 mb-6">
                                Your Token is:{' '}
                                <span className="text-primary">
                                    {electricityToken}
                                </span>
                            </p>
                        )}
                    </div>
                </div>
            </Modal>
            <div className="lg:pr-[3.125rem] lg:border-r lg:border-[#F5F5F5] lg:basis-1/2 lg:h-full">
                <div
                    className={`${hasClickedButton && 'hidden lg:block'} relative h-full`}
                >
                    <div
                        className={`${hasClickedButton ? 'block' : 'hidden'} absolute top-0 bottom-0 left-0 right-0 bg-[#E8E8E880] backdrop-blur-[.625rem] z-[2]`}
                    ></div>

                    <ActionForm
                        key={actionKey}
                        setFormData={setFormData}
                        setHasClickedButton={setHasClickedButton}
                        username={username}
                        setUsername={setUsername}
                    />
                </div>
            </div>
            <div className="lg:pl-[3.125rem] lg:basis-1/2">
                {hasClickedButton && (
                    <ConfirmTransaction
                        runRequest={runRequest}
                        goBack={() => {
                            setHasClickedButton(false)
                            setInlineErrors(defaultInlineErrors)
                        }}
                        isSubmitting={isSubmitting}
                        details={[
                            {
                                detail: 'Biller Name',
                                value: formData.biller,
                                error: inlineErrors.biller,
                            },
                            {
                                detail: 'Account Name',
                                value: username,
                                error: inlineErrors.username,
                            },
                            {
                                detail: 'Payment Type',
                                value: formData.paymentType,
                                error: inlineErrors.paymentType,
                            },
                            {
                                detail: 'Meter Number',
                                value: formData.meterNumber,
                                error: inlineErrors.meterNumber,
                            },
                            {
                                detail: 'Amount to pay',
                                value: `₦${formData.amount}`,
                                error: inlineErrors.amount,
                            },
                        ]}
                    />
                )}
            </div>
        </div>
    )
}
