import { type NextRequest, NextResponse } from 'next/server'
import {
    BASE_URL,
    isValidSession,
    SESSION_KEY,
    sessionDecoder,
} from './lib/common'
import { setSession } from './lib/auth/server/session'
import { cookies } from 'next/headers'

export async function middleware(request: NextRequest) {
    const url = request.nextUrl
    const session = (await cookies()).get(SESSION_KEY)
    const decodedSession = session?.value ? sessionDecoder(session.value) : null
    const validSession = isValidSession(decodedSession)
    const cookieStore = await cookies()

    //Route user to dashboard if they are already login
    if (
        validSession &&
        (url.pathname.startsWith('/signin') ||
            url.pathname.startsWith('/signup'))
    ) {
        return NextResponse.redirect(new URL('/dashboard', request.url))
    }

    // GOOGLE AUTH MIDDLEWARE
    if (
        url.pathname.startsWith('/signin') ||
        url.pathname.startsWith('/signup')
    ) {
        try {
            // handle Google Auth
            const code = url.searchParams.get('code')
            const state = url.searchParams.get('state')
            if (code) {
                const decodedCode = decodeURIComponent(code || '')
                const response = await fetch(
                    `${BASE_URL}/api/auth/google/callback`,
                    {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ code: decodedCode, state }),
                    }
                )

                if (!response.ok) {
                    throw new Error('Failed to authenticate with Google')
                }

                const { data } = await response.json()
                const sessionPayload = {
                    user: {
                        id: data.user.id,
                        full_name: data.user.full_name,
                        email: data.user.email,
                        phonenumber: data.user?.phonenumber,
                    },
                    token: data.token,
                }
                await setSession(sessionPayload)
                return NextResponse.redirect(new URL('/dashboard', request.url))
            }
        } catch {
            return NextResponse.redirect(
                new URL(
                    '/signin?callback_error=google_auth_failed',
                    request.url
                )
            )
        }
    }
    // SESSION EXPIRED MIDDLEWARE
    if (request.nextUrl.pathname.startsWith('/dashboard')) {
        if (!validSession || !session?.value) {
            return NextResponse.redirect(
                new URL('/signin?callback_error=session_expired', request.url)
            )
        }

        // Decode the session and check if it's valid
        const decodedSession = sessionDecoder(session.value)
        if (!isValidSession(decodedSession)) {
            // Clear the invalid session cookie and redirect
            const response = NextResponse.redirect(
                new URL('/signin?callback_error=session_expired', request.url)
            )
            cookieStore.delete(SESSION_KEY)
            return response
        }

        if (decodedSession?.token?.accessExpiresIn) {
            try {
                const now = new Date().getTime()
                const expires = new Date(
                    decodedSession.token.accessExpiresIn
                ).getTime()

                // Check if token is expired or expiring soon (within 5 minutes)
                if (expires <= now || expires - now < 1000 * 60 * 5) {
                    const response = await fetch(
                        `${BASE_URL}/api/auth/refresh`,
                        {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                refreshToken: decodedSession.token.refreshToken,
                            }),
                        }
                    )

                    if (!response.ok) {
                        throw new Error('Failed to refresh token')
                    }

                    const { data } = await response.json()
                    decodedSession.token.accessToken = data.token.accessToken
                    decodedSession.token.accessExpiresIn =
                        data.token.accessExpiresIn
                    decodedSession.token.refreshToken = data.token.refreshToken
                    decodedSession.token.refreshExpiresIn =
                        data.token.refreshExpiresIn
                    await setSession(decodedSession)
                }
            } catch {
                // Clear the session cookie when refresh fails and redirect
                const response = NextResponse.redirect(
                    new URL(
                        '/signin?callback_error=session_expired',
                        request.url
                    )
                )
                cookieStore.delete(SESSION_KEY)
                return response
            }
        } else {
            // No access token expiration time, clear session and redirect
            const response = NextResponse.redirect(
                new URL('/signin?callback_error=session_expired', request.url)
            )
            cookieStore.delete(SESSION_KEY)
            return response
        }
    }

    // EMAIL VERIFICATION MIDDLEWARE
    if (request.nextUrl.pathname.startsWith('/verify-email')) {
        const email = request.nextUrl.searchParams.get('email')
        if (!email) {
            return NextResponse.redirect(new URL('/signin', request.url))
        }
    }

    // EMAIL VERIFICATION MIDDLEWARE
    if (request.nextUrl.pathname.startsWith('/email-verification')) {
        const email = request.nextUrl.searchParams.get('email')
        const token = request.nextUrl.searchParams.get('token')
        if (!email || !token) {
            return NextResponse.redirect(new URL('/signin', request.url))
        }

        try {
            // handle email verification
            const response = await fetch(
                `${BASE_URL}/api/auth/verify-email/${token}`,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            )

            if (!response.ok) {
                throw new Error('Failed to verify email')
            }

            const { data } = await response.json()

            // log the user in
            const sessionPayload = {
                user: {
                    id: data.user.id,
                    full_name: data.user.full_name,
                    email: data.user.email,
                },
                token: data.token,
            }
            await setSession(sessionPayload)
            return NextResponse.redirect(new URL('/dashboard', request.url))
        } catch {
            // do nothing
        }
    }

    return NextResponse.next()
}

export const config = {
    matcher: [
        '/dashboard',
        '/dashboard/:path*',
        '/signin',
        '/signup',
        '/verify-email',
        '/email-verification',
    ],
}
