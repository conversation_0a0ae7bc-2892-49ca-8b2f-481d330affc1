'use client'
import apiRequest from '@/lib/auth/client/request'
import { useSearchParams } from 'next/navigation'
import { enqueueSnackbar as notify } from 'notistack'
import { Suspense, useState } from 'react'

const ResendEmailButtonMain = () => {
    const searchParam = useSearchParams()
    const [loading, setLoading] = useState(false)

    const resendEmail = async () => {
        try {
            setLoading(true)
            const email = searchParam.get('email')
            if (!email) {
                notify('Something went wrong, please try again', {
                    variant: 'error',
                })
                return
            }
            const response = await apiRequest(false).post(
                '/api/auth/resend-verification-email',
                { email }
            )
            if (response.status === 200) {
                notify('Email sent successfully, please check your inbox.')
            }
        } catch {
            notify('Something went wrong, please try again', {
                variant: 'error',
            })
        } finally {
            setLoading(false)
        }
    }
    return (
        <>
            <button
                disabled={loading}
                onClick={resendEmail}
                className="max-w-2xl bg-primary text-white rounded-lg p-3 w-[90%] mx-auto disabled:opacity-50"
            >
                {loading ? 'Sending Email...' : 'Resend Email'}
            </button>
        </>
    )
}

export default function ResendEmailButton() {
    return (
        <Suspense>
            <ResendEmailButtonMain />
        </Suspense>
    )
}
