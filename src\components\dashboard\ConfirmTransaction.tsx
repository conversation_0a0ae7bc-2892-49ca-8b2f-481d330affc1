import React from 'react'

export default function ConfirmTransaction({
    details,
    runRequest,
    goBack,
    isSubmitting,
}: {
    details: {
        detail: string
        value: string
        error?: string
        hidden?: boolean
    }[]
    runRequest: () => void
    goBack: () => void
    isSubmitting: boolean
}) {
    return (
        <div className="flex flex-col gap-[2.75rem] max-w-sm mx-auto md:mx-0">
            <h2 className="border-b border-[#F7F7F7] text-[1.25rem] font-brico text-[#101828]">
                Confirm Transaction
            </h2>

            <div className="flex flex-col gap-5 md:gap-6">
                {details
                    .filter((detail) => !detail.hidden)
                    .map((detail, index) => (
                        <div key={`${detail.detail}${index}`}>
                            <div className="flex items-center gap-2">
                                <p className="text-[#475467] ">{`${detail.detail}:`}</p>
                                <p className="text-[#0C111D] font-medium">
                                    {detail.value}
                                </p>
                            </div>
                            {detail.error && (
                                <span className="text-sm text-red-500 font-bold">
                                    {detail.error}
                                </span>
                            )}
                        </div>
                    ))}
            </div>

            <div className="flex flex-col gap-1">
                <button
                    onClick={runRequest}
                    className="rounded-lg py-[.625rem] px-[.9375rem] w-full font-medium bg-primary text-white text-center flex justify-center"
                >
                    {isSubmitting ? (
                        <span>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="lucide lucide-loader-circle animate-spin"
                            >
                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                            </svg>
                        </span>
                    ) : (
                        <span>Confirm</span>
                    )}
                </button>
                <button
                    onClick={goBack}
                    className="rounded-lg py-[.625rem] px-[.9375rem] w-full font-medium text-primary"
                >
                    Go back
                </button>
            </div>
        </div>
    )
}
