interface User {
    id: number
    email: string
    full_name: string
    phonenumber?: string // Optional field for phone number
}

interface Token {
    accessToken: string
    refreshToken: string
    accessExpiresIn: string
    refreshExpiresIn: string
}

interface SessionData {
    user: User
    token: Token
}

interface SessionObject {
    isLoading: boolean
    session: SessionData | null
    isAuthenticated: boolean
}
