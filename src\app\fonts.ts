import { Be_Vietnam_Pro, Bricolage_Grotesque, Inter } from 'next/font/google'
import localFont from 'next/font/local'

// export const general = DM_Sans({
//     subsets: ['latin'],
//     variable: '--font-general',
// })
export const brico = Bricolage_Grotesque({
    subsets: ['latin'],
    variable: '--font-brico',
})
export const inter = Inter({ subsets: ['latin'], variable: '--font-inter' })
export const vietnam = Be_Vietnam_Pro({
    subsets: ['latin'],
    weight: ['400', '700'],
    variable: '--font-vietnam',
})

export const helv = localFont({
    src: '../../public/fonts/HelveticaNeueBlack.otf',
    variable: '--font-helvetica',
    weight: '100 200 300 400 500 600 700 800 900',
})

export const general = localFont({
    src: '../../public/fonts/GeneralSans-Regular.otf',
    variable: '--font-general',
    weight: '100 200 300 400 500 600 700 800 900',
})
