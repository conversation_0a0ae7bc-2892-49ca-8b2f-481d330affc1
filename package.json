{"name": "cip-topup", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "prepare": "husky"}, "dependencies": {"@dinero.js/currencies": "2.0.0-alpha.1", "@mantine/core": "^7.17.0", "@mantine/dates": "^7.17.7", "@mantine/hooks": "^7.17.0", "@tanstack/react-query": "^5.71.1", "aos": "^2.3.4", "axios": "^1.7.9", "dayjs": "^1.11.13", "dinero.js": "2.0.0-alpha.14", "framer-motion": "^12.0.6", "husky": "^9.1.7", "lucide-react": "^0.476.0", "next": "15.1.6", "next-themes": "^0.4.6", "notistack": "^3.0.2", "p-throttle": "^7.0.0", "react": "^19.0.0", "react-countdown": "^2.3.6", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "sharp": "^0.33.5", "swiper": "^11.2.2", "use-debounce": "^10.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/aos": "^3.0.7", "@types/axios": "^0.14.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "lint-staged": "^15.4.3", "postcss": "^8", "prettier": "^3.4.2", "tailwindcss": "^3.4.1", "typescript": "5.7.3"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "**/*.{json,md,yml,yaml}": ["prettier --write"]}}