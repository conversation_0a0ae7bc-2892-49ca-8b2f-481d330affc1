import React from 'react'
import Pagination from './Pagination'

export default function TableAndPagination({
    children,
    pagination,
    params,
}: {
    children: React.ReactNode
    pagination: Pagination
    params: { page?: string | undefined } | undefined
}) {
    return (
        <div className="gap-[3.125rem] flex flex-col w-full">
            <div className="overflow-x-auto text-[#182230] text-sm py-8 md:py-[44px] border-t border-[F5F5F5] text-left w-full">
                {children}
            </div>
            <Pagination pagination={pagination} paginationParams={params} />
        </div>
    )
}
