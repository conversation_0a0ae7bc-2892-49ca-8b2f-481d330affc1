import apiRequest from '@/lib/auth/server/request'
import TableAndPagination from '../TableAndPagination'
import {
    formatDate,
    getDisplayNairaAmount,
    getStatusColor,
    toTitleCase,
} from '@/utilities'

export default async function TableSection({ params }: { params: Param }) {
    const page = Number(params?.page) || 1
    const search = params?.search || ''
    const status = params?.status || ''
    const endDate = params?.endDate || ''
    const startDate = params?.startDate || ''
    const request = await apiRequest()
    let response
    try {
        response = await request.get(
            `/api/users/transactions/top-up?${page && `page_number=${page}&`}${search && `search=${search}&`}${status && `status=${status}&`}${startDate && `date_from=${startDate}&`}${endDate && `date_to=${endDate}&`}
            `
        )
    } catch {
        response = {
            data: {
                data: {
                    transactions: [],
                    pagination: {
                        total_items: 0,
                        total_pages: 1,
                        current_page: 1,
                        page_size: 1,
                        has_next: false,
                        has_previous: false,
                    },
                },
            },
        }
    }

    const { data } = response.data

    const {
        transactions,
        pagination,
    }: { transactions: Transaction[]; pagination: Pagination } = data
    return (
        <TableAndPagination pagination={pagination} params={params}>
            {transactions.length > 0 ? (
                <table className="table-auto w-full border-separate">
                    <thead>
                        <tr className="border-b border-[#EAECF0] flex rounded-t-lg bg-[#F2F4F7]">
                            <th className="w-full p-4 min-w-16">Service</th>
                            <th className="w-full p-4 min-w-12">Amount</th>
                            <th className="w-full p-4 min-w-24">
                                Date and Time
                            </th>
                            <th className="w-full p-4 min-w-16">Status</th>
                            <th className="w-full p-4 min-w-48">Remarks</th>
                        </tr>
                    </thead>
                    <tbody>
                        {transactions.length > 0 &&
                            transactions.map((network) => {
                                const status = network?.status.toLowerCase()
                                const color = getStatusColor(status)
                                return (
                                    <tr
                                        key={network.id}
                                        className="border-b border-[#EAECF0] flex"
                                    >
                                        <td className="w-full p-4 min-w-16">
                                            {toTitleCase(network.source)}
                                        </td>
                                        <td className="w-full p-4 min-w-12">
                                            {getDisplayNairaAmount(
                                                network.amount
                                            )}
                                        </td>
                                        <td className="w-full p-4 min-w-24">
                                            {formatDate(network.date_created)}
                                        </td>
                                        <td
                                            className="w-full p-4 min-w-16"
                                            style={{ color: color }}
                                        >
                                            {toTitleCase(network.status)}
                                        </td>
                                        <td className="w-full p-4 min-w-48">
                                            {toTitleCase(network.remarks)}
                                        </td>
                                    </tr>
                                )
                            })}
                    </tbody>
                </table>
            ) : (
                <div className="flex items-center justify-center">
                    <p className="text-gray-500 font-bold text-xl">{`You don't have a top-up history`}</p>
                </div>
            )}
        </TableAndPagination>
    )
}
