import { notFound } from 'next/navigation'

export default async function Check(props: {
    searchParams?: Promise<{
        email?: string
    }>
}) {
    const searchParams = await props?.searchParams
    const email = searchParams?.email || null
    if (!email) {
        return notFound()
    }
    return (
        <div className="flex md:max-w-[86%] mx-auto gap-8 flex-col justify-center items-center h-full">
            <div className="flex flex-col gap-[1.2rem] text-center font-general">
                <h2 className="font-brico text-[1.875rem] font-bold">
                    Check Your Mail
                </h2>
                <p className="text-[#182230] text-[1.25rem]">
                    We&apos;ve sent an email to
                    <span className="font-semibold text-primary">
                        {' '}
                        {email}{' '}
                    </span>
                    with a link to reset your password.
                    <br />
                    Please check your inbox (and spam folder) to gain access.
                </p>
            </div>
        </div>
    )
}
