'use client'
import { useForm } from 'react-hook-form'

interface Form {
    email: string
}

export default function StayInLoop() {
    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<Form>()

    const onSubmit = (data: Form) => {
        console.log(data)
    }

    return (
        <div className="px-4 py-[4.125rem] max-w-6xl mx-auto gap-11 md:gap-[2.875rem] mb-[4.125rem] md:mb-[6.25rem] flex flex-col items-center">
            <div
                className="flex flex-col items-center max-w-xl gap-4 mx-auto text-center md:gap-3"
                data-aos="fade-up"
            >
                <h2
                    className={`font-brico font-semibold text-[1.75rem] leading-[2.0625rem] md:text-[2.75rem] md:leading-[3.25rem] md:text-[#101828]`}
                >
                    Stay in the Loop!
                </h2>
                <p className="leading-[1.3125rem] text-[#98A2B3] md:text-[1.5rem] md:leading-[2rem] md:text-[#475467]">
                    Get exclusive updates, deals, and tips delivered straight to
                    your inbox
                </p>
            </div>
            {/*  */}
            <form
                method="POST"
                onSubmit={handleSubmit(onSubmit)}
                className="flex flex-col max-w-lg gap-4 md:flex-row md:items-start"
            >
                <div className="gap-[.375rem] flex flex-col">
                    <input
                        type="text"
                        {...register('email', {
                            required: {
                                value: true,
                                message: 'Your Email is needed',
                            },
                            pattern: {
                                value: /^\S+@\S+$/i,
                                message: 'Please enter a valie email',
                            },
                        })}
                        placeholder="Enter your email"
                        className="w-full border border-[#D0D5DD] px-4 py-[.875rem] rounded-lg placeholder:text-[#667085] outline-0 focus:outline focus:outline-white"
                    />
                    {errors?.email && (
                        <span className="text-sm text-[#475467]">
                            {errors.email.message}
                        </span>
                    )}
                    <p className="text-[#475467] text-[.875rem] leading-[1.125rem]">
                        We value your privacy—no spam, just value-packed content
                    </p>
                </div>
                <button
                    type="submit"
                    className={`font-vietnam font-semibold leading-[24px] px-[1.125rem] py-[.875rem] rounded-lg bg-primary text-white`}
                >
                    Subscribe
                </button>
            </form>
        </div>
    )
}
