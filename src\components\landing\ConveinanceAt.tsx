import Image from 'next/image'
import Link from 'next/link'

export default function ConveinenceAt() {
    return (
        <div className="px-4 py-[4.125rem] max-w-6xl mx-auto pb-[4.125rem] md:pb-[6.25rem]">
            <div
                className="rounded-[1.5rem] md:rounded-[2rem] bg-primary justify-between md:items-end gap-8 flex flex-col md:flex-row overflow-hidden"
                data-aos="fade-up"
            >
                <div
                    className="text-center md:text-left px-[6px] pt-[3.5625rem] md:p-[1.6rem] lg:p-[3.5rem] flex flex-col gap-8 md:gap-11 items-center md:items-stretch md:basis-3/5"
                    data-aos="fade-up"
                >
                    <div className="flex flex-col gap-2">
                        <h2
                            className={`font-brico text-[2.375rem] leading-[2.8125rem] md:text-[3.75rem] md:leading-[4.125rem] font-bold text-white`}
                        >
                            Convenience a
                            <span className="relative">
                                t
                                <Image
                                    src="/img/grass.svg"
                                    alt=""
                                    width={39}
                                    height={29}
                                    className="absolute top-0 -translate-y-1/2 left-0 translate-x-1/4 w-[2.4375rem] h-auto"
                                />
                            </span>{' '}
                            Your Fingertips
                        </h2>
                        <p className="text-white text-[1.25rem] leading-[1.6875rem] md:text-[1.5rem] md:leading-[2rem] ">
                            Download the App now and enjoy <br /> Simplicity in
                            Life!
                        </p>
                    </div>
                    <div className="flex items-center gap-6">
                        {[
                            {
                                img: '/img/googleStore.svg',
                                href: 'https://play.google.com/store/apps/details?id=com.ciptopup',
                            },
                            {
                                img: '/img/appleStore.svg',
                                href: 'https://apps.apple.com/ng/app/ciptopup-app/id1600469000',
                            },
                        ].map((link) => (
                            <Link
                                target="_blank"
                                href={link.href}
                                key={link.img}
                            >
                                <Image
                                    src={link.img}
                                    alt=""
                                    height={48}
                                    width={135}
                                    className="max-w-full w-auto h-[2.75rem] md:h-[3rem]"
                                />
                            </Link>
                        ))}
                    </div>
                </div>
                <Image
                    alt="mock up"
                    height={464}
                    width={438}
                    src="/img/landingMockup.png"
                    className="relative w-full h-auto max-w-full left-1/4 md:left-auto md:max-h-full md:w-auto md:h-full md:max-w-auto md:min-h-[410px]"
                    data-aos="fade-up"
                />
            </div>
        </div>
    )
}
