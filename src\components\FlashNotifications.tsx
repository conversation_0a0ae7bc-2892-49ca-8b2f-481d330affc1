'use client'
import { useDisclosure } from '@mantine/hooks'
import { Modal } from '@mantine/core'
import { useEffect, useState } from 'react'
import Image from 'next/image'
import apiRequest from '@/lib/auth/client/request'
import * as crypto from 'crypto'

interface FlashNotification {
    id: string
    message: string
    active: boolean
    date_created: string
    date_updated: string
}

const FlashNotifications = () => {
    const [opened, { open, close }] = useDisclosure(false)
    const [notifications, setNotifications] = useState<FlashNotification[]>([])

    useEffect(() => {
        ;(async () => {
            try {
                const lastNotification =
                    localStorage.getItem('notificationHash')
                const response = await apiRequest().get(
                    '/api/users/notifications/flash'
                )
                const { data } = response.data
                setNotifications(data)
                const notificationHash = crypto
                    .createHash('sha256')
                    .update(JSON.stringify(data))
                    .digest('hex')
                if (data.length > 0) {
                    if (lastNotification !== notificationHash) {
                        open()
                    }
                }
            } catch {
                // do nothing
            }
        })()
    }, [open])

    const suppressNotifications = () => {
        const notificationHash = crypto
            .createHash('sha256')
            .update(JSON.stringify(notifications))
            .digest('hex')
        localStorage.setItem('notificationHash', notificationHash)
        close()
    }
    return (
        <>
            <Modal
                size="lg"
                opened={opened}
                onClose={close}
                withCloseButton={false}
                centered
            >
                <div className="p-4">
                    <Image
                        src="/icons/notification.png"
                        alt="Flash Notification"
                        width={32}
                        height={32}
                        className="mx-auto bell"
                    />
                    <div className="flex flex-col gap-3 my-8">
                        {notifications?.map((note) => (
                            <div
                                key={note.id}
                                className="p-4 border rounded-md"
                            >
                                <p className="font-inter">{note.message}</p>
                            </div>
                        ))}
                    </div>

                    <div>
                        <button
                            className="w-full bg-primary text-white py-2 rounded-md hover:bg-blue-600 transition-colors"
                            onClick={suppressNotifications}
                        >
                            Ok, I understand
                        </button>
                    </div>
                </div>
            </Modal>
        </>
    )
}

export default FlashNotifications
