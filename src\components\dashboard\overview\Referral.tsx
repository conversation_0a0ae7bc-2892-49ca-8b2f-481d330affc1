import { ArrowRight, Gift } from 'lucide-react'
import Link from 'next/link'
import React from 'react'

export const ReferralBanner = () => {
    return (
        <div className="bg-white rounded-xl shadow-sm border relative overflow-hidden">
            <div className="absolute inset-0 rounded-xl overflow-hidden">
                {/* Moving Lightning Effect */}
                <div className="absolute inset-0 rounded-xl">
                    <div className="absolute w-2 h-2 bg-blue-400 rounded-full animate-lightning-1 shadow-lg shadow-blue-400/50"></div>
                    <div className="absolute w-1.5 h-1.5 bg-cyan-400 rounded-full animate-lightning-2 shadow-lg shadow-cyan-400/50"></div>
                    <div className="absolute w-1 h-1 bg-blue-300 rounded-full animate-lightning-3 shadow-lg shadow-blue-300/50"></div>
                </div>
            </div>
            <div className="p-6 relative ">
                <div className="flex items-start justify-between">
                    <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-3">
                            <div className="bg-blue-100 p-2 rounded-lg">
                                <Gift className="w-6 h-6 text-primary" />
                            </div>
                            <span className="text-primary font-semibold text-sm">
                                Referral Bonus
                            </span>
                        </div>

                        <h3 className="text-xl font-bold text-gray-900 mb-2 font-brico">
                            Earn Big When Your Friends Join!
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">
                            Share your referral code and unlock instant rewards
                            when your friends sign up and start using our
                            services.
                        </p>

                        <Link
                            href="/dashboard/referral"
                            className="bg-primary text-white px-6 py-3 rounded-lg items-center space-x-2 hover:opacity-75 transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md inline-flex"
                        >
                            <span className="font-medium">Start earning</span>
                            <ArrowRight className="w-4 h-4" />
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    )
}
