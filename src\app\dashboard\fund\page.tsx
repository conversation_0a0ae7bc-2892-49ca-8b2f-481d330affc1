import DashboardPagesShell from '@/components/dashboard/DashboardPagesShell'
import ActionForm from '@/components/dashboard/fund/ActionForm'
import { fetchWalletData } from './server'

export default async function FundWallet() {
    const walletData = await fetchWalletData()
    return (
        <DashboardPagesShell
            innerComponentContent="Fund your wallet"
            innerComponentHeading="Fund wallet"
        >
            <ActionForm staticAccountsData={walletData} />
        </DashboardPagesShell>
    )
}
