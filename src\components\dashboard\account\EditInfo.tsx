'use client'
import Image from 'next/image'
import { useEffect, useState } from 'react'

export default function EditInfo({
    name,
    children,
    revertEditInput,
}: {
    name: string | number
    children: React.ReactNode
    revertEditInput: boolean
}) {
    const [showInput, setShowInput] = useState<boolean>(false)
    useEffect(() => {
        setShowInput(false)
    }, [revertEditInput])
    return showInput ? (
        <div className="flex items-center gap-2 max-w-[55%]">
            <div
                className="w-5 cursor-pointer"
                onClick={() => setShowInput(false)}
            >
                <Image
                    height={20}
                    width={20}
                    alt="edit"
                    src="/icons/cancel.svg"
                    className="w-full h-auto max-w-full"
                />
            </div>
            {children}
        </div>
    ) : (
        <div className="flex gap-2 items-center">
            <p className="text-[#667085] break-words">{name}</p>{' '}
            <div
                className="w-5 cursor-pointer"
                onClick={() => setShowInput(true)}
            >
                <Image
                    height={20}
                    width={20}
                    alt="edit"
                    src="/icons/edit.svg"
                    className="w-full h-auto max-w-full"
                />
            </div>
        </div>
    )
}
