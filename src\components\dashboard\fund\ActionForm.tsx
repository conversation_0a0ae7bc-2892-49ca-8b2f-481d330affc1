'use client'
import { useForm } from 'react-hook-form'
import React, { useEffect, useState } from 'react'
import { FormData, FormData1 } from './type'
import InsertInput from '@/components/inputs/InsertInput'
import apiRequest from '@/lib/auth/client/request'
import { handleError } from '@/lib/error'
import { enqueueSnackbar as notify } from 'notistack'
import Link from 'next/link'
import Input from '@/components/inputs/Input'
import { getDisplayNairaAmount } from '@/utilities'
import { useRouter } from 'next/navigation'
import AccountExpiryWarning from './AccountExpiryWarning'
import { Modal } from '@mantine/core'
import { CheckCircle } from 'lucide-react'
import { useDisclosure } from '@mantine/hooks'
import pThrottle from 'p-throttle'
import StaticAccounts from '../overview/StaticAccounts'

interface FetchedData {
    email: string
    full_name: string
    phonenumber: string
}

const throttle = pThrottle({
    limit: 10,
    interval: 60000,
})

export default function ActionForm({
    staticAccountsData,
}: {
    staticAccountsData: WalletData
}) {
    const [section, setSection] = useState<'default' | 'create' | 'pay'>(
        'default'
    )
    const [amountToPay, setAmountToPay] = useState('')
    const [userData, setUserData] = useState<FetchedData>({
        email: '',
        full_name: '',
        phonenumber: '',
    })
    const [walletData, setWalletData] = useState<WalletAccountData>({
        account_name: '',
        account_number: '',
        bank_code: '',
        bank_name: '',
        provider: '',
    })

    const router = useRouter()

    const {
        register,
        handleSubmit,
        reset,
        setValue,
        getValues,
        watch,
        setError,
        trigger,
        formState: { errors, isSubmitting },
    } = useForm<FormData>()

    const {
        register: register1,
        handleSubmit: handleSubmit1,
        setError: setError1,
        formState: { errors: errors1, isSubmitting: isSubmitting1 },
    } = useForm<FormData1>()

    const runPayment = (data: WalletAccountData) => {
        setSection('pay')
        setWalletData(data)
        setTrackPayment(true)
        reset()
    }
    const [trackPayment, setTrackPayment] = useState(false)
    const [opened, { open, close }] = useDisclosure(false)
    const [isSubmittingTrigger, setIsSubmittingTrigger] = useState(false)
    //Get User Virtual Account and direct user to create one if they don't have one
    const onSubmit = async (data: FormData) => {
        setAmountToPay(data.amount)
        try {
            const response = await apiRequest().get(
                `/api/bank/virtual-account?amount=${parseInt(data.amount) * 100}`
            )
            const { data: respData }: { data: WalletAccountData } =
                response.data
            if (respData?.account_number) {
                runPayment(respData)
            } else {
                //fetch user detail to populate input field
                try {
                    const response =
                        await apiRequest().get('/api/users/profile')
                    const { data } = response.data
                    setUserData(data)
                    setSection('create')
                } catch (err: unknown) {
                    const error: ApiWithError = handleError(err)
                    if (error.message && !error.errors) {
                        notify(error.message)
                    }
                }
            }
        } catch (err: unknown) {
            const error: ApiWithFormError<FormData> = handleError(err)
            if (error.errors) {
                error.errors.forEach((e) => {
                    setError(e.path, {
                        message: e.message,
                    })
                })
            }
            if (error.message && !error.errors) {
                notify(error.message)
            }
        }
    }

    // Create user virtual account
    const onSubmit1 = async (data: FormData1) => {
        const formData = {
            name: data.name,
            phone_number: data.phone_number,
            provider: 'PAYMENTPOINT',
        }
        try {
            const request = await apiRequest().post(
                '/api/bank/virtual-account',
                formData
            )
            const { data }: { data: WalletAccountData } = request.data
            if (data?.account_number) {
                runPayment(data)
            }
        } catch (err: unknown) {
            const error: ApiWithFormError<FormData1> = handleError(err)
            if (error.errors) {
                error.errors.forEach((e) => {
                    setError1(e.path, {
                        message: e.message,
                    })
                })
            }
            if (error.message && !error.errors) {
                notify(error.message)
            }
        }
    }

    // Wrap the original confirmPayment function with throttling
    const cPayment = throttle(async (triggered = false) => {
        try {
            setIsSubmittingTrigger(true)
            const response = await apiRequest().get(
                `/api/bank/check-transfer?reference=${walletData.reference}`
            )

            const { data } = response.data
            const { status, message } = data

            if (status === 'success') {
                open()
                setSection('default')
                setTrackPayment(false)
                setTimeout(() => {
                    close()
                    router.push('/dashboard')
                }, 5000)
            }

            if (status === 'pending') {
                if (triggered) {
                    notify(
                        'We are currently confirming your transaction. Please wait..'
                    )
                }
            }

            if (status === 'failed') {
                if (message.includes('pending')) {
                    if (triggered) {
                        notify(
                            'We are currently confirming your transaction. Please wait..'
                        )
                    }
                    return
                }

                notify(
                    'Payment failed. Please do not send money to the old account',
                    {
                        variant: 'error',
                    }
                )
                setTrackPayment(false)
                setSection('default')
            }
        } catch (err: unknown) {
            const error: ApiWithError = handleError(err)
            if (error.message && !error.errors) {
                notify(error.message)
            }
        } finally {
            setIsSubmittingTrigger(false)
        }
    })

    // Add a wrapper function that silently ignores rate limit exceeded errors
    const confirmPayment = React.useCallback(
        async (triggered = false) => {
            try {
                await cPayment(triggered)
            } catch {}
        },
        [cPayment]
    )

    useEffect(() => {
        // confirm payment every 5 seconds once there is a reference
        const interval = setInterval(() => {
            if (walletData.reference && trackPayment) {
                confirmPayment()
            }
        }, 10000)
        return () => clearInterval(interval)
    }, [walletData.reference, trackPayment, confirmPayment])

    return (
        <div className="h-full">
            <div className="max-w-[37rem] border border-[#EAECF0] rounded-xl [&_.grid]:lg:grid-cols-2">
                <StaticAccounts staticAccountsData={staticAccountsData} />
            </div>
            <div className="flex items-center justify-center text-[#667085] text-[1.25rem] md:text-[1.5rem] font-medium my-6 max-w-[37rem]">
                <p>OR</p>
            </div>

            <div className="max-w-[37rem] border border-[#EAECF0] rounded-xl flex flex-col">
                <Modal
                    opened={opened}
                    onClose={close}
                    withCloseButton={false}
                    centered
                >
                    <div className="w-full max-w-md bg-white overflow-hidden p-6">
                        <div className="flex flex-col items-center text-center">
                            <div className="mb-4 bg-green-100 p-3 rounded-full">
                                <CheckCircle className="h-12 w-12 text-green-600" />
                            </div>

                            <h1 className="text-2xl font-bold text-gray-800 mb-2">
                                Payment Successful!
                            </h1>
                            <p className="text-gray-600 mb-6">
                                Thank you for your payment. Your transaction has
                                been completed successfully.
                            </p>
                        </div>
                    </div>
                </Modal>
                {section === 'default' && (
                    <div className="p-8">
                        <h2 className="text-[1.125rem] font-medium">
                            Generate one-time account number
                        </h2>
                        <p className="text-[#667085] text-sm py-2">
                            Fill the form below to generate an account number
                        </p>

                        <div className="flex flex-col gap-4 border-t border-[#EAECF0] pt-6">
                            <form
                                method="POST"
                                onSubmit={handleSubmit(onSubmit)}
                                className="flex flex-col gap-[2.75rem] w-full"
                            >
                                <div className="flex flex-col gap-5 md:gap-7">
                                    <InsertInput
                                        trigger={trigger}
                                        label="Amount"
                                        placeholder="Enter amount"
                                        registerName="amount"
                                        register={register}
                                        errors={errors}
                                        setValue={setValue}
                                        getValues={getValues}
                                        watch={watch}
                                        inputOptions={[
                                            { option: '₦5,000', value: 5000 },
                                            { option: '₦10,000', value: 10000 },
                                            { option: '₦15,000', value: 15000 },
                                            { option: '₦20,000', value: 20000 },
                                        ]}
                                        validation={{
                                            required: {
                                                value: true,
                                                message: `Amount is required`,
                                            },
                                        }}
                                    />
                                </div>

                                <div>
                                    <button
                                        type="submit"
                                        className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem]`}
                                    >
                                        {isSubmitting ? (
                                            <svg
                                                className="animate-spin h-5 w-5 mr-3 border-white border-t-2 border-b-2 border-r-2 border-[rgba(255,255,255,0.2)] rounded-full"
                                                viewBox="0 0 24 24"
                                            />
                                        ) : null}
                                        Proceed to Payment
                                    </button>
                                </div>
                            </form>
                            <p className="text-[#475467] text-center">
                                Need help?{' '}
                                <Link
                                    href="/contact"
                                    className="text-primary font-medium"
                                >
                                    Contact support
                                </Link>
                            </p>
                        </div>
                    </div>
                )}
                {section === 'create' && (
                    <>
                        <h2 className="text-[#667085] text-[1.5rem] md:text-[2rem] text-center leading-normal md:leading-[2.7rem] p-6 border-b border-[#EAECF0]">
                            Create Payment Account
                        </h2>
                        <div className="p-5 md:p-[3.5rem] flex flex-col gap-4">
                            <p className="text-lg mb-4">{`You don't have a payment account, you'll need to generate a payment account to proceed.`}</p>
                            <form
                                method="POST"
                                onSubmit={handleSubmit1(onSubmit1)}
                                className="flex flex-col gap-[2.75rem] md:max-w-md w-full mx-auto md:mx-0"
                            >
                                <div className="flex flex-col gap-5 md:gap-7">
                                    <Input<FormData1>
                                        type={'text'}
                                        label={'Full Name'}
                                        registerName={'name'}
                                        placeholder={'Enter your full name'}
                                        defaultValue={userData.full_name}
                                        register={register1}
                                        errors={errors1}
                                        validation={{
                                            required: {
                                                value: true,
                                                message: `Your full name is required`,
                                            },
                                        }}
                                    />
                                    <Input<FormData1>
                                        type={'tel'}
                                        label={'Phone Number'}
                                        registerName={'phone_number'}
                                        placeholder={'Enter your phone number'}
                                        defaultValue={userData.phonenumber}
                                        register={register1}
                                        errors={errors1}
                                        validation={{
                                            required: {
                                                value: true,
                                                message: `Phone number is required`,
                                            },
                                            pattern: {
                                                value: /^(?:\+?234|0)[789][01]\d{8}$/,
                                                message:
                                                    'Please enter a valid Nigerian phone number',
                                            },
                                        }}
                                    />
                                </div>

                                <div className="flex flex-col gap-4">
                                    <button
                                        type="submit"
                                        className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem]`}
                                    >
                                        {isSubmitting1 ? (
                                            <svg
                                                className="animate-spin h-5 w-5 mr-3 border-white border-t-2 border-b-2 border-r-2 border-[rgba(255,255,255,0.2)] rounded-full"
                                                viewBox="0 0 24 24"
                                            />
                                        ) : null}
                                        Generate Account
                                    </button>
                                </div>
                            </form>
                            <p className="text-[#475467] text-center">
                                Need help?{' '}
                                <Link
                                    href="/contact"
                                    className="text-primary font-medium"
                                >
                                    Contact support
                                </Link>
                            </p>
                        </div>
                    </>
                )}
                {section === 'pay' && (
                    <>
                        <h2 className="text-[#667085] text-[1.5rem] md:text-[2rem] text-center leading-normal md:leading-[2.7rem] p-6 border-b border-[#EAECF0]">
                            Make Payment
                        </h2>
                        <div className="p-5 md:p-[3.5rem] flex flex-col gap-4">
                            <p className="text-lg mb-4">{`Make a deposit/bank transfer to the account below to fund your wallet`}</p>
                            <div className="flex flex-col gap-[2.75rem] max-w-md mx-auto md:mx-0 w-full">
                                <div className="flex flex-col gap-5 md:gap-7">
                                    {[
                                        {
                                            heading: 'Account Number',
                                            value: walletData.account_number,
                                            size: 'text-2xl',
                                        },
                                        {
                                            heading: 'Bank Name',
                                            value: walletData.bank_name,
                                            size: 'text-xl',
                                        },
                                        {
                                            heading: 'Account Name',
                                            value: walletData.account_name,
                                            size: 'text-lg',
                                        },
                                        {
                                            heading: 'Amount to transfer',
                                            value: getDisplayNairaAmount(
                                                parseInt(amountToPay) * 100
                                            ),
                                            size: '',
                                        },
                                    ].map((bank) => (
                                        <p
                                            key={bank.heading}
                                            className={`text-center text-[#667085] ${bank.size}`}
                                        >
                                            {bank.value}
                                        </p>
                                    ))}
                                </div>

                                <p className="text-[#475467] text-center -mb-7">
                                    {walletData.expires_at ? (
                                        <AccountExpiryWarning
                                            expires_at={walletData.expires_at}
                                        />
                                    ) : (
                                        <span>
                                            Payment made to this account reflect
                                            in your wallet balance after a few
                                            seconds automatically
                                        </span>
                                    )}
                                </p>

                                <div className="flex flex-col gap-4">
                                    {walletData.expires_at ? (
                                        <button
                                            disabled={isSubmittingTrigger}
                                            type="button"
                                            className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem]`}
                                            onClick={() => {
                                                setTrackPayment(true)
                                                confirmPayment(true)
                                            }}
                                        >
                                            {isSubmittingTrigger ? (
                                                <svg
                                                    className="animate-spin h-5 w-5 mr-3 border-white border-t-2 border-b-2 border-r-2 border-[rgba(255,255,255,0.2)] rounded-full"
                                                    viewBox="0 0 24 24"
                                                />
                                            ) : null}
                                            I have made payment
                                        </button>
                                    ) : (
                                        <button
                                            className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem]`}
                                            onClick={() =>
                                                router.push('/dashboard')
                                            }
                                        >
                                            I understand
                                        </button>
                                    )}
                                </div>
                            </div>
                            <p className="text-[#475467] text-center">
                                Need help?{' '}
                                <Link
                                    href="/contact"
                                    className="text-primary font-medium"
                                >
                                    Contact support
                                </Link>
                            </p>
                        </div>
                    </>
                )}
            </div>
        </div>
    )
}
