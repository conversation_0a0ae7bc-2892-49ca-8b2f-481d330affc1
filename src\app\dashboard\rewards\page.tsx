import DashboardPagesShell from '@/components/dashboard/DashboardPagesShell'
import TableSection from '@/components/dashboard/rewards/TableSection'
import apiRequest from '@/lib/auth/server/request'
import Image from 'next/image'
import Link from 'next/link'

export default async function RewardsPage() {
    const request = await apiRequest()
    const { data } = await request.get('/api/rewards').then((response) => {
        return response.data
    })

    const { leaderboard: users, summary } = data
    return (
        <DashboardPagesShell
            innerComponentContent="Celebrate active users and encourage competition."
            innerComponentHeading="Rewards Leaderboard"
        >
            <div className="font-general">
                <div className="bg-[#F0F5FF] p-5 rounded-3xl">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                        <div className="flex flex-col items-center justify-center bg-white p-6 rounded-xl">
                            <div className="flex gap-3 items-center">
                                <Image
                                    src="/icons/points.svg"
                                    alt="Your points"
                                    width={44}
                                    height={44}
                                />
                                <p className="text-[1rem]">Your Points</p>
                            </div>
                            <p className="text-[#090C0C] text-[2.5rem] md:text-[2.75rem] font-semibold">
                                {summary.points}
                            </p>
                        </div>

                        <div className="flex flex-col items-center justify-center bg-white p-6 rounded-xl">
                            <div className="flex gap-3 items-center">
                                <Image
                                    src="/icons/award.svg"
                                    alt="Your position"
                                    width={44}
                                    height={44}
                                />
                                <p className="text-[1rem]">Your Position</p>
                            </div>
                            <p className="text-[#090C0C] text-[2.5rem] md:text-[2.75rem] font-semibold">
                                {summary.rank}
                            </p>
                        </div>
                    </div>
                </div>
                <div className="mt-10 bg-[#F0F5FF] p-3 rounded-[6.19rem]">
                    <Link
                        href="/dashboard/rewards/instructions"
                        role="button"
                        className="items-center justify-center gap-2.5 text-white bg-primary hover:opacity-75 rounded-full px-8 py-4 inline-flex"
                    >
                        <span>Reward Instructions</span>
                        <Image
                            src="/icons/arrow-right.svg"
                            alt="Arrow right"
                            width={20}
                            height={20}
                        />
                    </Link>
                </div>
                {users.length > 0 && (
                    <div className="mt-10">
                        <TableSection users={users} />
                    </div>
                )}
            </div>
        </DashboardPagesShell>
    )
}
