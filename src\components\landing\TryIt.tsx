'use client'
import { AnimatePresence, motion } from 'framer-motion'
import { ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { useState } from 'react'

export default function TryIt({
    href,
    color,
}: {
    href: string
    color: string
}) {
    const [hovering, setHovering] = useState(false)
    return (
        <>
            <Link href={href}>
                <motion.button
                    whileTap={{ scale: 0.9 }}
                    onTapStart={() => {
                        setHovering(true)
                        setTimeout(() => setHovering(false), 800)
                    }}
                    whileHover={{ backgroundColor: color, color: 'white' }}
                    onHoverStart={() => setHovering(true)}
                    onHoverEnd={() => setHovering(false)}
                    className="py-[.375rem] px-3 rounded-full gap-1 flex items-center"
                    style={{
                        color: color,
                        border: `1px solid ${color}`,
                    }}
                >
                    <span>Try it now</span>
                    <AnimatePresence>
                        {hovering && (
                            <motion.div
                                initial={{ opacity: 0.5, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                exit={{ opacity: 0.5, width: 0 }}
                                transition={{ duration: 0.4 }}
                                className="w-6"
                            >
                                {/* <Image
                            src={service.tryIcon}
                            alt="try now"
                            height={24}
                            width={24}
                            className="w-full h-auto max-w-full"
                        /> */}
                                <ArrowRight size={24} className="text-white" />
                            </motion.div>
                        )}
                    </AnimatePresence>
                </motion.button>
            </Link>

            {/* <Link to={link} className="hover:opacity-100!">
                <motion.button
                    whileTap={{ scale: 0.9 }}
                    onTapStart={() => {
                        setHovering(true)
                        setTimeout(() => setHovering(false), 800)
                    }}
                    onHoverStart={() => setHovering(true)}
                    onHoverEnd={() => setHovering(false)}
                    className="py-1.5 px-2 bg-primary text-BG rounded-md text-xs flex items-center gap-2"
                >
                    <ArrowBigRightDash
                        size={25}
                        className="text-BG z-1 bg-primary"
                    />
                    <AnimatePresence>
                        {hovering && (
                            <motion.span
                                initial={{ opacity: 0.5, x: 10 }}
                                animate={{ opacity: 1, x: 0 }}
                                exit={{ opacity: 0.5, width: 0 }}
                            >
                                VIEW
                            </motion.span>
                        )}
                    </AnimatePresence>
                </motion.button>
            </Link> */}
        </>
    )
}
