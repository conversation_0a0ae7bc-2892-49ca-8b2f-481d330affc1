'use client'
import { useSession } from '@/lib/auth/client/session'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState, useRef, useEffect } from 'react'

export default function Header() {
    const [showDrop, setShowDrop] = useState(false)
    const burger = useRef<HTMLImageElement | null>(null)
    const drop = useRef<HTMLDivElement | null>(null)
    const path = usePathname()
    const { isAuthenticated, isLoading } = useSession()

    const linksArray = isAuthenticated
        ? [
              { name: 'Home', link: '/' },
              { name: 'Contact', link: '/contact' },
              { name: 'Airtime', link: '/dashboard/airtime-top-up' },
              { name: 'Data', link: '/dashboard/data-top-up' },
          ]
        : [
              { name: 'Home', link: '/' },
              { name: 'Contact', link: '/contact' },
          ]

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            const target = e.target as Node
            if (
                burger.current &&
                drop.current &&
                !burger.current.contains(target) &&
                !drop.current.contains(target)
            ) {
                setShowDrop(false)
            }
        }

        document.body.addEventListener('click', handleClickOutside)
        return () => {
            document.body.removeEventListener('click', handleClickOutside)
        }
    }, [])

    return (
        <div className="top-0 z-20 py-3 bg-white md:sticky">
            <div className="max-w-4xl mx-auto md:px-4">
                <div className=" relative md:rounded-[6.25rem] md:border-[.0313rem] md:border-[#A9A9A9] py-4 px-4 md:px-5 flex items-center gap-5 justify-between">
                    <Link href="/" className="w-[5.5rem]">
                        <Image
                            alt="Logo"
                            src="/img/logo.png"
                            width={85.6}
                            height={32}
                            className="w-full h-auto"
                        />
                    </Link>
                    <div className="hidden md:flex gap-[1.5rem] items-center">
                        {linksArray.map((link) => (
                            <Link
                                key={link.name}
                                href={link.link}
                                className={`font-vietnam leading-[1.265rem] ${
                                    path === link.link
                                        ? 'text-black'
                                        : 'text-[#475467]'
                                } `}
                            >
                                {link.name}
                            </Link>
                        ))}
                    </div>
                    <div className="hidden md:flex gap-[1.5rem] items-center">
                        {isLoading
                            ? null
                            : !isAuthenticated && (
                                  <Link
                                      href="/signin"
                                      className={`font-vietnam leading-[1.265rem] text-primary`}
                                  >
                                      Sign in
                                  </Link>
                              )}
                        <Link
                            href={`${isAuthenticated ? '/dashboard' : '/signup'}`}
                            className={`font-vietnam leading-[1.265rem] text-white py-[.875rem] px-[1.5rem] bg-primary rounded-[5rem]`}
                        >
                            Get started
                        </Link>
                    </div>
                    <Image
                        src={showDrop ? '/icons/cancel.svg' : '/icons/menu.svg'}
                        height={24}
                        width={24}
                        ref={burger}
                        alt="burger nav"
                        className="w-[1.5rem] aspect-square block md:hidden cursor-pointer"
                        onClick={() => setShowDrop((value) => !value)}
                    />
                    {/* dropdown for mobile */}
                    <AnimatePresence>
                        {showDrop && (
                            <motion.div
                                initial={{ opacity: 0, y: -30 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -30 }}
                                ref={drop}
                                className="absolute flex-col left-0 right-0 top-full flex md:hidden items-center w-full py-[1.5rem] gap-[.5rem] shadow-lg z-20 bg-white"
                            >
                                {linksArray.map((link) => (
                                    <Link
                                        key={link.name}
                                        href={link.link}
                                        className={`font-vietnam p-[.75rem] leading-[1.265rem] ${
                                            path === link.link
                                                ? 'text-black'
                                                : 'text-[#475467]'
                                        } `}
                                    >
                                        {link.name}
                                    </Link>
                                ))}
                                {isLoading
                                    ? null
                                    : !isAuthenticated && (
                                          <Link
                                              href="/signin"
                                              className={`font-vietnam p-[.75rem] leading-[1.265rem] text-primary`}
                                          >
                                              Sign in
                                          </Link>
                                      )}
                                <Link
                                    href={`${isAuthenticated ? '/dashboard' : '/signup'}`}
                                    className={`font-vietnam leading-[1.265rem] text-white py-[.875rem] px-[1.5rem] bg-primary rounded-[5rem]`}
                                >
                                    Get started
                                </Link>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>
            </div>
        </div>
    )
}
