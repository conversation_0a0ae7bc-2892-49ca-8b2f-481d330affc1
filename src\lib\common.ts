const SESSION_KEY = '_dETbYYvEe'
const BASE_URL = process.env.NEXT_PUBLIC_API_URL
const SHIFT_KEY = 8
const CALLBACK_URL = process.env.NEXT_PUBLIC_GOOGLE_CALLBACK_URL
const GOOGLE_CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID
const GOOGLE_AUTH_URL = `https://accounts.google.com/o/oauth2/auth?response_type=code&client_id=${GOOGLE_CLIENT_ID}&redirect_uri=${CALLBACK_URL}&scope=profile%20email`

const sessionEncoder = (data: SessionData) => {
    const jsonData = JSON.stringify(data)
    const base64Data = btoa(jsonData)
    const replaceDoubleEqual = base64Data.replace(/==/g, '+')
    const replaceEqual = replaceDoubleEqual.replace(/=/g, '*')
    const encrypted = encrypt(replaceEqual, SHIFT_KEY)
    return encrypted
}

const sessionDecoder = (data: string) => {
    try {
        const decrypted = decrypt(data, SHIFT_KEY)
        const replaceEqual = decrypted.replace(/\*/g, '=')
        const replaceDoubleEqual = replaceEqual.replace(/\+/g, '==')
        const jsonData = atob(replaceDoubleEqual)
        return JSON.parse(jsonData)
    } catch {
        return {}
    }
}

function encrypt(text: string, shift: number): string {
    return text
        .split('')
        .map((char) => {
            if (char.match(/[a-zA-Z]/)) {
                const base = char >= 'a' ? 'a'.charCodeAt(0) : 'A'.charCodeAt(0)
                return String.fromCharCode(
                    ((char.charCodeAt(0) - base + shift) % 26) + base
                )
            }
            return char
        })
        .join('')
}

function decrypt(text: string, shift: number): string {
    return text
        .split('')
        .map((char) => {
            if (char.match(/[a-zA-Z]/)) {
                const base = char >= 'a' ? 'a'.charCodeAt(0) : 'A'.charCodeAt(0)
                return String.fromCharCode(
                    ((char.charCodeAt(0) - base - shift + 26) % 26) + base
                )
            }
            return char
        })
        .join('')
}

const isValidSession = (data: SessionData) => {
    if (!data) return false
    if (!data.user) return false
    if (!data.token) return false
    if (!data.token.accessToken) return false
    if (!data.token.refreshToken) return false
    return true
}

export {
    SESSION_KEY,
    BASE_URL,
    GOOGLE_AUTH_URL,
    sessionEncoder,
    sessionDecoder,
    isValidSession,
}
