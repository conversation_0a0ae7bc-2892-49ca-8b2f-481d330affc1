'use client'
import { RegisterOptions, useForm } from 'react-hook-form'
import apiRequest from '@/lib/auth/client/request'
import { enqueueSnackbar as notify } from 'notistack'
import { handleError } from '@/lib/error'
import { useRouter, useSearchParams } from 'next/navigation'
import { Suspense } from 'react'
import Input from '../inputs/Input'

interface FormData {
    password: string
    rePassword: string
}

function ResetFormMain() {
    const {
        register,
        handleSubmit,
        setError,
        watch,
        formState: { errors, isSubmitting },
    } = useForm<FormData>()

    const searchParam = useSearchParams()
    const password = watch('password')
    const router = useRouter()

    const inputArray: {
        label: string
        name: keyof FormData
        type: string
        placeholder: string
        validation: RegisterOptions<FormData, keyof FormData>
    }[] = [
        {
            label: 'Password*',
            name: 'password',
            type: 'password',
            placeholder: 'Enter your password',
            validation: {
                required: {
                    value: true,
                    message: `Password is required`,
                },
                minLength: {
                    value: 8,
                    message: 'Password must be at least 8 characters',
                },
            },
        },
        {
            label: 'Confirm Password*',
            name: 'rePassword',
            type: 'password',
            placeholder: 'Enter your password',
            validation: {
                required: {
                    value: true,
                    message: `Password is required`,
                },
                validate: (value) =>
                    value === password || 'Passwords do not match',
            },
        },
    ]

    const onSubmit = (data: FormData) => {
        const resetID = searchParam.get('token')
        const runRequest = async () => {
            const formData = {
                reset_id: resetID,
                password: data.password,
            }
            try {
                const request = await apiRequest(false).post(
                    '/api/auth/reset-password',
                    formData
                )
                const { message } = request.data
                console.log(message, request, 'ghj')
                notify('Password reset successful, please login')
                router.push('/signin')
            } catch (err: unknown) {
                const error: ApiWithFormError<FormData> = handleError(err)
                if (error.errors) {
                    error.errors.forEach((e) => {
                        setError(e.path, {
                            message: e.message,
                        })
                    })
                }
                if (error.message && !error.errors) {
                    notify(error.message)
                }
            }
        }
        if (resetID) runRequest()
    }

    return (
        <form
            method="POST"
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-[2.4rem]"
        >
            <div className="flex flex-col gap-5">
                {inputArray.map((input) => (
                    <Input<FormData>
                        key={input.name}
                        type={input.type}
                        label={input.label}
                        registerName={input.name}
                        placeholder={input.placeholder}
                        register={register}
                        errors={errors}
                        validation={input.validation}
                    />
                ))}
            </div>

            <div className="flex flex-col gap-4">
                <button
                    disabled={isSubmitting}
                    type="submit"
                    className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] disabled:opacity-50`}
                >
                    {isSubmitting ? (
                        <svg
                            className="animate-spin h-5 w-5 mr-3 border-white border-t-2 border-b-2 border-r-2 border-[rgba(255,255,255,0.2)] rounded-full"
                            viewBox="0 0 24 24"
                        />
                    ) : null}
                    Reset Password
                </button>
            </div>
        </form>
    )
}

export default function ResetForm() {
    return (
        <Suspense>
            <ResetFormMain />
        </Suspense>
    )
}
