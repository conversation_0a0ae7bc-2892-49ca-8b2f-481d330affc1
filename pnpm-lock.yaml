lockfileVersion: '9.0'

settings:
    autoInstallPeers: true
    excludeLinksFromLockfile: false

importers:
    .:
        dependencies:
            '@dinero.js/currencies':
                specifier: 2.0.0-alpha.1
                version: 2.0.0-alpha.1
            '@mantine/core':
                specifier: ^7.17.0
                version: 7.17.0(@mantine/hooks@7.17.0(react@19.0.0))(@types/react@19.0.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
            '@mantine/dates':
                specifier: ^7.17.7
                version: 7.17.7(@mantine/core@7.17.0(@mantine/hooks@7.17.0(react@19.0.0))(@types/react@19.0.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0))(@mantine/hooks@7.17.0(react@19.0.0))(dayjs@1.11.13)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
            '@mantine/hooks':
                specifier: ^7.17.0
                version: 7.17.0(react@19.0.0)
            '@tanstack/react-query':
                specifier: ^5.71.1
                version: 5.71.1(react@19.0.0)
            aos:
                specifier: ^2.3.4
                version: 2.3.4
            axios:
                specifier: ^1.7.9
                version: 1.7.9
            dayjs:
                specifier: ^1.11.13
                version: 1.11.13
            dinero.js:
                specifier: 2.0.0-alpha.14
                version: 2.0.0-alpha.14
            framer-motion:
                specifier: ^12.0.6
                version: 12.0.6(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
            husky:
                specifier: ^9.1.7
                version: 9.1.7
            lucide-react:
                specifier: ^0.476.0
                version: 0.476.0(react@19.0.0)
            next:
                specifier: 15.1.6
                version: 15.1.6(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
            next-themes:
                specifier: ^0.4.6
                version: 0.4.6(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
            notistack:
                specifier: ^3.0.2
                version: 3.0.2(csstype@3.1.3)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
            p-throttle:
                specifier: ^7.0.0
                version: 7.0.0
            react:
                specifier: ^19.0.0
                version: 19.0.0
            react-countdown:
                specifier: ^2.3.6
                version: 2.3.6(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
            react-dom:
                specifier: ^19.0.0
                version: 19.0.0(react@19.0.0)
            react-hook-form:
                specifier: ^7.54.2
                version: 7.54.2(react@19.0.0)
            sharp:
                specifier: ^0.33.5
                version: 0.33.5
            swiper:
                specifier: ^11.2.2
                version: 11.2.2
            use-debounce:
                specifier: ^10.0.4
                version: 10.0.4(react@19.0.0)
        devDependencies:
            '@eslint/eslintrc':
                specifier: ^3
                version: 3.2.0
            '@types/aos':
                specifier: ^3.0.7
                version: 3.0.7
            '@types/axios':
                specifier: ^0.14.4
                version: 0.14.4
            '@types/node':
                specifier: ^20
                version: 20.17.16
            '@types/react':
                specifier: ^19
                version: 19.0.8
            '@types/react-dom':
                specifier: ^19
                version: 19.0.3(@types/react@19.0.8)
            eslint:
                specifier: ^9
                version: 9.19.0(jiti@1.21.7)
            eslint-config-next:
                specifier: 15.1.6
                version: 15.1.6(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)
            lint-staged:
                specifier: ^15.4.3
                version: 15.4.3
            postcss:
                specifier: ^8
                version: 8.5.1
            prettier:
                specifier: ^3.4.2
                version: 3.4.2
            tailwindcss:
                specifier: ^3.4.1
                version: 3.4.17
            typescript:
                specifier: 5.7.3
                version: 5.7.3

packages:
    '@alloc/quick-lru@5.2.0':
        resolution:
            {
                integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==,
            }
        engines: { node: '>=10' }

    '@babel/runtime@7.26.9':
        resolution:
            {
                integrity: sha512-aA63XwOkcl4xxQa3HjPMqOP6LiK0ZDv3mUPYEFXkpHbaFjtGggE1A61FjFzJnB+p7/oy2gA8E+rcBNl/zC1tMg==,
            }
        engines: { node: '>=6.9.0' }

    '@dinero.js/calculator-number@2.0.0-alpha.14':
        resolution:
            {
                integrity: sha512-Vmlu6eXNtkFU2cqlrpqfq8KQP9onALf8Es2d34liVa3k3RHjuhizgFUU3V3/tsjOu5WekZq+gYPOr58XVTkB7A==,
            }

    '@dinero.js/core@2.0.0-alpha.14':
        resolution:
            {
                integrity: sha512-CtKELJ783joUbaU62fRM2xDXb4XYSy0MgOuIkPWgVOS3SYKKb6+2Ec9bNlvrW8lsPROz/RsvCuYVTphrZ79twg==,
            }

    '@dinero.js/currencies@2.0.0-alpha.1':
        resolution:
            {
                integrity: sha512-JoKY6q3AOm2II44qLp6VMGn+j+p6QC39m3DFogKZXsucWIIvzt7c2v04DZrrHWLNnDYNzITOnbQ7f2pGbONjig==,
            }

    '@dinero.js/currencies@2.0.0-alpha.14':
        resolution:
            {
                integrity: sha512-Ck5ZLjRI7Pl7Y4VkeOst4WEwiN5vZezv8GHcXWsVLUfTNsmkV37VeLYDRAuTUP4akEJyIry+1o1qHYNtLq3eNw==,
            }

    '@emnapi/runtime@1.3.1':
        resolution:
            {
                integrity: sha512-kEBmG8KyqtxJZv+ygbEim+KCGtIq1fC22Ms3S4ziXmYKm8uyoLX0MHONVKwp+9opg390VaKRNt4a7A9NwmpNhw==,
            }

    '@eslint-community/eslint-utils@4.4.1':
        resolution:
            {
                integrity: sha512-s3O3waFUrMV8P/XaF/+ZTp1X9XBZW1a4B97ZnjQF2KYWaFD2A8KyFBsrsfSjEmjn3RGWAIuvlneuZm3CUK3jbA==,
            }
        engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
        peerDependencies:
            eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

    '@eslint-community/regexpp@4.12.1':
        resolution:
            {
                integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==,
            }
        engines: { node: ^12.0.0 || ^14.0.0 || >=16.0.0 }

    '@eslint/config-array@0.19.1':
        resolution:
            {
                integrity: sha512-fo6Mtm5mWyKjA/Chy1BYTdn5mGJoDNjC7C64ug20ADsRDGrA85bN3uK3MaKbeRkRuuIEAR5N33Jr1pbm411/PA==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    '@eslint/core@0.10.0':
        resolution:
            {
                integrity: sha512-gFHJ+xBOo4G3WRlR1e/3G8A6/KZAH6zcE/hkLRCZTi/B9avAG365QhFA8uOGzTMqgTghpn7/fSnscW++dpMSAw==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    '@eslint/eslintrc@3.2.0':
        resolution:
            {
                integrity: sha512-grOjVNN8P3hjJn/eIETF1wwd12DdnwFDoyceUJLYYdkpbwq3nLi+4fqrTAONx7XDALqlL220wC/RHSC/QTI/0w==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    '@eslint/js@9.19.0':
        resolution:
            {
                integrity: sha512-rbq9/g38qjfqFLOVPvwjIvFFdNziEC5S65jmjPw5r6A//QH+W91akh9irMwjDN8zKUTak6W9EsAv4m/7Wnw0UQ==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    '@eslint/object-schema@2.1.5':
        resolution:
            {
                integrity: sha512-o0bhxnL89h5Bae5T318nFoFzGy+YE5i/gGkoPAgkmTVdRKTiv3p8JHevPiPaMwoloKfEiiaHlawCqaZMqRm+XQ==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    '@eslint/plugin-kit@0.2.5':
        resolution:
            {
                integrity: sha512-lB05FkqEdUg2AA0xEbUz0SnkXT1LcCTa438W4IWTUh4hdOnVbQyOJ81OrDXsJk/LSiJHubgGEFoR5EHq1NsH1A==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    '@floating-ui/core@1.6.9':
        resolution:
            {
                integrity: sha512-uMXCuQ3BItDUbAMhIXw7UPXRfAlOAvZzdK9BWpE60MCn+Svt3aLn9jsPTi/WNGlRUu2uI0v5S7JiIUsbsvh3fw==,
            }

    '@floating-ui/dom@1.6.13':
        resolution:
            {
                integrity: sha512-umqzocjDgNRGTuO7Q8CU32dkHkECqI8ZdMZ5Swb6QAM0t5rnlrN3lGo1hdpscRd3WS8T6DKYK4ephgIH9iRh3w==,
            }

    '@floating-ui/react-dom@2.1.2':
        resolution:
            {
                integrity: sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A==,
            }
        peerDependencies:
            react: '>=16.8.0'
            react-dom: '>=16.8.0'

    '@floating-ui/react@0.26.28':
        resolution:
            {
                integrity: sha512-yORQuuAtVpiRjpMhdc0wJj06b9JFjrYF4qp96j++v2NBpbi6SEGF7donUJ3TMieerQ6qVkAv1tgr7L4r5roTqw==,
            }
        peerDependencies:
            react: '>=16.8.0'
            react-dom: '>=16.8.0'

    '@floating-ui/utils@0.2.9':
        resolution:
            {
                integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==,
            }

    '@humanfs/core@0.19.1':
        resolution:
            {
                integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==,
            }
        engines: { node: '>=18.18.0' }

    '@humanfs/node@0.16.6':
        resolution:
            {
                integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==,
            }
        engines: { node: '>=18.18.0' }

    '@humanwhocodes/module-importer@1.0.1':
        resolution:
            {
                integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==,
            }
        engines: { node: '>=12.22' }

    '@humanwhocodes/retry@0.3.1':
        resolution:
            {
                integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==,
            }
        engines: { node: '>=18.18' }

    '@humanwhocodes/retry@0.4.1':
        resolution:
            {
                integrity: sha512-c7hNEllBlenFTHBky65mhq8WD2kbN9Q6gk0bTk8lSBvc554jpXSkST1iePudpt7+A/AQvuHs9EMqjHDXMY1lrA==,
            }
        engines: { node: '>=18.18' }

    '@img/sharp-darwin-arm64@0.33.5':
        resolution:
            {
                integrity: sha512-UT4p+iz/2H4twwAoLCqfA9UH5pI6DggwKEGuaPy7nCVQ8ZsiY5PIcrRvD1DzuY3qYL07NtIQcWnBSY/heikIFQ==,
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [arm64]
        os: [darwin]

    '@img/sharp-darwin-x64@0.33.5':
        resolution:
            {
                integrity: sha512-fyHac4jIc1ANYGRDxtiqelIbdWkIuQaI84Mv45KvGRRxSAa7o7d1ZKAOBaYbnepLC1WqxfpimdeWfvqqSGwR2Q==,
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [x64]
        os: [darwin]

    '@img/sharp-libvips-darwin-arm64@1.0.4':
        resolution:
            {
                integrity: sha512-XblONe153h0O2zuFfTAbQYAX2JhYmDHeWikp1LM9Hul9gVPjFY427k6dFEcOL72O01QxQsWi761svJ/ev9xEDg==,
            }
        cpu: [arm64]
        os: [darwin]

    '@img/sharp-libvips-darwin-x64@1.0.4':
        resolution:
            {
                integrity: sha512-xnGR8YuZYfJGmWPvmlunFaWJsb9T/AO2ykoP3Fz/0X5XV2aoYBPkX6xqCQvUTKKiLddarLaxpzNe+b1hjeWHAQ==,
            }
        cpu: [x64]
        os: [darwin]

    '@img/sharp-libvips-linux-arm64@1.0.4':
        resolution:
            {
                integrity: sha512-9B+taZ8DlyyqzZQnoeIvDVR/2F4EbMepXMc/NdVbkzsJbzkUjhXv/70GQJ7tdLA4YJgNP25zukcxpX2/SueNrA==,
            }
        cpu: [arm64]
        os: [linux]

    '@img/sharp-libvips-linux-arm@1.0.5':
        resolution:
            {
                integrity: sha512-gvcC4ACAOPRNATg/ov8/MnbxFDJqf/pDePbBnuBDcjsI8PssmjoKMAz4LtLaVi+OnSb5FK/yIOamqDwGmXW32g==,
            }
        cpu: [arm]
        os: [linux]

    '@img/sharp-libvips-linux-s390x@1.0.4':
        resolution:
            {
                integrity: sha512-u7Wz6ntiSSgGSGcjZ55im6uvTrOxSIS8/dgoVMoiGE9I6JAfU50yH5BoDlYA1tcuGS7g/QNtetJnxA6QEsCVTA==,
            }
        cpu: [s390x]
        os: [linux]

    '@img/sharp-libvips-linux-x64@1.0.4':
        resolution:
            {
                integrity: sha512-MmWmQ3iPFZr0Iev+BAgVMb3ZyC4KeFc3jFxnNbEPas60e1cIfevbtuyf9nDGIzOaW9PdnDciJm+wFFaTlj5xYw==,
            }
        cpu: [x64]
        os: [linux]

    '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
        resolution:
            {
                integrity: sha512-9Ti+BbTYDcsbp4wfYib8Ctm1ilkugkA/uscUn6UXK1ldpC1JjiXbLfFZtRlBhjPZ5o1NCLiDbg8fhUPKStHoTA==,
            }
        cpu: [arm64]
        os: [linux]

    '@img/sharp-libvips-linuxmusl-x64@1.0.4':
        resolution:
            {
                integrity: sha512-viYN1KX9m+/hGkJtvYYp+CCLgnJXwiQB39damAO7WMdKWlIhmYTfHjwSbQeUK/20vY154mwezd9HflVFM1wVSw==,
            }
        cpu: [x64]
        os: [linux]

    '@img/sharp-linux-arm64@0.33.5':
        resolution:
            {
                integrity: sha512-JMVv+AMRyGOHtO1RFBiJy/MBsgz0x4AWrT6QoEVVTyh1E39TrCUpTRI7mx9VksGX4awWASxqCYLCV4wBZHAYxA==,
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [arm64]
        os: [linux]

    '@img/sharp-linux-arm@0.33.5':
        resolution:
            {
                integrity: sha512-JTS1eldqZbJxjvKaAkxhZmBqPRGmxgu+qFKSInv8moZ2AmT5Yib3EQ1c6gp493HvrvV8QgdOXdyaIBrhvFhBMQ==,
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [arm]
        os: [linux]

    '@img/sharp-linux-s390x@0.33.5':
        resolution:
            {
                integrity: sha512-y/5PCd+mP4CA/sPDKl2961b+C9d+vPAveS33s6Z3zfASk2j5upL6fXVPZi7ztePZ5CuH+1kW8JtvxgbuXHRa4Q==,
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [s390x]
        os: [linux]

    '@img/sharp-linux-x64@0.33.5':
        resolution:
            {
                integrity: sha512-opC+Ok5pRNAzuvq1AG0ar+1owsu842/Ab+4qvU879ippJBHvyY5n2mxF1izXqkPYlGuP/M556uh53jRLJmzTWA==,
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [x64]
        os: [linux]

    '@img/sharp-linuxmusl-arm64@0.33.5':
        resolution:
            {
                integrity: sha512-XrHMZwGQGvJg2V/oRSUfSAfjfPxO+4DkiRh6p2AFjLQztWUuY/o8Mq0eMQVIY7HJ1CDQUJlxGGZRw1a5bqmd1g==,
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [arm64]
        os: [linux]

    '@img/sharp-linuxmusl-x64@0.33.5':
        resolution:
            {
                integrity: sha512-WT+d/cgqKkkKySYmqoZ8y3pxx7lx9vVejxW/W4DOFMYVSkErR+w7mf2u8m/y4+xHe7yY9DAXQMWQhpnMuFfScw==,
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [x64]
        os: [linux]

    '@img/sharp-wasm32@0.33.5':
        resolution:
            {
                integrity: sha512-ykUW4LVGaMcU9lu9thv85CbRMAwfeadCJHRsg2GmeRa/cJxsVY9Rbd57JcMxBkKHag5U/x7TSBpScF4U8ElVzg==,
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [wasm32]

    '@img/sharp-win32-ia32@0.33.5':
        resolution:
            {
                integrity: sha512-T36PblLaTwuVJ/zw/LaH0PdZkRz5rd3SmMHX8GSmR7vtNSP5Z6bQkExdSK7xGWyxLw4sUknBuugTelgw2faBbQ==,
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [ia32]
        os: [win32]

    '@img/sharp-win32-x64@0.33.5':
        resolution:
            {
                integrity: sha512-MpY/o8/8kj+EcnxwvrP4aTJSWw/aZ7JIGR4aBeZkZw5B7/Jn+tY9/VNwtcoGmdT7GfggGIU4kygOMSbYnOrAbg==,
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [x64]
        os: [win32]

    '@isaacs/cliui@8.0.2':
        resolution:
            {
                integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==,
            }
        engines: { node: '>=12' }

    '@jridgewell/gen-mapping@0.3.8':
        resolution:
            {
                integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==,
            }
        engines: { node: '>=6.0.0' }

    '@jridgewell/resolve-uri@3.1.2':
        resolution:
            {
                integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==,
            }
        engines: { node: '>=6.0.0' }

    '@jridgewell/set-array@1.2.1':
        resolution:
            {
                integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==,
            }
        engines: { node: '>=6.0.0' }

    '@jridgewell/sourcemap-codec@1.5.0':
        resolution:
            {
                integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==,
            }

    '@jridgewell/trace-mapping@0.3.25':
        resolution:
            {
                integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==,
            }

    '@mantine/core@7.17.0':
        resolution:
            {
                integrity: sha512-AU5UFewUNzBCUXIq5Jk6q402TEri7atZW61qHW6P0GufJ2W/JxGHRvgmHOVHTVIcuWQRCt9SBSqZoZ/vHs9LhA==,
            }
        peerDependencies:
            '@mantine/hooks': 7.17.0
            react: ^18.x || ^19.x
            react-dom: ^18.x || ^19.x

    '@mantine/dates@7.17.7':
        resolution:
            {
                integrity: sha512-/F3QteV5Sb5H0zVkU7ZyKdlISYWngdViNLqmredJo8296ukLTsRPr/iIfuYSm3hy3wEWyW5/dWzNPVr1L5cyQg==,
            }
        peerDependencies:
            '@mantine/core': 7.17.7
            '@mantine/hooks': 7.17.7
            dayjs: '>=1.0.0'
            react: ^18.x || ^19.x
            react-dom: ^18.x || ^19.x

    '@mantine/hooks@7.17.0':
        resolution:
            {
                integrity: sha512-vo3K49mLy1nJ8LQNb5KDbJgnX0xwt3Y8JOF3ythjB5LEFMptdLSSgulu64zj+QHtzvffFCsMb05DbTLLpVP/JQ==,
            }
        peerDependencies:
            react: ^18.x || ^19.x

    '@next/env@15.1.6':
        resolution:
            {
                integrity: sha512-d9AFQVPEYNr+aqokIiPLNK/MTyt3DWa/dpKveiAaVccUadFbhFEvY6FXYX2LJO2Hv7PHnLBu2oWwB4uBuHjr/w==,
            }

    '@next/eslint-plugin-next@15.1.6':
        resolution:
            {
                integrity: sha512-+slMxhTgILUntZDGNgsKEYHUvpn72WP1YTlkmEhS51vnVd7S9jEEy0n9YAMcI21vUG4akTw9voWH02lrClt/yw==,
            }

    '@next/swc-darwin-arm64@15.1.6':
        resolution:
            {
                integrity: sha512-u7lg4Mpl9qWpKgy6NzEkz/w0/keEHtOybmIl0ykgItBxEM5mYotS5PmqTpo+Rhg8FiOiWgwr8USxmKQkqLBCrw==,
            }
        engines: { node: '>= 10' }
        cpu: [arm64]
        os: [darwin]

    '@next/swc-darwin-x64@15.1.6':
        resolution:
            {
                integrity: sha512-x1jGpbHbZoZ69nRuogGL2MYPLqohlhnT9OCU6E6QFewwup+z+M6r8oU47BTeJcWsF2sdBahp5cKiAcDbwwK/lg==,
            }
        engines: { node: '>= 10' }
        cpu: [x64]
        os: [darwin]

    '@next/swc-linux-arm64-gnu@15.1.6':
        resolution:
            {
                integrity: sha512-jar9sFw0XewXsBzPf9runGzoivajeWJUc/JkfbLTC4it9EhU8v7tCRLH7l5Y1ReTMN6zKJO0kKAGqDk8YSO2bg==,
            }
        engines: { node: '>= 10' }
        cpu: [arm64]
        os: [linux]

    '@next/swc-linux-arm64-musl@15.1.6':
        resolution:
            {
                integrity: sha512-+n3u//bfsrIaZch4cgOJ3tXCTbSxz0s6brJtU3SzLOvkJlPQMJ+eHVRi6qM2kKKKLuMY+tcau8XD9CJ1OjeSQQ==,
            }
        engines: { node: '>= 10' }
        cpu: [arm64]
        os: [linux]

    '@next/swc-linux-x64-gnu@15.1.6':
        resolution:
            {
                integrity: sha512-SpuDEXixM3PycniL4iVCLyUyvcl6Lt0mtv3am08sucskpG0tYkW1KlRhTgj4LI5ehyxriVVcfdoxuuP8csi3kQ==,
            }
        engines: { node: '>= 10' }
        cpu: [x64]
        os: [linux]

    '@next/swc-linux-x64-musl@15.1.6':
        resolution:
            {
                integrity: sha512-L4druWmdFSZIIRhF+G60API5sFB7suTbDRhYWSjiw0RbE+15igQvE2g2+S973pMGvwN3guw7cJUjA/TmbPWTHQ==,
            }
        engines: { node: '>= 10' }
        cpu: [x64]
        os: [linux]

    '@next/swc-win32-arm64-msvc@15.1.6':
        resolution:
            {
                integrity: sha512-s8w6EeqNmi6gdvM19tqKKWbCyOBvXFbndkGHl+c9YrzsLARRdCHsD9S1fMj8gsXm9v8vhC8s3N8rjuC/XrtkEg==,
            }
        engines: { node: '>= 10' }
        cpu: [arm64]
        os: [win32]

    '@next/swc-win32-x64-msvc@15.1.6':
        resolution:
            {
                integrity: sha512-6xomMuu54FAFxttYr5PJbEfu96godcxBTRk1OhAvJq0/EnmFU/Ybiax30Snis4vdWZ9LGpf7Roy5fSs7v/5ROQ==,
            }
        engines: { node: '>= 10' }
        cpu: [x64]
        os: [win32]

    '@nodelib/fs.scandir@2.1.5':
        resolution:
            {
                integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==,
            }
        engines: { node: '>= 8' }

    '@nodelib/fs.stat@2.0.5':
        resolution:
            {
                integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==,
            }
        engines: { node: '>= 8' }

    '@nodelib/fs.walk@1.2.8':
        resolution:
            {
                integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==,
            }
        engines: { node: '>= 8' }

    '@nolyfill/is-core-module@1.0.39':
        resolution:
            {
                integrity: sha512-nn5ozdjYQpUCZlWGuxcJY/KpxkWQs4DcbMCmKojjyrYDEAGy4Ce19NN4v5MduafTwJlbKc99UA8YhSVqq9yPZA==,
            }
        engines: { node: '>=12.4.0' }

    '@pkgjs/parseargs@0.11.0':
        resolution:
            {
                integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==,
            }
        engines: { node: '>=14' }

    '@rtsao/scc@1.1.0':
        resolution:
            {
                integrity: sha512-zt6OdqaDoOnJ1ZYsCYGt9YmWzDXl4vQdKTyJev62gFhRGKdx7mcT54V9KIjg+d2wi9EXsPvAPKe7i7WjfVWB8g==,
            }

    '@rushstack/eslint-patch@1.10.5':
        resolution:
            {
                integrity: sha512-kkKUDVlII2DQiKy7UstOR1ErJP8kUKAQ4oa+SQtM0K+lPdmmjj0YnnxBgtTVYH7mUKtbsxeFC9y0AmK7Yb78/A==,
            }

    '@swc/counter@0.1.3':
        resolution:
            {
                integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==,
            }

    '@swc/helpers@0.5.15':
        resolution:
            {
                integrity: sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==,
            }

    '@tanstack/query-core@5.71.1':
        resolution:
            {
                integrity: sha512-4+ZswCHOfJX+ikhXNoocamTUmJcHtB+Ljjz/oJkC7/eKB5IrzEwR4vEwZUENiPi+wISucJHR5TUbuuJ26w3kdQ==,
            }

    '@tanstack/react-query@5.71.1':
        resolution:
            {
                integrity: sha512-6BTkaSIGT58MroI4kIGXNdx/NhirXPU+75AJObLq+WBa39WmoxhzSk0YX+hqWJ/bvqZJFxslbEU4qIHaRZq+8Q==,
            }
        peerDependencies:
            react: ^18 || ^19

    '@types/aos@3.0.7':
        resolution:
            {
                integrity: sha512-sEhyFqvKauUJZDbvAB3Pggynrq6g+2PS4XB3tmUr+mDL1gfDJnwslUC4QQ7/l8UD+LWpr3RxZVR/rHoZrLqZVg==,
            }

    '@types/axios@0.14.4':
        resolution:
            {
                integrity: sha512-9JgOaunvQdsQ/qW2OPmE5+hCeUB52lQSolecrFrthct55QekhmXEwT203s20RL+UHtCQc15y3VXpby9E7Kkh/g==,
            }
        deprecated: This is a stub types definition. axios provides its own type definitions, so you do not need this installed.

    '@types/estree@1.0.6':
        resolution:
            {
                integrity: sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==,
            }

    '@types/json-schema@7.0.15':
        resolution:
            {
                integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==,
            }

    '@types/json5@0.0.29':
        resolution:
            {
                integrity: sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==,
            }

    '@types/node@20.17.16':
        resolution:
            {
                integrity: sha512-vOTpLduLkZXePLxHiHsBLp98mHGnl8RptV4YAO3HfKO5UHjDvySGbxKtpYfy8Sx5+WKcgc45qNreJJRVM3L6mw==,
            }

    '@types/react-dom@19.0.3':
        resolution:
            {
                integrity: sha512-0Knk+HJiMP/qOZgMyNFamlIjw9OFCsyC2ZbigmEEyXXixgre6IQpm/4V+r3qH4GC1JPvRJKInw+on2rV6YZLeA==,
            }
        peerDependencies:
            '@types/react': ^19.0.0

    '@types/react@19.0.8':
        resolution:
            {
                integrity: sha512-9P/o1IGdfmQxrujGbIMDyYaaCykhLKc0NGCtYcECNUr9UAaDe4gwvV9bR6tvd5Br1SG0j+PBpbKr2UYY8CwqSw==,
            }

    '@typescript-eslint/eslint-plugin@8.22.0':
        resolution:
            {
                integrity: sha512-4Uta6REnz/xEJMvwf72wdUnC3rr4jAQf5jnTkeRQ9b6soxLxhDEbS/pfMPoJLDfFPNVRdryqWUIV/2GZzDJFZw==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        peerDependencies:
            '@typescript-eslint/parser': ^8.0.0 || ^8.0.0-alpha.0
            eslint: ^8.57.0 || ^9.0.0
            typescript: '>=4.8.4 <5.8.0'

    '@typescript-eslint/parser@8.22.0':
        resolution:
            {
                integrity: sha512-MqtmbdNEdoNxTPzpWiWnqNac54h8JDAmkWtJExBVVnSrSmi9z+sZUt0LfKqk9rjqmKOIeRhO4fHHJ1nQIjduIQ==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        peerDependencies:
            eslint: ^8.57.0 || ^9.0.0
            typescript: '>=4.8.4 <5.8.0'

    '@typescript-eslint/scope-manager@8.22.0':
        resolution:
            {
                integrity: sha512-/lwVV0UYgkj7wPSw0o8URy6YI64QmcOdwHuGuxWIYznO6d45ER0wXUbksr9pYdViAofpUCNJx/tAzNukgvaaiQ==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    '@typescript-eslint/type-utils@8.22.0':
        resolution:
            {
                integrity: sha512-NzE3aB62fDEaGjaAYZE4LH7I1MUwHooQ98Byq0G0y3kkibPJQIXVUspzlFOmOfHhiDLwKzMlWxaNv+/qcZurJA==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        peerDependencies:
            eslint: ^8.57.0 || ^9.0.0
            typescript: '>=4.8.4 <5.8.0'

    '@typescript-eslint/types@8.22.0':
        resolution:
            {
                integrity: sha512-0S4M4baNzp612zwpD4YOieP3VowOARgK2EkN/GBn95hpyF8E2fbMT55sRHWBq+Huaqk3b3XK+rxxlM8sPgGM6A==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    '@typescript-eslint/typescript-estree@8.22.0':
        resolution:
            {
                integrity: sha512-SJX99NAS2ugGOzpyhMza/tX+zDwjvwAtQFLsBo3GQxiGcvaKlqGBkmZ+Y1IdiSi9h4Q0Lr5ey+Cp9CGWNY/F/w==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        peerDependencies:
            typescript: '>=4.8.4 <5.8.0'

    '@typescript-eslint/utils@8.22.0':
        resolution:
            {
                integrity: sha512-T8oc1MbF8L+Bk2msAvCUzjxVB2Z2f+vXYfcucE2wOmYs7ZUwco5Ep0fYZw8quNwOiw9K8GYVL+Kgc2pETNTLOg==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        peerDependencies:
            eslint: ^8.57.0 || ^9.0.0
            typescript: '>=4.8.4 <5.8.0'

    '@typescript-eslint/visitor-keys@8.22.0':
        resolution:
            {
                integrity: sha512-AWpYAXnUgvLNabGTy3uBylkgZoosva/miNd1I8Bz3SjotmQPbVqhO4Cczo8AsZ44XVErEBPr/CRSgaj8sG7g0w==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    acorn-jsx@5.3.2:
        resolution:
            {
                integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==,
            }
        peerDependencies:
            acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

    acorn@8.14.0:
        resolution:
            {
                integrity: sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==,
            }
        engines: { node: '>=0.4.0' }
        hasBin: true

    ajv@6.12.6:
        resolution:
            {
                integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==,
            }

    ansi-escapes@7.0.0:
        resolution:
            {
                integrity: sha512-GdYO7a61mR0fOlAsvC9/rIHf7L96sBc6dEWzeOu+KAea5bZyQRPIpojrVoI4AXGJS/ycu/fBTdLrUkA4ODrvjw==,
            }
        engines: { node: '>=18' }

    ansi-regex@5.0.1:
        resolution:
            {
                integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==,
            }
        engines: { node: '>=8' }

    ansi-regex@6.1.0:
        resolution:
            {
                integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==,
            }
        engines: { node: '>=12' }

    ansi-styles@4.3.0:
        resolution:
            {
                integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==,
            }
        engines: { node: '>=8' }

    ansi-styles@6.2.1:
        resolution:
            {
                integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==,
            }
        engines: { node: '>=12' }

    any-promise@1.3.0:
        resolution:
            {
                integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==,
            }

    anymatch@3.1.3:
        resolution:
            {
                integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==,
            }
        engines: { node: '>= 8' }

    aos@2.3.4:
        resolution:
            {
                integrity: sha512-zh/ahtR2yME4I51z8IttIt4lC1Nw0ktsFtmeDzID1m9naJnWXhCoARaCgNOGXb5CLy3zm+wqmRAEgMYB5E2HUw==,
            }

    arg@5.0.2:
        resolution:
            {
                integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==,
            }

    argparse@2.0.1:
        resolution:
            {
                integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==,
            }

    aria-query@5.3.2:
        resolution:
            {
                integrity: sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw==,
            }
        engines: { node: '>= 0.4' }

    array-buffer-byte-length@1.0.2:
        resolution:
            {
                integrity: sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==,
            }
        engines: { node: '>= 0.4' }

    array-includes@3.1.8:
        resolution:
            {
                integrity: sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==,
            }
        engines: { node: '>= 0.4' }

    array.prototype.findlast@1.2.5:
        resolution:
            {
                integrity: sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==,
            }
        engines: { node: '>= 0.4' }

    array.prototype.findlastindex@1.2.5:
        resolution:
            {
                integrity: sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ==,
            }
        engines: { node: '>= 0.4' }

    array.prototype.flat@1.3.3:
        resolution:
            {
                integrity: sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==,
            }
        engines: { node: '>= 0.4' }

    array.prototype.flatmap@1.3.3:
        resolution:
            {
                integrity: sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==,
            }
        engines: { node: '>= 0.4' }

    array.prototype.tosorted@1.1.4:
        resolution:
            {
                integrity: sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==,
            }
        engines: { node: '>= 0.4' }

    arraybuffer.prototype.slice@1.0.4:
        resolution:
            {
                integrity: sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==,
            }
        engines: { node: '>= 0.4' }

    ast-types-flow@0.0.8:
        resolution:
            {
                integrity: sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==,
            }

    async-function@1.0.0:
        resolution:
            {
                integrity: sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==,
            }
        engines: { node: '>= 0.4' }

    asynckit@0.4.0:
        resolution:
            {
                integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==,
            }

    available-typed-arrays@1.0.7:
        resolution:
            {
                integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==,
            }
        engines: { node: '>= 0.4' }

    axe-core@4.10.2:
        resolution:
            {
                integrity: sha512-RE3mdQ7P3FRSe7eqCWoeQ/Z9QXrtniSjp1wUjt5nRC3WIpz5rSCve6o3fsZ2aCpJtrZjSZgjwXAoTO5k4tEI0w==,
            }
        engines: { node: '>=4' }

    axios@1.7.9:
        resolution:
            {
                integrity: sha512-LhLcE7Hbiryz8oMDdDptSrWowmB4Bl6RCt6sIJKpRB4XtVf0iEgewX3au/pJqm+Py1kCASkb/FFKjxQaLtxJvw==,
            }

    axobject-query@4.1.0:
        resolution:
            {
                integrity: sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==,
            }
        engines: { node: '>= 0.4' }

    balanced-match@1.0.2:
        resolution:
            {
                integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==,
            }

    binary-extensions@2.3.0:
        resolution:
            {
                integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==,
            }
        engines: { node: '>=8' }

    brace-expansion@1.1.11:
        resolution:
            {
                integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==,
            }

    brace-expansion@2.0.1:
        resolution:
            {
                integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==,
            }

    braces@3.0.3:
        resolution:
            {
                integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==,
            }
        engines: { node: '>=8' }

    busboy@1.6.0:
        resolution:
            {
                integrity: sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==,
            }
        engines: { node: '>=10.16.0' }

    call-bind-apply-helpers@1.0.1:
        resolution:
            {
                integrity: sha512-BhYE+WDaywFg2TBWYNXAE+8B1ATnThNBqXHP5nQu0jWJdVvY2hvkpyB3qOmtmDePiS5/BDQ8wASEWGMWRG148g==,
            }
        engines: { node: '>= 0.4' }

    call-bind@1.0.8:
        resolution:
            {
                integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==,
            }
        engines: { node: '>= 0.4' }

    call-bound@1.0.3:
        resolution:
            {
                integrity: sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==,
            }
        engines: { node: '>= 0.4' }

    callsites@3.1.0:
        resolution:
            {
                integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==,
            }
        engines: { node: '>=6' }

    camelcase-css@2.0.1:
        resolution:
            {
                integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==,
            }
        engines: { node: '>= 6' }

    caniuse-lite@1.0.30001695:
        resolution:
            {
                integrity: sha512-vHyLade6wTgI2u1ec3WQBxv+2BrTERV28UXQu9LO6lZ9pYeMk34vjXFLOxo1A4UBA8XTL4njRQZdno/yYaSmWw==,
            }

    chalk@4.1.2:
        resolution:
            {
                integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==,
            }
        engines: { node: '>=10' }

    chalk@5.4.1:
        resolution:
            {
                integrity: sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==,
            }
        engines: { node: ^12.17.0 || ^14.13 || >=16.0.0 }

    chokidar@3.6.0:
        resolution:
            {
                integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==,
            }
        engines: { node: '>= 8.10.0' }

    classlist-polyfill@1.2.0:
        resolution:
            {
                integrity: sha512-GzIjNdcEtH4ieA2S8NmrSxv7DfEV5fmixQeyTmqmRmRJPGpRBaSnA2a0VrCjyT8iW8JjEdMbKzDotAJf+ajgaQ==,
            }

    cli-cursor@5.0.0:
        resolution:
            {
                integrity: sha512-aCj4O5wKyszjMmDT4tZj93kxyydN/K5zPWSCe6/0AV/AA1pqe5ZBIw0a2ZfPQV7lL5/yb5HsUreJ6UFAF1tEQw==,
            }
        engines: { node: '>=18' }

    cli-truncate@4.0.0:
        resolution:
            {
                integrity: sha512-nPdaFdQ0h/GEigbPClz11D0v/ZJEwxmeVZGeMo3Z5StPtUTkA9o1lD6QwoirYiSDzbcwn2XcjwmCp68W1IS4TA==,
            }
        engines: { node: '>=18' }

    client-only@0.0.1:
        resolution:
            {
                integrity: sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==,
            }

    clsx@1.2.1:
        resolution:
            {
                integrity: sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==,
            }
        engines: { node: '>=6' }

    clsx@2.1.1:
        resolution:
            {
                integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==,
            }
        engines: { node: '>=6' }

    color-convert@2.0.1:
        resolution:
            {
                integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==,
            }
        engines: { node: '>=7.0.0' }

    color-name@1.1.4:
        resolution:
            {
                integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==,
            }

    color-string@1.9.1:
        resolution:
            {
                integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==,
            }

    color@4.2.3:
        resolution:
            {
                integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==,
            }
        engines: { node: '>=12.5.0' }

    colorette@2.0.20:
        resolution:
            {
                integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==,
            }

    combined-stream@1.0.8:
        resolution:
            {
                integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==,
            }
        engines: { node: '>= 0.8' }

    commander@13.1.0:
        resolution:
            {
                integrity: sha512-/rFeCpNJQbhSZjGVwO9RFV3xPqbnERS8MmIQzCtD/zl6gpJuV/bMLuN92oG3F7d8oDEHHRrujSXNUr8fpjntKw==,
            }
        engines: { node: '>=18' }

    commander@4.1.1:
        resolution:
            {
                integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==,
            }
        engines: { node: '>= 6' }

    concat-map@0.0.1:
        resolution:
            {
                integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==,
            }

    cross-spawn@7.0.6:
        resolution:
            {
                integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==,
            }
        engines: { node: '>= 8' }

    cssesc@3.0.0:
        resolution:
            {
                integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==,
            }
        engines: { node: '>=4' }
        hasBin: true

    csstype@3.1.3:
        resolution:
            {
                integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==,
            }

    damerau-levenshtein@1.0.8:
        resolution:
            {
                integrity: sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==,
            }

    data-view-buffer@1.0.2:
        resolution:
            {
                integrity: sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==,
            }
        engines: { node: '>= 0.4' }

    data-view-byte-length@1.0.2:
        resolution:
            {
                integrity: sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==,
            }
        engines: { node: '>= 0.4' }

    data-view-byte-offset@1.0.1:
        resolution:
            {
                integrity: sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==,
            }
        engines: { node: '>= 0.4' }

    dayjs@1.11.13:
        resolution:
            {
                integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==,
            }

    debug@3.2.7:
        resolution:
            {
                integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==,
            }
        peerDependencies:
            supports-color: '*'
        peerDependenciesMeta:
            supports-color:
                optional: true

    debug@4.4.0:
        resolution:
            {
                integrity: sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==,
            }
        engines: { node: '>=6.0' }
        peerDependencies:
            supports-color: '*'
        peerDependenciesMeta:
            supports-color:
                optional: true

    deep-is@0.1.4:
        resolution:
            {
                integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==,
            }

    define-data-property@1.1.4:
        resolution:
            {
                integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==,
            }
        engines: { node: '>= 0.4' }

    define-properties@1.2.1:
        resolution:
            {
                integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==,
            }
        engines: { node: '>= 0.4' }

    delayed-stream@1.0.0:
        resolution:
            {
                integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==,
            }
        engines: { node: '>=0.4.0' }

    detect-libc@2.0.3:
        resolution:
            {
                integrity: sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==,
            }
        engines: { node: '>=8' }

    detect-node-es@1.1.0:
        resolution:
            {
                integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==,
            }

    didyoumean@1.2.2:
        resolution:
            {
                integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==,
            }

    dinero.js@2.0.0-alpha.14:
        resolution:
            {
                integrity: sha512-dkURHd9P+2TjuSTMUAnvrB7SsL4GbBYG/WPtIBV8M+L7Xf80x84sJcUUTxIS33S4AlkIbVANlaL86w2g5zdrmg==,
            }

    dlv@1.1.3:
        resolution:
            {
                integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==,
            }

    doctrine@2.1.0:
        resolution:
            {
                integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==,
            }
        engines: { node: '>=0.10.0' }

    dunder-proto@1.0.1:
        resolution:
            {
                integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==,
            }
        engines: { node: '>= 0.4' }

    eastasianwidth@0.2.0:
        resolution:
            {
                integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==,
            }

    emoji-regex@10.4.0:
        resolution:
            {
                integrity: sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==,
            }

    emoji-regex@8.0.0:
        resolution:
            {
                integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==,
            }

    emoji-regex@9.2.2:
        resolution:
            {
                integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==,
            }

    enhanced-resolve@5.18.0:
        resolution:
            {
                integrity: sha512-0/r0MySGYG8YqlayBZ6MuCfECmHFdJ5qyPh8s8wa5Hnm6SaFLSK1VYCbj+NKp090Nm1caZhD+QTnmxO7esYGyQ==,
            }
        engines: { node: '>=10.13.0' }

    environment@1.1.0:
        resolution:
            {
                integrity: sha512-xUtoPkMggbz0MPyPiIWr1Kp4aeWJjDZ6SMvURhimjdZgsRuDplF5/s9hcgGhyXMhs+6vpnuoiZ2kFiu3FMnS8Q==,
            }
        engines: { node: '>=18' }

    es-abstract@1.23.9:
        resolution:
            {
                integrity: sha512-py07lI0wjxAC/DcfK1S6G7iANonniZwTISvdPzk9hzeH0IZIshbuuFxLIU96OyF89Yb9hiqWn8M/bY83KY5vzA==,
            }
        engines: { node: '>= 0.4' }

    es-define-property@1.0.1:
        resolution:
            {
                integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==,
            }
        engines: { node: '>= 0.4' }

    es-errors@1.3.0:
        resolution:
            {
                integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==,
            }
        engines: { node: '>= 0.4' }

    es-iterator-helpers@1.2.1:
        resolution:
            {
                integrity: sha512-uDn+FE1yrDzyC0pCo961B2IHbdM8y/ACZsKD4dG6WqrjV53BADjwa7D+1aom2rsNVfLyDgU/eigvlJGJ08OQ4w==,
            }
        engines: { node: '>= 0.4' }

    es-object-atoms@1.1.1:
        resolution:
            {
                integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==,
            }
        engines: { node: '>= 0.4' }

    es-set-tostringtag@2.1.0:
        resolution:
            {
                integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==,
            }
        engines: { node: '>= 0.4' }

    es-shim-unscopables@1.0.2:
        resolution:
            {
                integrity: sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw==,
            }

    es-to-primitive@1.3.0:
        resolution:
            {
                integrity: sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==,
            }
        engines: { node: '>= 0.4' }

    escape-string-regexp@4.0.0:
        resolution:
            {
                integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==,
            }
        engines: { node: '>=10' }

    eslint-config-next@15.1.6:
        resolution:
            {
                integrity: sha512-Wd1uy6y7nBbXUSg9QAuQ+xYEKli5CgUhLjz1QHW11jLDis5vK5XB3PemL6jEmy7HrdhaRFDz+GTZ/3FoH+EUjg==,
            }
        peerDependencies:
            eslint: ^7.23.0 || ^8.0.0 || ^9.0.0
            typescript: '>=3.3.1'
        peerDependenciesMeta:
            typescript:
                optional: true

    eslint-import-resolver-node@0.3.9:
        resolution:
            {
                integrity: sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==,
            }

    eslint-import-resolver-typescript@3.7.0:
        resolution:
            {
                integrity: sha512-Vrwyi8HHxY97K5ebydMtffsWAn1SCR9eol49eCd5fJS4O1WV7PaAjbcjmbfJJSMz/t4Mal212Uz/fQZrOB8mow==,
            }
        engines: { node: ^14.18.0 || >=16.0.0 }
        peerDependencies:
            eslint: '*'
            eslint-plugin-import: '*'
            eslint-plugin-import-x: '*'
        peerDependenciesMeta:
            eslint-plugin-import:
                optional: true
            eslint-plugin-import-x:
                optional: true

    eslint-module-utils@2.12.0:
        resolution:
            {
                integrity: sha512-wALZ0HFoytlyh/1+4wuZ9FJCD/leWHQzzrxJ8+rebyReSLk7LApMyd3WJaLVoN+D5+WIdJyDK1c6JnE65V4Zyg==,
            }
        engines: { node: '>=4' }
        peerDependencies:
            '@typescript-eslint/parser': '*'
            eslint: '*'
            eslint-import-resolver-node: '*'
            eslint-import-resolver-typescript: '*'
            eslint-import-resolver-webpack: '*'
        peerDependenciesMeta:
            '@typescript-eslint/parser':
                optional: true
            eslint:
                optional: true
            eslint-import-resolver-node:
                optional: true
            eslint-import-resolver-typescript:
                optional: true
            eslint-import-resolver-webpack:
                optional: true

    eslint-plugin-import@2.31.0:
        resolution:
            {
                integrity: sha512-ixmkI62Rbc2/w8Vfxyh1jQRTdRTF52VxwRVHl/ykPAmqG+Nb7/kNn+byLP0LxPgI7zWA16Jt82SybJInmMia3A==,
            }
        engines: { node: '>=4' }
        peerDependencies:
            '@typescript-eslint/parser': '*'
            eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
        peerDependenciesMeta:
            '@typescript-eslint/parser':
                optional: true

    eslint-plugin-jsx-a11y@6.10.2:
        resolution:
            {
                integrity: sha512-scB3nz4WmG75pV8+3eRUQOHZlNSUhFNq37xnpgRkCCELU3XMvXAxLk1eqWWyE22Ki4Q01Fnsw9BA3cJHDPgn2Q==,
            }
        engines: { node: '>=4.0' }
        peerDependencies:
            eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9

    eslint-plugin-react-hooks@5.1.0:
        resolution:
            {
                integrity: sha512-mpJRtPgHN2tNAvZ35AMfqeB3Xqeo273QxrHJsbBEPWODRM4r0yB6jfoROqKEYrOn27UtRPpcpHc2UqyBSuUNTw==,
            }
        engines: { node: '>=10' }
        peerDependencies:
            eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0

    eslint-plugin-react@7.37.4:
        resolution:
            {
                integrity: sha512-BGP0jRmfYyvOyvMoRX/uoUeW+GqNj9y16bPQzqAHf3AYII/tDs+jMN0dBVkl88/OZwNGwrVFxE7riHsXVfy/LQ==,
            }
        engines: { node: '>=4' }
        peerDependencies:
            eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

    eslint-scope@8.2.0:
        resolution:
            {
                integrity: sha512-PHlWUfG6lvPc3yvP5A4PNyBL1W8fkDUccmI21JUu/+GKZBoH/W5u6usENXUrWFRsyoW5ACUjFGgAFQp5gUlb/A==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    eslint-visitor-keys@3.4.3:
        resolution:
            {
                integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==,
            }
        engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

    eslint-visitor-keys@4.2.0:
        resolution:
            {
                integrity: sha512-UyLnSehNt62FFhSwjZlHmeokpRK59rcz29j+F1/aDgbkbRTk7wIc9XzdoasMUbRNKDM0qQt/+BJ4BrpFeABemw==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    eslint@9.19.0:
        resolution:
            {
                integrity: sha512-ug92j0LepKlbbEv6hD911THhoRHmbdXt2gX+VDABAW/Ir7D3nqKdv5Pf5vtlyY6HQMTEP2skXY43ueqTCWssEA==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        hasBin: true
        peerDependencies:
            jiti: '*'
        peerDependenciesMeta:
            jiti:
                optional: true

    espree@10.3.0:
        resolution:
            {
                integrity: sha512-0QYC8b24HWY8zjRnDTL6RiHfDbAWn63qb4LMj1Z4b076A4une81+z03Kg7l7mn/48PUTqoLptSXez8oknU8Clg==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    esquery@1.6.0:
        resolution:
            {
                integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==,
            }
        engines: { node: '>=0.10' }

    esrecurse@4.3.0:
        resolution:
            {
                integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==,
            }
        engines: { node: '>=4.0' }

    estraverse@5.3.0:
        resolution:
            {
                integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==,
            }
        engines: { node: '>=4.0' }

    esutils@2.0.3:
        resolution:
            {
                integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==,
            }
        engines: { node: '>=0.10.0' }

    eventemitter3@5.0.1:
        resolution:
            {
                integrity: sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==,
            }

    execa@8.0.1:
        resolution:
            {
                integrity: sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==,
            }
        engines: { node: '>=16.17' }

    fast-deep-equal@3.1.3:
        resolution:
            {
                integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==,
            }

    fast-glob@3.3.1:
        resolution:
            {
                integrity: sha512-kNFPyjhh5cKjrUltxs+wFx+ZkbRaxxmZ+X0ZU31SOsxCEtP9VPgtq2teZw1DebupL5GmDaNQ6yKMMVcM41iqDg==,
            }
        engines: { node: '>=8.6.0' }

    fast-glob@3.3.3:
        resolution:
            {
                integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==,
            }
        engines: { node: '>=8.6.0' }

    fast-json-stable-stringify@2.1.0:
        resolution:
            {
                integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==,
            }

    fast-levenshtein@2.0.6:
        resolution:
            {
                integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==,
            }

    fastq@1.18.0:
        resolution:
            {
                integrity: sha512-QKHXPW0hD8g4UET03SdOdunzSouc9N4AuHdsX8XNcTsuz+yYFILVNIX4l9yHABMhiEI9Db0JTTIpu0wB+Y1QQw==,
            }

    file-entry-cache@8.0.0:
        resolution:
            {
                integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==,
            }
        engines: { node: '>=16.0.0' }

    fill-range@7.1.1:
        resolution:
            {
                integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==,
            }
        engines: { node: '>=8' }

    find-up@5.0.0:
        resolution:
            {
                integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==,
            }
        engines: { node: '>=10' }

    flat-cache@4.0.1:
        resolution:
            {
                integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==,
            }
        engines: { node: '>=16' }

    flatted@3.3.2:
        resolution:
            {
                integrity: sha512-AiwGJM8YcNOaobumgtng+6NHuOqC3A7MixFeDafM3X9cIUM+xUXoS5Vfgf+OihAYe20fxqNM9yPBXJzRtZ/4eA==,
            }

    follow-redirects@1.15.9:
        resolution:
            {
                integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==,
            }
        engines: { node: '>=4.0' }
        peerDependencies:
            debug: '*'
        peerDependenciesMeta:
            debug:
                optional: true

    for-each@0.3.4:
        resolution:
            {
                integrity: sha512-kKaIINnFpzW6ffJNDjjyjrk21BkDx38c0xa/klsT8VzLCaMEefv4ZTacrcVR4DmgTeBra++jMDAfS/tS799YDw==,
            }
        engines: { node: '>= 0.4' }

    foreground-child@3.3.0:
        resolution:
            {
                integrity: sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==,
            }
        engines: { node: '>=14' }

    form-data@4.0.2:
        resolution:
            {
                integrity: sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==,
            }
        engines: { node: '>= 6' }

    framer-motion@12.0.6:
        resolution:
            {
                integrity: sha512-LmrXbXF6Vv5WCNmb+O/zn891VPZrH7XbsZgRLBROw6kFiP+iTK49gxTv2Ur3F0Tbw6+sy9BVtSqnWfMUpH+6nA==,
            }
        peerDependencies:
            '@emotion/is-prop-valid': '*'
            react: ^18.0.0 || ^19.0.0
            react-dom: ^18.0.0 || ^19.0.0
        peerDependenciesMeta:
            '@emotion/is-prop-valid':
                optional: true
            react:
                optional: true
            react-dom:
                optional: true

    fsevents@2.3.3:
        resolution:
            {
                integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==,
            }
        engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
        os: [darwin]

    function-bind@1.1.2:
        resolution:
            {
                integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==,
            }

    function.prototype.name@1.1.8:
        resolution:
            {
                integrity: sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==,
            }
        engines: { node: '>= 0.4' }

    functions-have-names@1.2.3:
        resolution:
            {
                integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==,
            }

    get-east-asian-width@1.3.0:
        resolution:
            {
                integrity: sha512-vpeMIQKxczTD/0s2CdEWHcb0eeJe6TFjxb+J5xgX7hScxqrGuyjmv4c1D4A/gelKfyox0gJJwIHF+fLjeaM8kQ==,
            }
        engines: { node: '>=18' }

    get-intrinsic@1.2.7:
        resolution:
            {
                integrity: sha512-VW6Pxhsrk0KAOqs3WEd0klDiF/+V7gQOpAvY1jVU/LHmaD/kQO4523aiJuikX/QAKYiW6x8Jh+RJej1almdtCA==,
            }
        engines: { node: '>= 0.4' }

    get-nonce@1.0.1:
        resolution:
            {
                integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==,
            }
        engines: { node: '>=6' }

    get-proto@1.0.1:
        resolution:
            {
                integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==,
            }
        engines: { node: '>= 0.4' }

    get-stream@8.0.1:
        resolution:
            {
                integrity: sha512-VaUJspBffn/LMCJVoMvSAdmscJyS1auj5Zulnn5UoYcY531UWmdwhRWkcGKnGU93m5HSXP9LP2usOryrBtQowA==,
            }
        engines: { node: '>=16' }

    get-symbol-description@1.1.0:
        resolution:
            {
                integrity: sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==,
            }
        engines: { node: '>= 0.4' }

    get-tsconfig@4.10.0:
        resolution:
            {
                integrity: sha512-kGzZ3LWWQcGIAmg6iWvXn0ei6WDtV26wzHRMwDSzmAbcXrTEXxHy6IehI6/4eT6VRKyMP1eF1VqwrVUmE/LR7A==,
            }

    glob-parent@5.1.2:
        resolution:
            {
                integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==,
            }
        engines: { node: '>= 6' }

    glob-parent@6.0.2:
        resolution:
            {
                integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==,
            }
        engines: { node: '>=10.13.0' }

    glob@10.4.5:
        resolution:
            {
                integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==,
            }
        hasBin: true

    globals@14.0.0:
        resolution:
            {
                integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==,
            }
        engines: { node: '>=18' }

    globalthis@1.0.4:
        resolution:
            {
                integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==,
            }
        engines: { node: '>= 0.4' }

    goober@2.1.16:
        resolution:
            {
                integrity: sha512-erjk19y1U33+XAMe1VTvIONHYoSqE4iS7BYUZfHaqeohLmnC0FdxEh7rQU+6MZ4OajItzjZFSRtVANrQwNq6/g==,
            }
        peerDependencies:
            csstype: ^3.0.10

    gopd@1.2.0:
        resolution:
            {
                integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==,
            }
        engines: { node: '>= 0.4' }

    graceful-fs@4.2.11:
        resolution:
            {
                integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==,
            }

    graphemer@1.4.0:
        resolution:
            {
                integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==,
            }

    has-bigints@1.1.0:
        resolution:
            {
                integrity: sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==,
            }
        engines: { node: '>= 0.4' }

    has-flag@4.0.0:
        resolution:
            {
                integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==,
            }
        engines: { node: '>=8' }

    has-property-descriptors@1.0.2:
        resolution:
            {
                integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==,
            }

    has-proto@1.2.0:
        resolution:
            {
                integrity: sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==,
            }
        engines: { node: '>= 0.4' }

    has-symbols@1.1.0:
        resolution:
            {
                integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==,
            }
        engines: { node: '>= 0.4' }

    has-tostringtag@1.0.2:
        resolution:
            {
                integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==,
            }
        engines: { node: '>= 0.4' }

    hasown@2.0.2:
        resolution:
            {
                integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==,
            }
        engines: { node: '>= 0.4' }

    human-signals@5.0.0:
        resolution:
            {
                integrity: sha512-AXcZb6vzzrFAUE61HnN4mpLqd/cSIwNQjtNWR0euPm6y0iqx3G4gOXaIDdtdDwZmhwe82LA6+zinmW4UBWVePQ==,
            }
        engines: { node: '>=16.17.0' }

    husky@9.1.7:
        resolution:
            {
                integrity: sha512-5gs5ytaNjBrh5Ow3zrvdUUY+0VxIuWVL4i9irt6friV+BqdCfmV11CQTWMiBYWHbXhco+J1kHfTOUkePhCDvMA==,
            }
        engines: { node: '>=18' }
        hasBin: true

    ignore@5.3.2:
        resolution:
            {
                integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==,
            }
        engines: { node: '>= 4' }

    import-fresh@3.3.0:
        resolution:
            {
                integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==,
            }
        engines: { node: '>=6' }

    imurmurhash@0.1.4:
        resolution:
            {
                integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==,
            }
        engines: { node: '>=0.8.19' }

    internal-slot@1.1.0:
        resolution:
            {
                integrity: sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==,
            }
        engines: { node: '>= 0.4' }

    is-array-buffer@3.0.5:
        resolution:
            {
                integrity: sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==,
            }
        engines: { node: '>= 0.4' }

    is-arrayish@0.3.2:
        resolution:
            {
                integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==,
            }

    is-async-function@2.1.1:
        resolution:
            {
                integrity: sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==,
            }
        engines: { node: '>= 0.4' }

    is-bigint@1.1.0:
        resolution:
            {
                integrity: sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==,
            }
        engines: { node: '>= 0.4' }

    is-binary-path@2.1.0:
        resolution:
            {
                integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==,
            }
        engines: { node: '>=8' }

    is-boolean-object@1.2.1:
        resolution:
            {
                integrity: sha512-l9qO6eFlUETHtuihLcYOaLKByJ1f+N4kthcU9YjHy3N+B3hWv0y/2Nd0mu/7lTFnRQHTrSdXF50HQ3bl5fEnng==,
            }
        engines: { node: '>= 0.4' }

    is-bun-module@1.3.0:
        resolution:
            {
                integrity: sha512-DgXeu5UWI0IsMQundYb5UAOzm6G2eVnarJ0byP6Tm55iZNKceD59LNPA2L4VvsScTtHcw0yEkVwSf7PC+QoLSA==,
            }

    is-callable@1.2.7:
        resolution:
            {
                integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==,
            }
        engines: { node: '>= 0.4' }

    is-core-module@2.16.1:
        resolution:
            {
                integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==,
            }
        engines: { node: '>= 0.4' }

    is-data-view@1.0.2:
        resolution:
            {
                integrity: sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==,
            }
        engines: { node: '>= 0.4' }

    is-date-object@1.1.0:
        resolution:
            {
                integrity: sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==,
            }
        engines: { node: '>= 0.4' }

    is-extglob@2.1.1:
        resolution:
            {
                integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==,
            }
        engines: { node: '>=0.10.0' }

    is-finalizationregistry@1.1.1:
        resolution:
            {
                integrity: sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==,
            }
        engines: { node: '>= 0.4' }

    is-fullwidth-code-point@3.0.0:
        resolution:
            {
                integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==,
            }
        engines: { node: '>=8' }

    is-fullwidth-code-point@4.0.0:
        resolution:
            {
                integrity: sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==,
            }
        engines: { node: '>=12' }

    is-fullwidth-code-point@5.0.0:
        resolution:
            {
                integrity: sha512-OVa3u9kkBbw7b8Xw5F9P+D/T9X+Z4+JruYVNapTjPYZYUznQ5YfWeFkOj606XYYW8yugTfC8Pj0hYqvi4ryAhA==,
            }
        engines: { node: '>=18' }

    is-generator-function@1.1.0:
        resolution:
            {
                integrity: sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==,
            }
        engines: { node: '>= 0.4' }

    is-glob@4.0.3:
        resolution:
            {
                integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==,
            }
        engines: { node: '>=0.10.0' }

    is-map@2.0.3:
        resolution:
            {
                integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==,
            }
        engines: { node: '>= 0.4' }

    is-number-object@1.1.1:
        resolution:
            {
                integrity: sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==,
            }
        engines: { node: '>= 0.4' }

    is-number@7.0.0:
        resolution:
            {
                integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==,
            }
        engines: { node: '>=0.12.0' }

    is-regex@1.2.1:
        resolution:
            {
                integrity: sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==,
            }
        engines: { node: '>= 0.4' }

    is-set@2.0.3:
        resolution:
            {
                integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==,
            }
        engines: { node: '>= 0.4' }

    is-shared-array-buffer@1.0.4:
        resolution:
            {
                integrity: sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==,
            }
        engines: { node: '>= 0.4' }

    is-stream@3.0.0:
        resolution:
            {
                integrity: sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==,
            }
        engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

    is-string@1.1.1:
        resolution:
            {
                integrity: sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==,
            }
        engines: { node: '>= 0.4' }

    is-symbol@1.1.1:
        resolution:
            {
                integrity: sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==,
            }
        engines: { node: '>= 0.4' }

    is-typed-array@1.1.15:
        resolution:
            {
                integrity: sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==,
            }
        engines: { node: '>= 0.4' }

    is-weakmap@2.0.2:
        resolution:
            {
                integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==,
            }
        engines: { node: '>= 0.4' }

    is-weakref@1.1.0:
        resolution:
            {
                integrity: sha512-SXM8Nwyys6nT5WP6pltOwKytLV7FqQ4UiibxVmW+EIosHcmCqkkjViTb5SNssDlkCiEYRP1/pdWUKVvZBmsR2Q==,
            }
        engines: { node: '>= 0.4' }

    is-weakset@2.0.4:
        resolution:
            {
                integrity: sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==,
            }
        engines: { node: '>= 0.4' }

    isarray@2.0.5:
        resolution:
            {
                integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==,
            }

    isexe@2.0.0:
        resolution:
            {
                integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==,
            }

    iterator.prototype@1.1.5:
        resolution:
            {
                integrity: sha512-H0dkQoCa3b2VEeKQBOxFph+JAbcrQdE7KC0UkqwpLmv2EC4P41QXP+rqo9wYodACiG5/WM5s9oDApTU8utwj9g==,
            }
        engines: { node: '>= 0.4' }

    jackspeak@3.4.3:
        resolution:
            {
                integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==,
            }

    jiti@1.21.7:
        resolution:
            {
                integrity: sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==,
            }
        hasBin: true

    js-tokens@4.0.0:
        resolution:
            {
                integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==,
            }

    js-yaml@4.1.0:
        resolution:
            {
                integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==,
            }
        hasBin: true

    json-buffer@3.0.1:
        resolution:
            {
                integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==,
            }

    json-schema-traverse@0.4.1:
        resolution:
            {
                integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==,
            }

    json-stable-stringify-without-jsonify@1.0.1:
        resolution:
            {
                integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==,
            }

    json5@1.0.2:
        resolution:
            {
                integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==,
            }
        hasBin: true

    jsx-ast-utils@3.3.5:
        resolution:
            {
                integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==,
            }
        engines: { node: '>=4.0' }

    keyv@4.5.4:
        resolution:
            {
                integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==,
            }

    language-subtag-registry@0.3.23:
        resolution:
            {
                integrity: sha512-0K65Lea881pHotoGEa5gDlMxt3pctLi2RplBb7Ezh4rRdLEOtgi7n4EwK9lamnUCkKBqaeKRVebTq6BAxSkpXQ==,
            }

    language-tags@1.0.9:
        resolution:
            {
                integrity: sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==,
            }
        engines: { node: '>=0.10' }

    levn@0.4.1:
        resolution:
            {
                integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==,
            }
        engines: { node: '>= 0.8.0' }

    lilconfig@3.1.3:
        resolution:
            {
                integrity: sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==,
            }
        engines: { node: '>=14' }

    lines-and-columns@1.2.4:
        resolution:
            {
                integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==,
            }

    lint-staged@15.4.3:
        resolution:
            {
                integrity: sha512-FoH1vOeouNh1pw+90S+cnuoFwRfUD9ijY2GKy5h7HS3OR7JVir2N2xrsa0+Twc1B7cW72L+88geG5cW4wIhn7g==,
            }
        engines: { node: '>=18.12.0' }
        hasBin: true

    listr2@8.2.5:
        resolution:
            {
                integrity: sha512-iyAZCeyD+c1gPyE9qpFu8af0Y+MRtmKOncdGoA2S5EY8iFq99dmmvkNnHiWo+pj0s7yH7l3KPIgee77tKpXPWQ==,
            }
        engines: { node: '>=18.0.0' }

    locate-path@6.0.0:
        resolution:
            {
                integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==,
            }
        engines: { node: '>=10' }

    lodash.debounce@4.0.8:
        resolution:
            {
                integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==,
            }

    lodash.merge@4.6.2:
        resolution:
            {
                integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==,
            }

    lodash.throttle@4.1.1:
        resolution:
            {
                integrity: sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ==,
            }

    log-update@6.1.0:
        resolution:
            {
                integrity: sha512-9ie8ItPR6tjY5uYJh8K/Zrv/RMZ5VOlOWvtZdEHYSTFKZfIBPQa9tOAEeAWhd+AnIneLJ22w5fjOYtoutpWq5w==,
            }
        engines: { node: '>=18' }

    loose-envify@1.4.0:
        resolution:
            {
                integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==,
            }
        hasBin: true

    lru-cache@10.4.3:
        resolution:
            {
                integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==,
            }

    lucide-react@0.476.0:
        resolution:
            {
                integrity: sha512-x6cLTk8gahdUPje0hSgLN1/MgiJH+Xl90Xoxy9bkPAsMPOUiyRSKR4JCDPGVCEpyqnZXH3exFWNItcvra9WzUQ==,
            }
        peerDependencies:
            react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0

    math-intrinsics@1.1.0:
        resolution:
            {
                integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==,
            }
        engines: { node: '>= 0.4' }

    merge-stream@2.0.0:
        resolution:
            {
                integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==,
            }

    merge2@1.4.1:
        resolution:
            {
                integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==,
            }
        engines: { node: '>= 8' }

    micromatch@4.0.8:
        resolution:
            {
                integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==,
            }
        engines: { node: '>=8.6' }

    mime-db@1.52.0:
        resolution:
            {
                integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==,
            }
        engines: { node: '>= 0.6' }

    mime-types@2.1.35:
        resolution:
            {
                integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==,
            }
        engines: { node: '>= 0.6' }

    mimic-fn@4.0.0:
        resolution:
            {
                integrity: sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==,
            }
        engines: { node: '>=12' }

    mimic-function@5.0.1:
        resolution:
            {
                integrity: sha512-VP79XUPxV2CigYP3jWwAUFSku2aKqBH7uTAapFWCBqutsbmDo96KY5o8uh6U+/YSIn5OxJnXp73beVkpqMIGhA==,
            }
        engines: { node: '>=18' }

    minimatch@3.1.2:
        resolution:
            {
                integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==,
            }

    minimatch@9.0.5:
        resolution:
            {
                integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==,
            }
        engines: { node: '>=16 || 14 >=14.17' }

    minimist@1.2.8:
        resolution:
            {
                integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==,
            }

    minipass@7.1.2:
        resolution:
            {
                integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==,
            }
        engines: { node: '>=16 || 14 >=14.17' }

    motion-dom@12.0.0:
        resolution:
            {
                integrity: sha512-CvYd15OeIR6kHgMdonCc1ihsaUG4MYh/wrkz8gZ3hBX/uamyZCXN9S9qJoYF03GqfTt7thTV/dxnHYX4+55vDg==,
            }

    motion-utils@12.0.0:
        resolution:
            {
                integrity: sha512-MNFiBKbbqnmvOjkPyOKgHUp3Q6oiokLkI1bEwm5QA28cxMZrv0CbbBGDNmhF6DIXsi1pCQBSs0dX8xjeER1tmA==,
            }

    ms@2.1.3:
        resolution:
            {
                integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==,
            }

    mz@2.7.0:
        resolution:
            {
                integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==,
            }

    nanoid@3.3.8:
        resolution:
            {
                integrity: sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==,
            }
        engines: { node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1 }
        hasBin: true

    natural-compare@1.4.0:
        resolution:
            {
                integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==,
            }

    next-themes@0.4.6:
        resolution:
            {
                integrity: sha512-pZvgD5L0IEvX5/9GWyHMf3m8BKiVQwsCMHfoFosXtXBMnaS0ZnIJ9ST4b4NqLVKDEm8QBxoNNGNaBv2JNF6XNA==,
            }
        peerDependencies:
            react: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc
            react-dom: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc

    next@15.1.6:
        resolution:
            {
                integrity: sha512-Hch4wzbaX0vKQtalpXvUiw5sYivBy4cm5rzUKrBnUB/y436LGrvOUqYvlSeNVCWFO/770gDlltR9gqZH62ct4Q==,
            }
        engines: { node: ^18.18.0 || ^19.8.0 || >= 20.0.0 }
        hasBin: true
        peerDependencies:
            '@opentelemetry/api': ^1.1.0
            '@playwright/test': ^1.41.2
            babel-plugin-react-compiler: '*'
            react: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
            react-dom: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
            sass: ^1.3.0
        peerDependenciesMeta:
            '@opentelemetry/api':
                optional: true
            '@playwright/test':
                optional: true
            babel-plugin-react-compiler:
                optional: true
            sass:
                optional: true

    normalize-path@3.0.0:
        resolution:
            {
                integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==,
            }
        engines: { node: '>=0.10.0' }

    notistack@3.0.2:
        resolution:
            {
                integrity: sha512-0R+/arLYbK5Hh7mEfR2adt0tyXJcCC9KkA2hc56FeWik2QN6Bm/S4uW+BjzDARsJth5u06nTjelSw/VSnB1YEA==,
            }
        engines: { node: '>=12.0.0', npm: '>=6.0.0' }
        peerDependencies:
            react: ^17.0.0 || ^18.0.0 || ^19.0.0
            react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0

    npm-run-path@5.3.0:
        resolution:
            {
                integrity: sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==,
            }
        engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

    object-assign@4.1.1:
        resolution:
            {
                integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==,
            }
        engines: { node: '>=0.10.0' }

    object-hash@3.0.0:
        resolution:
            {
                integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==,
            }
        engines: { node: '>= 6' }

    object-inspect@1.13.3:
        resolution:
            {
                integrity: sha512-kDCGIbxkDSXE3euJZZXzc6to7fCrKHNI/hSRQnRuQ+BWjFNzZwiFF8fj/6o2t2G9/jTj8PSIYTfCLelLZEeRpA==,
            }
        engines: { node: '>= 0.4' }

    object-keys@1.1.1:
        resolution:
            {
                integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==,
            }
        engines: { node: '>= 0.4' }

    object.assign@4.1.7:
        resolution:
            {
                integrity: sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==,
            }
        engines: { node: '>= 0.4' }

    object.entries@1.1.8:
        resolution:
            {
                integrity: sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==,
            }
        engines: { node: '>= 0.4' }

    object.fromentries@2.0.8:
        resolution:
            {
                integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==,
            }
        engines: { node: '>= 0.4' }

    object.groupby@1.0.3:
        resolution:
            {
                integrity: sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==,
            }
        engines: { node: '>= 0.4' }

    object.values@1.2.1:
        resolution:
            {
                integrity: sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==,
            }
        engines: { node: '>= 0.4' }

    onetime@6.0.0:
        resolution:
            {
                integrity: sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==,
            }
        engines: { node: '>=12' }

    onetime@7.0.0:
        resolution:
            {
                integrity: sha512-VXJjc87FScF88uafS3JllDgvAm+c/Slfz06lorj2uAY34rlUu0Nt+v8wreiImcrgAjjIHp1rXpTDlLOGw29WwQ==,
            }
        engines: { node: '>=18' }

    optionator@0.9.4:
        resolution:
            {
                integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==,
            }
        engines: { node: '>= 0.8.0' }

    own-keys@1.0.1:
        resolution:
            {
                integrity: sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==,
            }
        engines: { node: '>= 0.4' }

    p-limit@3.1.0:
        resolution:
            {
                integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==,
            }
        engines: { node: '>=10' }

    p-locate@5.0.0:
        resolution:
            {
                integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==,
            }
        engines: { node: '>=10' }

    p-throttle@7.0.0:
        resolution:
            {
                integrity: sha512-aio0v+S0QVkH1O+9x4dHtD4dgCExACcL+3EtNaGqC01GBudS9ijMuUsmN8OVScyV4OOp0jqdLShZFuSlbL/AsA==,
            }
        engines: { node: '>=18' }

    package-json-from-dist@1.0.1:
        resolution:
            {
                integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==,
            }

    parent-module@1.0.1:
        resolution:
            {
                integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==,
            }
        engines: { node: '>=6' }

    path-exists@4.0.0:
        resolution:
            {
                integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==,
            }
        engines: { node: '>=8' }

    path-key@3.1.1:
        resolution:
            {
                integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==,
            }
        engines: { node: '>=8' }

    path-key@4.0.0:
        resolution:
            {
                integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==,
            }
        engines: { node: '>=12' }

    path-parse@1.0.7:
        resolution:
            {
                integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==,
            }

    path-scurry@1.11.1:
        resolution:
            {
                integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==,
            }
        engines: { node: '>=16 || 14 >=14.18' }

    picocolors@1.1.1:
        resolution:
            {
                integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==,
            }

    picomatch@2.3.1:
        resolution:
            {
                integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==,
            }
        engines: { node: '>=8.6' }

    pidtree@0.6.0:
        resolution:
            {
                integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==,
            }
        engines: { node: '>=0.10' }
        hasBin: true

    pify@2.3.0:
        resolution:
            {
                integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==,
            }
        engines: { node: '>=0.10.0' }

    pirates@4.0.6:
        resolution:
            {
                integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==,
            }
        engines: { node: '>= 6' }

    possible-typed-array-names@1.0.0:
        resolution:
            {
                integrity: sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==,
            }
        engines: { node: '>= 0.4' }

    postcss-import@15.1.0:
        resolution:
            {
                integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==,
            }
        engines: { node: '>=14.0.0' }
        peerDependencies:
            postcss: ^8.0.0

    postcss-js@4.0.1:
        resolution:
            {
                integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==,
            }
        engines: { node: ^12 || ^14 || >= 16 }
        peerDependencies:
            postcss: ^8.4.21

    postcss-load-config@4.0.2:
        resolution:
            {
                integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==,
            }
        engines: { node: '>= 14' }
        peerDependencies:
            postcss: '>=8.0.9'
            ts-node: '>=9.0.0'
        peerDependenciesMeta:
            postcss:
                optional: true
            ts-node:
                optional: true

    postcss-nested@6.2.0:
        resolution:
            {
                integrity: sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==,
            }
        engines: { node: '>=12.0' }
        peerDependencies:
            postcss: ^8.2.14

    postcss-selector-parser@6.1.2:
        resolution:
            {
                integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==,
            }
        engines: { node: '>=4' }

    postcss-value-parser@4.2.0:
        resolution:
            {
                integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==,
            }

    postcss@8.4.31:
        resolution:
            {
                integrity: sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==,
            }
        engines: { node: ^10 || ^12 || >=14 }

    postcss@8.5.1:
        resolution:
            {
                integrity: sha512-6oz2beyjc5VMn/KV1pPw8fliQkhBXrVn1Z3TVyqZxU8kZpzEKhBdmCFqI6ZbmGtamQvQGuU1sgPTk8ZrXDD7jQ==,
            }
        engines: { node: ^10 || ^12 || >=14 }

    prelude-ls@1.2.1:
        resolution:
            {
                integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==,
            }
        engines: { node: '>= 0.8.0' }

    prettier@3.4.2:
        resolution:
            {
                integrity: sha512-e9MewbtFo+Fevyuxn/4rrcDAaq0IYxPGLvObpQjiZBMAzB9IGmzlnG9RZy3FFas+eBMu2vA0CszMeduow5dIuQ==,
            }
        engines: { node: '>=14' }
        hasBin: true

    prop-types@15.8.1:
        resolution:
            {
                integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==,
            }

    proxy-from-env@1.1.0:
        resolution:
            {
                integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==,
            }

    punycode@2.3.1:
        resolution:
            {
                integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==,
            }
        engines: { node: '>=6' }

    queue-microtask@1.2.3:
        resolution:
            {
                integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==,
            }

    react-countdown@2.3.6:
        resolution:
            {
                integrity: sha512-ZfX6S08Hb6x6W6eCn1hMDvxPICI/T30fd+gaeVTCR/2cGZ2WJ3f26e4ImNIMX1fHkopJrUdnRpWXP13/D39+gg==,
            }
        peerDependencies:
            react: '>= 15'
            react-dom: '>= 15'

    react-dom@19.0.0:
        resolution:
            {
                integrity: sha512-4GV5sHFG0e/0AD4X+ySy6UJd3jVl1iNsNHdpad0qhABJ11twS3TTBnseqsKurKcsNqCEFeGL3uLpVChpIO3QfQ==,
            }
        peerDependencies:
            react: ^19.0.0

    react-hook-form@7.54.2:
        resolution:
            {
                integrity: sha512-eHpAUgUjWbZocoQYUHposymRb4ZP6d0uwUnooL2uOybA9/3tPUvoAKqEWK1WaSiTxxOfTpffNZP7QwlnM3/gEg==,
            }
        engines: { node: '>=18.0.0' }
        peerDependencies:
            react: ^16.8.0 || ^17 || ^18 || ^19

    react-is@16.13.1:
        resolution:
            {
                integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==,
            }

    react-number-format@5.4.3:
        resolution:
            {
                integrity: sha512-VCY5hFg/soBighAoGcdE+GagkJq0230qN6jcS5sp8wQX1qy1fYN/RX7/BXkrs0oyzzwqR8/+eSUrqXbGeywdUQ==,
            }
        peerDependencies:
            react: ^0.14 || ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
            react-dom: ^0.14 || ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

    react-remove-scroll-bar@2.3.8:
        resolution:
            {
                integrity: sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==,
            }
        engines: { node: '>=10' }
        peerDependencies:
            '@types/react': '*'
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
        peerDependenciesMeta:
            '@types/react':
                optional: true

    react-remove-scroll@2.6.3:
        resolution:
            {
                integrity: sha512-pnAi91oOk8g8ABQKGF5/M9qxmmOPxaAnopyTHYfqYEwJhyFrbbBtHuSgtKEoH0jpcxx5o3hXqH1mNd9/Oi+8iQ==,
            }
        engines: { node: '>=10' }
        peerDependencies:
            '@types/react': '*'
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
        peerDependenciesMeta:
            '@types/react':
                optional: true

    react-style-singleton@2.2.3:
        resolution:
            {
                integrity: sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==,
            }
        engines: { node: '>=10' }
        peerDependencies:
            '@types/react': '*'
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
        peerDependenciesMeta:
            '@types/react':
                optional: true

    react-textarea-autosize@8.5.6:
        resolution:
            {
                integrity: sha512-aT3ioKXMa8f6zHYGebhbdMD2L00tKeRX1zuVuDx9YQK/JLLRSaSxq3ugECEmUB9z2kvk6bFSIoRHLkkUv0RJiw==,
            }
        engines: { node: '>=10' }
        peerDependencies:
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

    react@19.0.0:
        resolution:
            {
                integrity: sha512-V8AVnmPIICiWpGfm6GLzCR/W5FXLchHop40W4nXBmdlEceh16rCN8O8LNWm5bh5XUX91fh7KpA+W0TgMKmgTpQ==,
            }
        engines: { node: '>=0.10.0' }

    read-cache@1.0.0:
        resolution:
            {
                integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==,
            }

    readdirp@3.6.0:
        resolution:
            {
                integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==,
            }
        engines: { node: '>=8.10.0' }

    reflect.getprototypeof@1.0.10:
        resolution:
            {
                integrity: sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==,
            }
        engines: { node: '>= 0.4' }

    regenerator-runtime@0.14.1:
        resolution:
            {
                integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==,
            }

    regexp.prototype.flags@1.5.4:
        resolution:
            {
                integrity: sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==,
            }
        engines: { node: '>= 0.4' }

    resolve-from@4.0.0:
        resolution:
            {
                integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==,
            }
        engines: { node: '>=4' }

    resolve-pkg-maps@1.0.0:
        resolution:
            {
                integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==,
            }

    resolve@1.22.10:
        resolution:
            {
                integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==,
            }
        engines: { node: '>= 0.4' }
        hasBin: true

    resolve@2.0.0-next.5:
        resolution:
            {
                integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==,
            }
        hasBin: true

    restore-cursor@5.1.0:
        resolution:
            {
                integrity: sha512-oMA2dcrw6u0YfxJQXm342bFKX/E4sG9rbTzO9ptUcR/e8A33cHuvStiYOwH7fszkZlZ1z/ta9AAoPk2F4qIOHA==,
            }
        engines: { node: '>=18' }

    reusify@1.0.4:
        resolution:
            {
                integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==,
            }
        engines: { iojs: '>=1.0.0', node: '>=0.10.0' }

    rfdc@1.4.1:
        resolution:
            {
                integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==,
            }

    run-parallel@1.2.0:
        resolution:
            {
                integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==,
            }

    safe-array-concat@1.1.3:
        resolution:
            {
                integrity: sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==,
            }
        engines: { node: '>=0.4' }

    safe-push-apply@1.0.0:
        resolution:
            {
                integrity: sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==,
            }
        engines: { node: '>= 0.4' }

    safe-regex-test@1.1.0:
        resolution:
            {
                integrity: sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==,
            }
        engines: { node: '>= 0.4' }

    scheduler@0.25.0:
        resolution:
            {
                integrity: sha512-xFVuu11jh+xcO7JOAGJNOXld8/TcEHK/4CituBUeUb5hqxJLj9YuemAEuvm9gQ/+pgXYfbQuqAkiYu+u7YEsNA==,
            }

    semver@6.3.1:
        resolution:
            {
                integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==,
            }
        hasBin: true

    semver@7.6.3:
        resolution:
            {
                integrity: sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==,
            }
        engines: { node: '>=10' }
        hasBin: true

    set-function-length@1.2.2:
        resolution:
            {
                integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==,
            }
        engines: { node: '>= 0.4' }

    set-function-name@2.0.2:
        resolution:
            {
                integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==,
            }
        engines: { node: '>= 0.4' }

    set-proto@1.0.0:
        resolution:
            {
                integrity: sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==,
            }
        engines: { node: '>= 0.4' }

    sharp@0.33.5:
        resolution:
            {
                integrity: sha512-haPVm1EkS9pgvHrQ/F3Xy+hgcuMV0Wm9vfIBSiwZ05k+xgb0PkBQpGsAA/oWdDobNaZTH5ppvHtzCFbnSEwHVw==,
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }

    shebang-command@2.0.0:
        resolution:
            {
                integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==,
            }
        engines: { node: '>=8' }

    shebang-regex@3.0.0:
        resolution:
            {
                integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==,
            }
        engines: { node: '>=8' }

    side-channel-list@1.0.0:
        resolution:
            {
                integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==,
            }
        engines: { node: '>= 0.4' }

    side-channel-map@1.0.1:
        resolution:
            {
                integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==,
            }
        engines: { node: '>= 0.4' }

    side-channel-weakmap@1.0.2:
        resolution:
            {
                integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==,
            }
        engines: { node: '>= 0.4' }

    side-channel@1.1.0:
        resolution:
            {
                integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==,
            }
        engines: { node: '>= 0.4' }

    signal-exit@4.1.0:
        resolution:
            {
                integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==,
            }
        engines: { node: '>=14' }

    simple-swizzle@0.2.2:
        resolution:
            {
                integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==,
            }

    slice-ansi@5.0.0:
        resolution:
            {
                integrity: sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==,
            }
        engines: { node: '>=12' }

    slice-ansi@7.1.0:
        resolution:
            {
                integrity: sha512-bSiSngZ/jWeX93BqeIAbImyTbEihizcwNjFoRUIY/T1wWQsfsm2Vw1agPKylXvQTU7iASGdHhyqRlqQzfz+Htg==,
            }
        engines: { node: '>=18' }

    source-map-js@1.2.1:
        resolution:
            {
                integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==,
            }
        engines: { node: '>=0.10.0' }

    stable-hash@0.0.4:
        resolution:
            {
                integrity: sha512-LjdcbuBeLcdETCrPn9i8AYAZ1eCtu4ECAWtP7UleOiZ9LzVxRzzUZEoZ8zB24nhkQnDWyET0I+3sWokSDS3E7g==,
            }

    streamsearch@1.1.0:
        resolution:
            {
                integrity: sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==,
            }
        engines: { node: '>=10.0.0' }

    string-argv@0.3.2:
        resolution:
            {
                integrity: sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q==,
            }
        engines: { node: '>=0.6.19' }

    string-width@4.2.3:
        resolution:
            {
                integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==,
            }
        engines: { node: '>=8' }

    string-width@5.1.2:
        resolution:
            {
                integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==,
            }
        engines: { node: '>=12' }

    string-width@7.2.0:
        resolution:
            {
                integrity: sha512-tsaTIkKW9b4N+AEj+SVA+WhJzV7/zMhcSu78mLKWSk7cXMOSHsBKFWUs0fWwq8QyK3MgJBQRX6Gbi4kYbdvGkQ==,
            }
        engines: { node: '>=18' }

    string.prototype.includes@2.0.1:
        resolution:
            {
                integrity: sha512-o7+c9bW6zpAdJHTtujeePODAhkuicdAryFsfVKwA+wGw89wJ4GTY484WTucM9hLtDEOpOvI+aHnzqnC5lHp4Rg==,
            }
        engines: { node: '>= 0.4' }

    string.prototype.matchall@4.0.12:
        resolution:
            {
                integrity: sha512-6CC9uyBL+/48dYizRf7H7VAYCMCNTBeM78x/VTUe9bFEaxBepPJDa1Ow99LqI/1yF7kuy7Q3cQsYMrcjGUcskA==,
            }
        engines: { node: '>= 0.4' }

    string.prototype.repeat@1.0.0:
        resolution:
            {
                integrity: sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==,
            }

    string.prototype.trim@1.2.10:
        resolution:
            {
                integrity: sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==,
            }
        engines: { node: '>= 0.4' }

    string.prototype.trimend@1.0.9:
        resolution:
            {
                integrity: sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==,
            }
        engines: { node: '>= 0.4' }

    string.prototype.trimstart@1.0.8:
        resolution:
            {
                integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==,
            }
        engines: { node: '>= 0.4' }

    strip-ansi@6.0.1:
        resolution:
            {
                integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==,
            }
        engines: { node: '>=8' }

    strip-ansi@7.1.0:
        resolution:
            {
                integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==,
            }
        engines: { node: '>=12' }

    strip-bom@3.0.0:
        resolution:
            {
                integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==,
            }
        engines: { node: '>=4' }

    strip-final-newline@3.0.0:
        resolution:
            {
                integrity: sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==,
            }
        engines: { node: '>=12' }

    strip-json-comments@3.1.1:
        resolution:
            {
                integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==,
            }
        engines: { node: '>=8' }

    styled-jsx@5.1.6:
        resolution:
            {
                integrity: sha512-qSVyDTeMotdvQYoHWLNGwRFJHC+i+ZvdBRYosOFgC+Wg1vx4frN2/RG/NA7SYqqvKNLf39P2LSRA2pu6n0XYZA==,
            }
        engines: { node: '>= 12.0.0' }
        peerDependencies:
            '@babel/core': '*'
            babel-plugin-macros: '*'
            react: '>= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0'
        peerDependenciesMeta:
            '@babel/core':
                optional: true
            babel-plugin-macros:
                optional: true

    sucrase@3.35.0:
        resolution:
            {
                integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==,
            }
        engines: { node: '>=16 || 14 >=14.17' }
        hasBin: true

    supports-color@7.2.0:
        resolution:
            {
                integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==,
            }
        engines: { node: '>=8' }

    supports-preserve-symlinks-flag@1.0.0:
        resolution:
            {
                integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==,
            }
        engines: { node: '>= 0.4' }

    swiper@11.2.2:
        resolution:
            {
                integrity: sha512-FmAN6zACpVUbd/1prO9xQ9gKo9cc6RE2UKU/z4oXtS8fNyX4sdOW/HHT/e444WucLJs0jeMId6WjdWM2Lrs8zA==,
            }
        engines: { node: '>= 4.7.0' }

    tabbable@6.2.0:
        resolution:
            {
                integrity: sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==,
            }

    tailwindcss@3.4.17:
        resolution:
            {
                integrity: sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==,
            }
        engines: { node: '>=14.0.0' }
        hasBin: true

    tapable@2.2.1:
        resolution:
            {
                integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==,
            }
        engines: { node: '>=6' }

    thenify-all@1.6.0:
        resolution:
            {
                integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==,
            }
        engines: { node: '>=0.8' }

    thenify@3.3.1:
        resolution:
            {
                integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==,
            }

    to-regex-range@5.0.1:
        resolution:
            {
                integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==,
            }
        engines: { node: '>=8.0' }

    ts-api-utils@2.0.0:
        resolution:
            {
                integrity: sha512-xCt/TOAc+EOHS1XPnijD3/yzpH6qg2xppZO1YDqGoVsNXfQfzHpOdNuXwrwOU8u4ITXJyDCTyt8w5g1sZv9ynQ==,
            }
        engines: { node: '>=18.12' }
        peerDependencies:
            typescript: '>=4.8.4'

    ts-interface-checker@0.1.13:
        resolution:
            {
                integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==,
            }

    tsconfig-paths@3.15.0:
        resolution:
            {
                integrity: sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==,
            }

    tslib@2.8.1:
        resolution:
            {
                integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==,
            }

    type-check@0.4.0:
        resolution:
            {
                integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==,
            }
        engines: { node: '>= 0.8.0' }

    type-fest@4.35.0:
        resolution:
            {
                integrity: sha512-2/AwEFQDFEy30iOLjrvHDIH7e4HEWH+f1Yl1bI5XMqzuoCUqwYCdxachgsgv0og/JdVZUhbfjcJAoHj5L1753A==,
            }
        engines: { node: '>=16' }

    typed-array-buffer@1.0.3:
        resolution:
            {
                integrity: sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==,
            }
        engines: { node: '>= 0.4' }

    typed-array-byte-length@1.0.3:
        resolution:
            {
                integrity: sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==,
            }
        engines: { node: '>= 0.4' }

    typed-array-byte-offset@1.0.4:
        resolution:
            {
                integrity: sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==,
            }
        engines: { node: '>= 0.4' }

    typed-array-length@1.0.7:
        resolution:
            {
                integrity: sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==,
            }
        engines: { node: '>= 0.4' }

    typescript@5.7.3:
        resolution:
            {
                integrity: sha512-84MVSjMEHP+FQRPy3pX9sTVV/INIex71s9TL2Gm5FG/WG1SqXeKyZ0k7/blY/4FdOzI12CBy1vGc4og/eus0fw==,
            }
        engines: { node: '>=14.17' }
        hasBin: true

    unbox-primitive@1.1.0:
        resolution:
            {
                integrity: sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==,
            }
        engines: { node: '>= 0.4' }

    undici-types@6.19.8:
        resolution:
            {
                integrity: sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==,
            }

    uri-js@4.4.1:
        resolution:
            {
                integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==,
            }

    use-callback-ref@1.3.3:
        resolution:
            {
                integrity: sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==,
            }
        engines: { node: '>=10' }
        peerDependencies:
            '@types/react': '*'
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
        peerDependenciesMeta:
            '@types/react':
                optional: true

    use-composed-ref@1.4.0:
        resolution:
            {
                integrity: sha512-djviaxuOOh7wkj0paeO1Q/4wMZ8Zrnag5H6yBvzN7AKKe8beOaED9SF5/ByLqsku8NP4zQqsvM2u3ew/tJK8/w==,
            }
        peerDependencies:
            '@types/react': '*'
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
        peerDependenciesMeta:
            '@types/react':
                optional: true

    use-debounce@10.0.4:
        resolution:
            {
                integrity: sha512-6Cf7Yr7Wk7Kdv77nnJMf6de4HuDE4dTxKij+RqE9rufDsI6zsbjyAxcH5y2ueJCQAnfgKbzXbZHYlkFwmBlWkw==,
            }
        engines: { node: '>= 16.0.0' }
        peerDependencies:
            react: '*'

    use-isomorphic-layout-effect@1.2.0:
        resolution:
            {
                integrity: sha512-q6ayo8DWoPZT0VdG4u3D3uxcgONP3Mevx2i2b0434cwWBoL+aelL1DzkXI6w3PhTZzUeR2kaVlZn70iCiseP6w==,
            }
        peerDependencies:
            '@types/react': '*'
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
        peerDependenciesMeta:
            '@types/react':
                optional: true

    use-latest@1.3.0:
        resolution:
            {
                integrity: sha512-mhg3xdm9NaM8q+gLT8KryJPnRFOz1/5XPBhmDEVZK1webPzDjrPk7f/mbpeLqTgB9msytYWANxgALOCJKnLvcQ==,
            }
        peerDependencies:
            '@types/react': '*'
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
        peerDependenciesMeta:
            '@types/react':
                optional: true

    use-sidecar@1.1.3:
        resolution:
            {
                integrity: sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==,
            }
        engines: { node: '>=10' }
        peerDependencies:
            '@types/react': '*'
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
        peerDependenciesMeta:
            '@types/react':
                optional: true

    util-deprecate@1.0.2:
        resolution:
            {
                integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==,
            }

    which-boxed-primitive@1.1.1:
        resolution:
            {
                integrity: sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==,
            }
        engines: { node: '>= 0.4' }

    which-builtin-type@1.2.1:
        resolution:
            {
                integrity: sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==,
            }
        engines: { node: '>= 0.4' }

    which-collection@1.0.2:
        resolution:
            {
                integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==,
            }
        engines: { node: '>= 0.4' }

    which-typed-array@1.1.18:
        resolution:
            {
                integrity: sha512-qEcY+KJYlWyLH9vNbsr6/5j59AXk5ni5aakf8ldzBvGde6Iz4sxZGkJyWSAueTG7QhOvNRYb1lDdFmL5Td0QKA==,
            }
        engines: { node: '>= 0.4' }

    which@2.0.2:
        resolution:
            {
                integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==,
            }
        engines: { node: '>= 8' }
        hasBin: true

    word-wrap@1.2.5:
        resolution:
            {
                integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==,
            }
        engines: { node: '>=0.10.0' }

    wrap-ansi@7.0.0:
        resolution:
            {
                integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==,
            }
        engines: { node: '>=10' }

    wrap-ansi@8.1.0:
        resolution:
            {
                integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==,
            }
        engines: { node: '>=12' }

    wrap-ansi@9.0.0:
        resolution:
            {
                integrity: sha512-G8ura3S+3Z2G+mkgNRq8dqaFZAuxfsxpBB8OCTGRTCtp+l/v9nbFNmCUP1BZMts3G1142MsZfn6eeUKrr4PD1Q==,
            }
        engines: { node: '>=18' }

    yaml@2.7.0:
        resolution:
            {
                integrity: sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==,
            }
        engines: { node: '>= 14' }
        hasBin: true

    yocto-queue@0.1.0:
        resolution:
            {
                integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==,
            }
        engines: { node: '>=10' }

snapshots:
    '@alloc/quick-lru@5.2.0': {}

    '@babel/runtime@7.26.9':
        dependencies:
            regenerator-runtime: 0.14.1

    '@dinero.js/calculator-number@2.0.0-alpha.14':
        dependencies:
            '@dinero.js/core': 2.0.0-alpha.14

    '@dinero.js/core@2.0.0-alpha.14':
        dependencies:
            '@dinero.js/currencies': 2.0.0-alpha.14

    '@dinero.js/currencies@2.0.0-alpha.1': {}

    '@dinero.js/currencies@2.0.0-alpha.14': {}

    '@emnapi/runtime@1.3.1':
        dependencies:
            tslib: 2.8.1
        optional: true

    '@eslint-community/eslint-utils@4.4.1(eslint@9.19.0(jiti@1.21.7))':
        dependencies:
            eslint: 9.19.0(jiti@1.21.7)
            eslint-visitor-keys: 3.4.3

    '@eslint-community/regexpp@4.12.1': {}

    '@eslint/config-array@0.19.1':
        dependencies:
            '@eslint/object-schema': 2.1.5
            debug: 4.4.0
            minimatch: 3.1.2
        transitivePeerDependencies:
            - supports-color

    '@eslint/core@0.10.0':
        dependencies:
            '@types/json-schema': 7.0.15

    '@eslint/eslintrc@3.2.0':
        dependencies:
            ajv: 6.12.6
            debug: 4.4.0
            espree: 10.3.0
            globals: 14.0.0
            ignore: 5.3.2
            import-fresh: 3.3.0
            js-yaml: 4.1.0
            minimatch: 3.1.2
            strip-json-comments: 3.1.1
        transitivePeerDependencies:
            - supports-color

    '@eslint/js@9.19.0': {}

    '@eslint/object-schema@2.1.5': {}

    '@eslint/plugin-kit@0.2.5':
        dependencies:
            '@eslint/core': 0.10.0
            levn: 0.4.1

    '@floating-ui/core@1.6.9':
        dependencies:
            '@floating-ui/utils': 0.2.9

    '@floating-ui/dom@1.6.13':
        dependencies:
            '@floating-ui/core': 1.6.9
            '@floating-ui/utils': 0.2.9

    '@floating-ui/react-dom@2.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
        dependencies:
            '@floating-ui/dom': 1.6.13
            react: 19.0.0
            react-dom: 19.0.0(react@19.0.0)

    '@floating-ui/react@0.26.28(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
        dependencies:
            '@floating-ui/react-dom': 2.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
            '@floating-ui/utils': 0.2.9
            react: 19.0.0
            react-dom: 19.0.0(react@19.0.0)
            tabbable: 6.2.0

    '@floating-ui/utils@0.2.9': {}

    '@humanfs/core@0.19.1': {}

    '@humanfs/node@0.16.6':
        dependencies:
            '@humanfs/core': 0.19.1
            '@humanwhocodes/retry': 0.3.1

    '@humanwhocodes/module-importer@1.0.1': {}

    '@humanwhocodes/retry@0.3.1': {}

    '@humanwhocodes/retry@0.4.1': {}

    '@img/sharp-darwin-arm64@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-darwin-arm64': 1.0.4
        optional: true

    '@img/sharp-darwin-x64@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-darwin-x64': 1.0.4
        optional: true

    '@img/sharp-libvips-darwin-arm64@1.0.4':
        optional: true

    '@img/sharp-libvips-darwin-x64@1.0.4':
        optional: true

    '@img/sharp-libvips-linux-arm64@1.0.4':
        optional: true

    '@img/sharp-libvips-linux-arm@1.0.5':
        optional: true

    '@img/sharp-libvips-linux-s390x@1.0.4':
        optional: true

    '@img/sharp-libvips-linux-x64@1.0.4':
        optional: true

    '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
        optional: true

    '@img/sharp-libvips-linuxmusl-x64@1.0.4':
        optional: true

    '@img/sharp-linux-arm64@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-linux-arm64': 1.0.4
        optional: true

    '@img/sharp-linux-arm@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-linux-arm': 1.0.5
        optional: true

    '@img/sharp-linux-s390x@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-linux-s390x': 1.0.4
        optional: true

    '@img/sharp-linux-x64@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-linux-x64': 1.0.4
        optional: true

    '@img/sharp-linuxmusl-arm64@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-linuxmusl-arm64': 1.0.4
        optional: true

    '@img/sharp-linuxmusl-x64@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-linuxmusl-x64': 1.0.4
        optional: true

    '@img/sharp-wasm32@0.33.5':
        dependencies:
            '@emnapi/runtime': 1.3.1
        optional: true

    '@img/sharp-win32-ia32@0.33.5':
        optional: true

    '@img/sharp-win32-x64@0.33.5':
        optional: true

    '@isaacs/cliui@8.0.2':
        dependencies:
            string-width: 5.1.2
            string-width-cjs: string-width@4.2.3
            strip-ansi: 7.1.0
            strip-ansi-cjs: strip-ansi@6.0.1
            wrap-ansi: 8.1.0
            wrap-ansi-cjs: wrap-ansi@7.0.0

    '@jridgewell/gen-mapping@0.3.8':
        dependencies:
            '@jridgewell/set-array': 1.2.1
            '@jridgewell/sourcemap-codec': 1.5.0
            '@jridgewell/trace-mapping': 0.3.25

    '@jridgewell/resolve-uri@3.1.2': {}

    '@jridgewell/set-array@1.2.1': {}

    '@jridgewell/sourcemap-codec@1.5.0': {}

    '@jridgewell/trace-mapping@0.3.25':
        dependencies:
            '@jridgewell/resolve-uri': 3.1.2
            '@jridgewell/sourcemap-codec': 1.5.0

    '@mantine/core@7.17.0(@mantine/hooks@7.17.0(react@19.0.0))(@types/react@19.0.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
        dependencies:
            '@floating-ui/react': 0.26.28(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
            '@mantine/hooks': 7.17.0(react@19.0.0)
            clsx: 2.1.1
            react: 19.0.0
            react-dom: 19.0.0(react@19.0.0)
            react-number-format: 5.4.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
            react-remove-scroll: 2.6.3(@types/react@19.0.8)(react@19.0.0)
            react-textarea-autosize: 8.5.6(@types/react@19.0.8)(react@19.0.0)
            type-fest: 4.35.0
        transitivePeerDependencies:
            - '@types/react'

    '@mantine/dates@7.17.7(@mantine/core@7.17.0(@mantine/hooks@7.17.0(react@19.0.0))(@types/react@19.0.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0))(@mantine/hooks@7.17.0(react@19.0.0))(dayjs@1.11.13)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
        dependencies:
            '@mantine/core': 7.17.0(@mantine/hooks@7.17.0(react@19.0.0))(@types/react@19.0.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
            '@mantine/hooks': 7.17.0(react@19.0.0)
            clsx: 2.1.1
            dayjs: 1.11.13
            react: 19.0.0
            react-dom: 19.0.0(react@19.0.0)

    '@mantine/hooks@7.17.0(react@19.0.0)':
        dependencies:
            react: 19.0.0

    '@next/env@15.1.6': {}

    '@next/eslint-plugin-next@15.1.6':
        dependencies:
            fast-glob: 3.3.1

    '@next/swc-darwin-arm64@15.1.6':
        optional: true

    '@next/swc-darwin-x64@15.1.6':
        optional: true

    '@next/swc-linux-arm64-gnu@15.1.6':
        optional: true

    '@next/swc-linux-arm64-musl@15.1.6':
        optional: true

    '@next/swc-linux-x64-gnu@15.1.6':
        optional: true

    '@next/swc-linux-x64-musl@15.1.6':
        optional: true

    '@next/swc-win32-arm64-msvc@15.1.6':
        optional: true

    '@next/swc-win32-x64-msvc@15.1.6':
        optional: true

    '@nodelib/fs.scandir@2.1.5':
        dependencies:
            '@nodelib/fs.stat': 2.0.5
            run-parallel: 1.2.0

    '@nodelib/fs.stat@2.0.5': {}

    '@nodelib/fs.walk@1.2.8':
        dependencies:
            '@nodelib/fs.scandir': 2.1.5
            fastq: 1.18.0

    '@nolyfill/is-core-module@1.0.39': {}

    '@pkgjs/parseargs@0.11.0':
        optional: true

    '@rtsao/scc@1.1.0': {}

    '@rushstack/eslint-patch@1.10.5': {}

    '@swc/counter@0.1.3': {}

    '@swc/helpers@0.5.15':
        dependencies:
            tslib: 2.8.1

    '@tanstack/query-core@5.71.1': {}

    '@tanstack/react-query@5.71.1(react@19.0.0)':
        dependencies:
            '@tanstack/query-core': 5.71.1
            react: 19.0.0

    '@types/aos@3.0.7': {}

    '@types/axios@0.14.4':
        dependencies:
            axios: 1.7.9
        transitivePeerDependencies:
            - debug

    '@types/estree@1.0.6': {}

    '@types/json-schema@7.0.15': {}

    '@types/json5@0.0.29': {}

    '@types/node@20.17.16':
        dependencies:
            undici-types: 6.19.8

    '@types/react-dom@19.0.3(@types/react@19.0.8)':
        dependencies:
            '@types/react': 19.0.8

    '@types/react@19.0.8':
        dependencies:
            csstype: 3.1.3

    '@typescript-eslint/eslint-plugin@8.22.0(@typescript-eslint/parser@8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3))(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)':
        dependencies:
            '@eslint-community/regexpp': 4.12.1
            '@typescript-eslint/parser': 8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)
            '@typescript-eslint/scope-manager': 8.22.0
            '@typescript-eslint/type-utils': 8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)
            '@typescript-eslint/utils': 8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)
            '@typescript-eslint/visitor-keys': 8.22.0
            eslint: 9.19.0(jiti@1.21.7)
            graphemer: 1.4.0
            ignore: 5.3.2
            natural-compare: 1.4.0
            ts-api-utils: 2.0.0(typescript@5.7.3)
            typescript: 5.7.3
        transitivePeerDependencies:
            - supports-color

    '@typescript-eslint/parser@8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)':
        dependencies:
            '@typescript-eslint/scope-manager': 8.22.0
            '@typescript-eslint/types': 8.22.0
            '@typescript-eslint/typescript-estree': 8.22.0(typescript@5.7.3)
            '@typescript-eslint/visitor-keys': 8.22.0
            debug: 4.4.0
            eslint: 9.19.0(jiti@1.21.7)
            typescript: 5.7.3
        transitivePeerDependencies:
            - supports-color

    '@typescript-eslint/scope-manager@8.22.0':
        dependencies:
            '@typescript-eslint/types': 8.22.0
            '@typescript-eslint/visitor-keys': 8.22.0

    '@typescript-eslint/type-utils@8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)':
        dependencies:
            '@typescript-eslint/typescript-estree': 8.22.0(typescript@5.7.3)
            '@typescript-eslint/utils': 8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)
            debug: 4.4.0
            eslint: 9.19.0(jiti@1.21.7)
            ts-api-utils: 2.0.0(typescript@5.7.3)
            typescript: 5.7.3
        transitivePeerDependencies:
            - supports-color

    '@typescript-eslint/types@8.22.0': {}

    '@typescript-eslint/typescript-estree@8.22.0(typescript@5.7.3)':
        dependencies:
            '@typescript-eslint/types': 8.22.0
            '@typescript-eslint/visitor-keys': 8.22.0
            debug: 4.4.0
            fast-glob: 3.3.3
            is-glob: 4.0.3
            minimatch: 9.0.5
            semver: 7.6.3
            ts-api-utils: 2.0.0(typescript@5.7.3)
            typescript: 5.7.3
        transitivePeerDependencies:
            - supports-color

    '@typescript-eslint/utils@8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)':
        dependencies:
            '@eslint-community/eslint-utils': 4.4.1(eslint@9.19.0(jiti@1.21.7))
            '@typescript-eslint/scope-manager': 8.22.0
            '@typescript-eslint/types': 8.22.0
            '@typescript-eslint/typescript-estree': 8.22.0(typescript@5.7.3)
            eslint: 9.19.0(jiti@1.21.7)
            typescript: 5.7.3
        transitivePeerDependencies:
            - supports-color

    '@typescript-eslint/visitor-keys@8.22.0':
        dependencies:
            '@typescript-eslint/types': 8.22.0
            eslint-visitor-keys: 4.2.0

    acorn-jsx@5.3.2(acorn@8.14.0):
        dependencies:
            acorn: 8.14.0

    acorn@8.14.0: {}

    ajv@6.12.6:
        dependencies:
            fast-deep-equal: 3.1.3
            fast-json-stable-stringify: 2.1.0
            json-schema-traverse: 0.4.1
            uri-js: 4.4.1

    ansi-escapes@7.0.0:
        dependencies:
            environment: 1.1.0

    ansi-regex@5.0.1: {}

    ansi-regex@6.1.0: {}

    ansi-styles@4.3.0:
        dependencies:
            color-convert: 2.0.1

    ansi-styles@6.2.1: {}

    any-promise@1.3.0: {}

    anymatch@3.1.3:
        dependencies:
            normalize-path: 3.0.0
            picomatch: 2.3.1

    aos@2.3.4:
        dependencies:
            classlist-polyfill: 1.2.0
            lodash.debounce: 4.0.8
            lodash.throttle: 4.1.1

    arg@5.0.2: {}

    argparse@2.0.1: {}

    aria-query@5.3.2: {}

    array-buffer-byte-length@1.0.2:
        dependencies:
            call-bound: 1.0.3
            is-array-buffer: 3.0.5

    array-includes@3.1.8:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-object-atoms: 1.1.1
            get-intrinsic: 1.2.7
            is-string: 1.1.1

    array.prototype.findlast@1.2.5:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            es-object-atoms: 1.1.1
            es-shim-unscopables: 1.0.2

    array.prototype.findlastindex@1.2.5:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            es-object-atoms: 1.1.1
            es-shim-unscopables: 1.0.2

    array.prototype.flat@1.3.3:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-shim-unscopables: 1.0.2

    array.prototype.flatmap@1.3.3:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-shim-unscopables: 1.0.2

    array.prototype.tosorted@1.1.4:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            es-shim-unscopables: 1.0.2

    arraybuffer.prototype.slice@1.0.4:
        dependencies:
            array-buffer-byte-length: 1.0.2
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            get-intrinsic: 1.2.7
            is-array-buffer: 3.0.5

    ast-types-flow@0.0.8: {}

    async-function@1.0.0: {}

    asynckit@0.4.0: {}

    available-typed-arrays@1.0.7:
        dependencies:
            possible-typed-array-names: 1.0.0

    axe-core@4.10.2: {}

    axios@1.7.9:
        dependencies:
            follow-redirects: 1.15.9
            form-data: 4.0.2
            proxy-from-env: 1.1.0
        transitivePeerDependencies:
            - debug

    axobject-query@4.1.0: {}

    balanced-match@1.0.2: {}

    binary-extensions@2.3.0: {}

    brace-expansion@1.1.11:
        dependencies:
            balanced-match: 1.0.2
            concat-map: 0.0.1

    brace-expansion@2.0.1:
        dependencies:
            balanced-match: 1.0.2

    braces@3.0.3:
        dependencies:
            fill-range: 7.1.1

    busboy@1.6.0:
        dependencies:
            streamsearch: 1.1.0

    call-bind-apply-helpers@1.0.1:
        dependencies:
            es-errors: 1.3.0
            function-bind: 1.1.2

    call-bind@1.0.8:
        dependencies:
            call-bind-apply-helpers: 1.0.1
            es-define-property: 1.0.1
            get-intrinsic: 1.2.7
            set-function-length: 1.2.2

    call-bound@1.0.3:
        dependencies:
            call-bind-apply-helpers: 1.0.1
            get-intrinsic: 1.2.7

    callsites@3.1.0: {}

    camelcase-css@2.0.1: {}

    caniuse-lite@1.0.30001695: {}

    chalk@4.1.2:
        dependencies:
            ansi-styles: 4.3.0
            supports-color: 7.2.0

    chalk@5.4.1: {}

    chokidar@3.6.0:
        dependencies:
            anymatch: 3.1.3
            braces: 3.0.3
            glob-parent: 5.1.2
            is-binary-path: 2.1.0
            is-glob: 4.0.3
            normalize-path: 3.0.0
            readdirp: 3.6.0
        optionalDependencies:
            fsevents: 2.3.3

    classlist-polyfill@1.2.0: {}

    cli-cursor@5.0.0:
        dependencies:
            restore-cursor: 5.1.0

    cli-truncate@4.0.0:
        dependencies:
            slice-ansi: 5.0.0
            string-width: 7.2.0

    client-only@0.0.1: {}

    clsx@1.2.1: {}

    clsx@2.1.1: {}

    color-convert@2.0.1:
        dependencies:
            color-name: 1.1.4

    color-name@1.1.4: {}

    color-string@1.9.1:
        dependencies:
            color-name: 1.1.4
            simple-swizzle: 0.2.2

    color@4.2.3:
        dependencies:
            color-convert: 2.0.1
            color-string: 1.9.1

    colorette@2.0.20: {}

    combined-stream@1.0.8:
        dependencies:
            delayed-stream: 1.0.0

    commander@13.1.0: {}

    commander@4.1.1: {}

    concat-map@0.0.1: {}

    cross-spawn@7.0.6:
        dependencies:
            path-key: 3.1.1
            shebang-command: 2.0.0
            which: 2.0.2

    cssesc@3.0.0: {}

    csstype@3.1.3: {}

    damerau-levenshtein@1.0.8: {}

    data-view-buffer@1.0.2:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            is-data-view: 1.0.2

    data-view-byte-length@1.0.2:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            is-data-view: 1.0.2

    data-view-byte-offset@1.0.1:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            is-data-view: 1.0.2

    dayjs@1.11.13: {}

    debug@3.2.7:
        dependencies:
            ms: 2.1.3

    debug@4.4.0:
        dependencies:
            ms: 2.1.3

    deep-is@0.1.4: {}

    define-data-property@1.1.4:
        dependencies:
            es-define-property: 1.0.1
            es-errors: 1.3.0
            gopd: 1.2.0

    define-properties@1.2.1:
        dependencies:
            define-data-property: 1.1.4
            has-property-descriptors: 1.0.2
            object-keys: 1.1.1

    delayed-stream@1.0.0: {}

    detect-libc@2.0.3: {}

    detect-node-es@1.1.0: {}

    didyoumean@1.2.2: {}

    dinero.js@2.0.0-alpha.14:
        dependencies:
            '@dinero.js/calculator-number': 2.0.0-alpha.14
            '@dinero.js/core': 2.0.0-alpha.14
            '@dinero.js/currencies': 2.0.0-alpha.14

    dlv@1.1.3: {}

    doctrine@2.1.0:
        dependencies:
            esutils: 2.0.3

    dunder-proto@1.0.1:
        dependencies:
            call-bind-apply-helpers: 1.0.1
            es-errors: 1.3.0
            gopd: 1.2.0

    eastasianwidth@0.2.0: {}

    emoji-regex@10.4.0: {}

    emoji-regex@8.0.0: {}

    emoji-regex@9.2.2: {}

    enhanced-resolve@5.18.0:
        dependencies:
            graceful-fs: 4.2.11
            tapable: 2.2.1

    environment@1.1.0: {}

    es-abstract@1.23.9:
        dependencies:
            array-buffer-byte-length: 1.0.2
            arraybuffer.prototype.slice: 1.0.4
            available-typed-arrays: 1.0.7
            call-bind: 1.0.8
            call-bound: 1.0.3
            data-view-buffer: 1.0.2
            data-view-byte-length: 1.0.2
            data-view-byte-offset: 1.0.1
            es-define-property: 1.0.1
            es-errors: 1.3.0
            es-object-atoms: 1.1.1
            es-set-tostringtag: 2.1.0
            es-to-primitive: 1.3.0
            function.prototype.name: 1.1.8
            get-intrinsic: 1.2.7
            get-proto: 1.0.1
            get-symbol-description: 1.1.0
            globalthis: 1.0.4
            gopd: 1.2.0
            has-property-descriptors: 1.0.2
            has-proto: 1.2.0
            has-symbols: 1.1.0
            hasown: 2.0.2
            internal-slot: 1.1.0
            is-array-buffer: 3.0.5
            is-callable: 1.2.7
            is-data-view: 1.0.2
            is-regex: 1.2.1
            is-shared-array-buffer: 1.0.4
            is-string: 1.1.1
            is-typed-array: 1.1.15
            is-weakref: 1.1.0
            math-intrinsics: 1.1.0
            object-inspect: 1.13.3
            object-keys: 1.1.1
            object.assign: 4.1.7
            own-keys: 1.0.1
            regexp.prototype.flags: 1.5.4
            safe-array-concat: 1.1.3
            safe-push-apply: 1.0.0
            safe-regex-test: 1.1.0
            set-proto: 1.0.0
            string.prototype.trim: 1.2.10
            string.prototype.trimend: 1.0.9
            string.prototype.trimstart: 1.0.8
            typed-array-buffer: 1.0.3
            typed-array-byte-length: 1.0.3
            typed-array-byte-offset: 1.0.4
            typed-array-length: 1.0.7
            unbox-primitive: 1.1.0
            which-typed-array: 1.1.18

    es-define-property@1.0.1: {}

    es-errors@1.3.0: {}

    es-iterator-helpers@1.2.1:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            es-set-tostringtag: 2.1.0
            function-bind: 1.1.2
            get-intrinsic: 1.2.7
            globalthis: 1.0.4
            gopd: 1.2.0
            has-property-descriptors: 1.0.2
            has-proto: 1.2.0
            has-symbols: 1.1.0
            internal-slot: 1.1.0
            iterator.prototype: 1.1.5
            safe-array-concat: 1.1.3

    es-object-atoms@1.1.1:
        dependencies:
            es-errors: 1.3.0

    es-set-tostringtag@2.1.0:
        dependencies:
            es-errors: 1.3.0
            get-intrinsic: 1.2.7
            has-tostringtag: 1.0.2
            hasown: 2.0.2

    es-shim-unscopables@1.0.2:
        dependencies:
            hasown: 2.0.2

    es-to-primitive@1.3.0:
        dependencies:
            is-callable: 1.2.7
            is-date-object: 1.1.0
            is-symbol: 1.1.1

    escape-string-regexp@4.0.0: {}

    eslint-config-next@15.1.6(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3):
        dependencies:
            '@next/eslint-plugin-next': 15.1.6
            '@rushstack/eslint-patch': 1.10.5
            '@typescript-eslint/eslint-plugin': 8.22.0(@typescript-eslint/parser@8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3))(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)
            '@typescript-eslint/parser': 8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)
            eslint: 9.19.0(jiti@1.21.7)
            eslint-import-resolver-node: 0.3.9
            eslint-import-resolver-typescript: 3.7.0(eslint-plugin-import@2.31.0)(eslint@9.19.0(jiti@1.21.7))
            eslint-plugin-import: 2.31.0(@typescript-eslint/parser@8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3))(eslint-import-resolver-typescript@3.7.0)(eslint@9.19.0(jiti@1.21.7))
            eslint-plugin-jsx-a11y: 6.10.2(eslint@9.19.0(jiti@1.21.7))
            eslint-plugin-react: 7.37.4(eslint@9.19.0(jiti@1.21.7))
            eslint-plugin-react-hooks: 5.1.0(eslint@9.19.0(jiti@1.21.7))
        optionalDependencies:
            typescript: 5.7.3
        transitivePeerDependencies:
            - eslint-import-resolver-webpack
            - eslint-plugin-import-x
            - supports-color

    eslint-import-resolver-node@0.3.9:
        dependencies:
            debug: 3.2.7
            is-core-module: 2.16.1
            resolve: 1.22.10
        transitivePeerDependencies:
            - supports-color

    eslint-import-resolver-typescript@3.7.0(eslint-plugin-import@2.31.0)(eslint@9.19.0(jiti@1.21.7)):
        dependencies:
            '@nolyfill/is-core-module': 1.0.39
            debug: 4.4.0
            enhanced-resolve: 5.18.0
            eslint: 9.19.0(jiti@1.21.7)
            fast-glob: 3.3.3
            get-tsconfig: 4.10.0
            is-bun-module: 1.3.0
            is-glob: 4.0.3
            stable-hash: 0.0.4
        optionalDependencies:
            eslint-plugin-import: 2.31.0(@typescript-eslint/parser@8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3))(eslint-import-resolver-typescript@3.7.0)(eslint@9.19.0(jiti@1.21.7))
        transitivePeerDependencies:
            - supports-color

    eslint-module-utils@2.12.0(@typescript-eslint/parser@8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.7.0)(eslint@9.19.0(jiti@1.21.7)):
        dependencies:
            debug: 3.2.7
        optionalDependencies:
            '@typescript-eslint/parser': 8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)
            eslint: 9.19.0(jiti@1.21.7)
            eslint-import-resolver-node: 0.3.9
            eslint-import-resolver-typescript: 3.7.0(eslint-plugin-import@2.31.0)(eslint@9.19.0(jiti@1.21.7))
        transitivePeerDependencies:
            - supports-color

    eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3))(eslint-import-resolver-typescript@3.7.0)(eslint@9.19.0(jiti@1.21.7)):
        dependencies:
            '@rtsao/scc': 1.1.0
            array-includes: 3.1.8
            array.prototype.findlastindex: 1.2.5
            array.prototype.flat: 1.3.3
            array.prototype.flatmap: 1.3.3
            debug: 3.2.7
            doctrine: 2.1.0
            eslint: 9.19.0(jiti@1.21.7)
            eslint-import-resolver-node: 0.3.9
            eslint-module-utils: 2.12.0(@typescript-eslint/parser@8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.7.0)(eslint@9.19.0(jiti@1.21.7))
            hasown: 2.0.2
            is-core-module: 2.16.1
            is-glob: 4.0.3
            minimatch: 3.1.2
            object.fromentries: 2.0.8
            object.groupby: 1.0.3
            object.values: 1.2.1
            semver: 6.3.1
            string.prototype.trimend: 1.0.9
            tsconfig-paths: 3.15.0
        optionalDependencies:
            '@typescript-eslint/parser': 8.22.0(eslint@9.19.0(jiti@1.21.7))(typescript@5.7.3)
        transitivePeerDependencies:
            - eslint-import-resolver-typescript
            - eslint-import-resolver-webpack
            - supports-color

    eslint-plugin-jsx-a11y@6.10.2(eslint@9.19.0(jiti@1.21.7)):
        dependencies:
            aria-query: 5.3.2
            array-includes: 3.1.8
            array.prototype.flatmap: 1.3.3
            ast-types-flow: 0.0.8
            axe-core: 4.10.2
            axobject-query: 4.1.0
            damerau-levenshtein: 1.0.8
            emoji-regex: 9.2.2
            eslint: 9.19.0(jiti@1.21.7)
            hasown: 2.0.2
            jsx-ast-utils: 3.3.5
            language-tags: 1.0.9
            minimatch: 3.1.2
            object.fromentries: 2.0.8
            safe-regex-test: 1.1.0
            string.prototype.includes: 2.0.1

    eslint-plugin-react-hooks@5.1.0(eslint@9.19.0(jiti@1.21.7)):
        dependencies:
            eslint: 9.19.0(jiti@1.21.7)

    eslint-plugin-react@7.37.4(eslint@9.19.0(jiti@1.21.7)):
        dependencies:
            array-includes: 3.1.8
            array.prototype.findlast: 1.2.5
            array.prototype.flatmap: 1.3.3
            array.prototype.tosorted: 1.1.4
            doctrine: 2.1.0
            es-iterator-helpers: 1.2.1
            eslint: 9.19.0(jiti@1.21.7)
            estraverse: 5.3.0
            hasown: 2.0.2
            jsx-ast-utils: 3.3.5
            minimatch: 3.1.2
            object.entries: 1.1.8
            object.fromentries: 2.0.8
            object.values: 1.2.1
            prop-types: 15.8.1
            resolve: 2.0.0-next.5
            semver: 6.3.1
            string.prototype.matchall: 4.0.12
            string.prototype.repeat: 1.0.0

    eslint-scope@8.2.0:
        dependencies:
            esrecurse: 4.3.0
            estraverse: 5.3.0

    eslint-visitor-keys@3.4.3: {}

    eslint-visitor-keys@4.2.0: {}

    eslint@9.19.0(jiti@1.21.7):
        dependencies:
            '@eslint-community/eslint-utils': 4.4.1(eslint@9.19.0(jiti@1.21.7))
            '@eslint-community/regexpp': 4.12.1
            '@eslint/config-array': 0.19.1
            '@eslint/core': 0.10.0
            '@eslint/eslintrc': 3.2.0
            '@eslint/js': 9.19.0
            '@eslint/plugin-kit': 0.2.5
            '@humanfs/node': 0.16.6
            '@humanwhocodes/module-importer': 1.0.1
            '@humanwhocodes/retry': 0.4.1
            '@types/estree': 1.0.6
            '@types/json-schema': 7.0.15
            ajv: 6.12.6
            chalk: 4.1.2
            cross-spawn: 7.0.6
            debug: 4.4.0
            escape-string-regexp: 4.0.0
            eslint-scope: 8.2.0
            eslint-visitor-keys: 4.2.0
            espree: 10.3.0
            esquery: 1.6.0
            esutils: 2.0.3
            fast-deep-equal: 3.1.3
            file-entry-cache: 8.0.0
            find-up: 5.0.0
            glob-parent: 6.0.2
            ignore: 5.3.2
            imurmurhash: 0.1.4
            is-glob: 4.0.3
            json-stable-stringify-without-jsonify: 1.0.1
            lodash.merge: 4.6.2
            minimatch: 3.1.2
            natural-compare: 1.4.0
            optionator: 0.9.4
        optionalDependencies:
            jiti: 1.21.7
        transitivePeerDependencies:
            - supports-color

    espree@10.3.0:
        dependencies:
            acorn: 8.14.0
            acorn-jsx: 5.3.2(acorn@8.14.0)
            eslint-visitor-keys: 4.2.0

    esquery@1.6.0:
        dependencies:
            estraverse: 5.3.0

    esrecurse@4.3.0:
        dependencies:
            estraverse: 5.3.0

    estraverse@5.3.0: {}

    esutils@2.0.3: {}

    eventemitter3@5.0.1: {}

    execa@8.0.1:
        dependencies:
            cross-spawn: 7.0.6
            get-stream: 8.0.1
            human-signals: 5.0.0
            is-stream: 3.0.0
            merge-stream: 2.0.0
            npm-run-path: 5.3.0
            onetime: 6.0.0
            signal-exit: 4.1.0
            strip-final-newline: 3.0.0

    fast-deep-equal@3.1.3: {}

    fast-glob@3.3.1:
        dependencies:
            '@nodelib/fs.stat': 2.0.5
            '@nodelib/fs.walk': 1.2.8
            glob-parent: 5.1.2
            merge2: 1.4.1
            micromatch: 4.0.8

    fast-glob@3.3.3:
        dependencies:
            '@nodelib/fs.stat': 2.0.5
            '@nodelib/fs.walk': 1.2.8
            glob-parent: 5.1.2
            merge2: 1.4.1
            micromatch: 4.0.8

    fast-json-stable-stringify@2.1.0: {}

    fast-levenshtein@2.0.6: {}

    fastq@1.18.0:
        dependencies:
            reusify: 1.0.4

    file-entry-cache@8.0.0:
        dependencies:
            flat-cache: 4.0.1

    fill-range@7.1.1:
        dependencies:
            to-regex-range: 5.0.1

    find-up@5.0.0:
        dependencies:
            locate-path: 6.0.0
            path-exists: 4.0.0

    flat-cache@4.0.1:
        dependencies:
            flatted: 3.3.2
            keyv: 4.5.4

    flatted@3.3.2: {}

    follow-redirects@1.15.9: {}

    for-each@0.3.4:
        dependencies:
            is-callable: 1.2.7

    foreground-child@3.3.0:
        dependencies:
            cross-spawn: 7.0.6
            signal-exit: 4.1.0

    form-data@4.0.2:
        dependencies:
            asynckit: 0.4.0
            combined-stream: 1.0.8
            es-set-tostringtag: 2.1.0
            mime-types: 2.1.35

    framer-motion@12.0.6(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
        dependencies:
            motion-dom: 12.0.0
            motion-utils: 12.0.0
            tslib: 2.8.1
        optionalDependencies:
            react: 19.0.0
            react-dom: 19.0.0(react@19.0.0)

    fsevents@2.3.3:
        optional: true

    function-bind@1.1.2: {}

    function.prototype.name@1.1.8:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-properties: 1.2.1
            functions-have-names: 1.2.3
            hasown: 2.0.2
            is-callable: 1.2.7

    functions-have-names@1.2.3: {}

    get-east-asian-width@1.3.0: {}

    get-intrinsic@1.2.7:
        dependencies:
            call-bind-apply-helpers: 1.0.1
            es-define-property: 1.0.1
            es-errors: 1.3.0
            es-object-atoms: 1.1.1
            function-bind: 1.1.2
            get-proto: 1.0.1
            gopd: 1.2.0
            has-symbols: 1.1.0
            hasown: 2.0.2
            math-intrinsics: 1.1.0

    get-nonce@1.0.1: {}

    get-proto@1.0.1:
        dependencies:
            dunder-proto: 1.0.1
            es-object-atoms: 1.1.1

    get-stream@8.0.1: {}

    get-symbol-description@1.1.0:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            get-intrinsic: 1.2.7

    get-tsconfig@4.10.0:
        dependencies:
            resolve-pkg-maps: 1.0.0

    glob-parent@5.1.2:
        dependencies:
            is-glob: 4.0.3

    glob-parent@6.0.2:
        dependencies:
            is-glob: 4.0.3

    glob@10.4.5:
        dependencies:
            foreground-child: 3.3.0
            jackspeak: 3.4.3
            minimatch: 9.0.5
            minipass: 7.1.2
            package-json-from-dist: 1.0.1
            path-scurry: 1.11.1

    globals@14.0.0: {}

    globalthis@1.0.4:
        dependencies:
            define-properties: 1.2.1
            gopd: 1.2.0

    goober@2.1.16(csstype@3.1.3):
        dependencies:
            csstype: 3.1.3

    gopd@1.2.0: {}

    graceful-fs@4.2.11: {}

    graphemer@1.4.0: {}

    has-bigints@1.1.0: {}

    has-flag@4.0.0: {}

    has-property-descriptors@1.0.2:
        dependencies:
            es-define-property: 1.0.1

    has-proto@1.2.0:
        dependencies:
            dunder-proto: 1.0.1

    has-symbols@1.1.0: {}

    has-tostringtag@1.0.2:
        dependencies:
            has-symbols: 1.1.0

    hasown@2.0.2:
        dependencies:
            function-bind: 1.1.2

    human-signals@5.0.0: {}

    husky@9.1.7: {}

    ignore@5.3.2: {}

    import-fresh@3.3.0:
        dependencies:
            parent-module: 1.0.1
            resolve-from: 4.0.0

    imurmurhash@0.1.4: {}

    internal-slot@1.1.0:
        dependencies:
            es-errors: 1.3.0
            hasown: 2.0.2
            side-channel: 1.1.0

    is-array-buffer@3.0.5:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            get-intrinsic: 1.2.7

    is-arrayish@0.3.2: {}

    is-async-function@2.1.1:
        dependencies:
            async-function: 1.0.0
            call-bound: 1.0.3
            get-proto: 1.0.1
            has-tostringtag: 1.0.2
            safe-regex-test: 1.1.0

    is-bigint@1.1.0:
        dependencies:
            has-bigints: 1.1.0

    is-binary-path@2.1.0:
        dependencies:
            binary-extensions: 2.3.0

    is-boolean-object@1.2.1:
        dependencies:
            call-bound: 1.0.3
            has-tostringtag: 1.0.2

    is-bun-module@1.3.0:
        dependencies:
            semver: 7.6.3

    is-callable@1.2.7: {}

    is-core-module@2.16.1:
        dependencies:
            hasown: 2.0.2

    is-data-view@1.0.2:
        dependencies:
            call-bound: 1.0.3
            get-intrinsic: 1.2.7
            is-typed-array: 1.1.15

    is-date-object@1.1.0:
        dependencies:
            call-bound: 1.0.3
            has-tostringtag: 1.0.2

    is-extglob@2.1.1: {}

    is-finalizationregistry@1.1.1:
        dependencies:
            call-bound: 1.0.3

    is-fullwidth-code-point@3.0.0: {}

    is-fullwidth-code-point@4.0.0: {}

    is-fullwidth-code-point@5.0.0:
        dependencies:
            get-east-asian-width: 1.3.0

    is-generator-function@1.1.0:
        dependencies:
            call-bound: 1.0.3
            get-proto: 1.0.1
            has-tostringtag: 1.0.2
            safe-regex-test: 1.1.0

    is-glob@4.0.3:
        dependencies:
            is-extglob: 2.1.1

    is-map@2.0.3: {}

    is-number-object@1.1.1:
        dependencies:
            call-bound: 1.0.3
            has-tostringtag: 1.0.2

    is-number@7.0.0: {}

    is-regex@1.2.1:
        dependencies:
            call-bound: 1.0.3
            gopd: 1.2.0
            has-tostringtag: 1.0.2
            hasown: 2.0.2

    is-set@2.0.3: {}

    is-shared-array-buffer@1.0.4:
        dependencies:
            call-bound: 1.0.3

    is-stream@3.0.0: {}

    is-string@1.1.1:
        dependencies:
            call-bound: 1.0.3
            has-tostringtag: 1.0.2

    is-symbol@1.1.1:
        dependencies:
            call-bound: 1.0.3
            has-symbols: 1.1.0
            safe-regex-test: 1.1.0

    is-typed-array@1.1.15:
        dependencies:
            which-typed-array: 1.1.18

    is-weakmap@2.0.2: {}

    is-weakref@1.1.0:
        dependencies:
            call-bound: 1.0.3

    is-weakset@2.0.4:
        dependencies:
            call-bound: 1.0.3
            get-intrinsic: 1.2.7

    isarray@2.0.5: {}

    isexe@2.0.0: {}

    iterator.prototype@1.1.5:
        dependencies:
            define-data-property: 1.1.4
            es-object-atoms: 1.1.1
            get-intrinsic: 1.2.7
            get-proto: 1.0.1
            has-symbols: 1.1.0
            set-function-name: 2.0.2

    jackspeak@3.4.3:
        dependencies:
            '@isaacs/cliui': 8.0.2
        optionalDependencies:
            '@pkgjs/parseargs': 0.11.0

    jiti@1.21.7: {}

    js-tokens@4.0.0: {}

    js-yaml@4.1.0:
        dependencies:
            argparse: 2.0.1

    json-buffer@3.0.1: {}

    json-schema-traverse@0.4.1: {}

    json-stable-stringify-without-jsonify@1.0.1: {}

    json5@1.0.2:
        dependencies:
            minimist: 1.2.8

    jsx-ast-utils@3.3.5:
        dependencies:
            array-includes: 3.1.8
            array.prototype.flat: 1.3.3
            object.assign: 4.1.7
            object.values: 1.2.1

    keyv@4.5.4:
        dependencies:
            json-buffer: 3.0.1

    language-subtag-registry@0.3.23: {}

    language-tags@1.0.9:
        dependencies:
            language-subtag-registry: 0.3.23

    levn@0.4.1:
        dependencies:
            prelude-ls: 1.2.1
            type-check: 0.4.0

    lilconfig@3.1.3: {}

    lines-and-columns@1.2.4: {}

    lint-staged@15.4.3:
        dependencies:
            chalk: 5.4.1
            commander: 13.1.0
            debug: 4.4.0
            execa: 8.0.1
            lilconfig: 3.1.3
            listr2: 8.2.5
            micromatch: 4.0.8
            pidtree: 0.6.0
            string-argv: 0.3.2
            yaml: 2.7.0
        transitivePeerDependencies:
            - supports-color

    listr2@8.2.5:
        dependencies:
            cli-truncate: 4.0.0
            colorette: 2.0.20
            eventemitter3: 5.0.1
            log-update: 6.1.0
            rfdc: 1.4.1
            wrap-ansi: 9.0.0

    locate-path@6.0.0:
        dependencies:
            p-locate: 5.0.0

    lodash.debounce@4.0.8: {}

    lodash.merge@4.6.2: {}

    lodash.throttle@4.1.1: {}

    log-update@6.1.0:
        dependencies:
            ansi-escapes: 7.0.0
            cli-cursor: 5.0.0
            slice-ansi: 7.1.0
            strip-ansi: 7.1.0
            wrap-ansi: 9.0.0

    loose-envify@1.4.0:
        dependencies:
            js-tokens: 4.0.0

    lru-cache@10.4.3: {}

    lucide-react@0.476.0(react@19.0.0):
        dependencies:
            react: 19.0.0

    math-intrinsics@1.1.0: {}

    merge-stream@2.0.0: {}

    merge2@1.4.1: {}

    micromatch@4.0.8:
        dependencies:
            braces: 3.0.3
            picomatch: 2.3.1

    mime-db@1.52.0: {}

    mime-types@2.1.35:
        dependencies:
            mime-db: 1.52.0

    mimic-fn@4.0.0: {}

    mimic-function@5.0.1: {}

    minimatch@3.1.2:
        dependencies:
            brace-expansion: 1.1.11

    minimatch@9.0.5:
        dependencies:
            brace-expansion: 2.0.1

    minimist@1.2.8: {}

    minipass@7.1.2: {}

    motion-dom@12.0.0:
        dependencies:
            motion-utils: 12.0.0

    motion-utils@12.0.0: {}

    ms@2.1.3: {}

    mz@2.7.0:
        dependencies:
            any-promise: 1.3.0
            object-assign: 4.1.1
            thenify-all: 1.6.0

    nanoid@3.3.8: {}

    natural-compare@1.4.0: {}

    next-themes@0.4.6(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
        dependencies:
            react: 19.0.0
            react-dom: 19.0.0(react@19.0.0)

    next@15.1.6(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
        dependencies:
            '@next/env': 15.1.6
            '@swc/counter': 0.1.3
            '@swc/helpers': 0.5.15
            busboy: 1.6.0
            caniuse-lite: 1.0.30001695
            postcss: 8.4.31
            react: 19.0.0
            react-dom: 19.0.0(react@19.0.0)
            styled-jsx: 5.1.6(react@19.0.0)
        optionalDependencies:
            '@next/swc-darwin-arm64': 15.1.6
            '@next/swc-darwin-x64': 15.1.6
            '@next/swc-linux-arm64-gnu': 15.1.6
            '@next/swc-linux-arm64-musl': 15.1.6
            '@next/swc-linux-x64-gnu': 15.1.6
            '@next/swc-linux-x64-musl': 15.1.6
            '@next/swc-win32-arm64-msvc': 15.1.6
            '@next/swc-win32-x64-msvc': 15.1.6
            sharp: 0.33.5
        transitivePeerDependencies:
            - '@babel/core'
            - babel-plugin-macros

    normalize-path@3.0.0: {}

    notistack@3.0.2(csstype@3.1.3)(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
        dependencies:
            clsx: 1.2.1
            goober: 2.1.16(csstype@3.1.3)
            react: 19.0.0
            react-dom: 19.0.0(react@19.0.0)
        transitivePeerDependencies:
            - csstype

    npm-run-path@5.3.0:
        dependencies:
            path-key: 4.0.0

    object-assign@4.1.1: {}

    object-hash@3.0.0: {}

    object-inspect@1.13.3: {}

    object-keys@1.1.1: {}

    object.assign@4.1.7:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-properties: 1.2.1
            es-object-atoms: 1.1.1
            has-symbols: 1.1.0
            object-keys: 1.1.1

    object.entries@1.1.8:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-object-atoms: 1.1.1

    object.fromentries@2.0.8:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-object-atoms: 1.1.1

    object.groupby@1.0.3:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9

    object.values@1.2.1:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-properties: 1.2.1
            es-object-atoms: 1.1.1

    onetime@6.0.0:
        dependencies:
            mimic-fn: 4.0.0

    onetime@7.0.0:
        dependencies:
            mimic-function: 5.0.1

    optionator@0.9.4:
        dependencies:
            deep-is: 0.1.4
            fast-levenshtein: 2.0.6
            levn: 0.4.1
            prelude-ls: 1.2.1
            type-check: 0.4.0
            word-wrap: 1.2.5

    own-keys@1.0.1:
        dependencies:
            get-intrinsic: 1.2.7
            object-keys: 1.1.1
            safe-push-apply: 1.0.0

    p-limit@3.1.0:
        dependencies:
            yocto-queue: 0.1.0

    p-locate@5.0.0:
        dependencies:
            p-limit: 3.1.0

    p-throttle@7.0.0: {}

    package-json-from-dist@1.0.1: {}

    parent-module@1.0.1:
        dependencies:
            callsites: 3.1.0

    path-exists@4.0.0: {}

    path-key@3.1.1: {}

    path-key@4.0.0: {}

    path-parse@1.0.7: {}

    path-scurry@1.11.1:
        dependencies:
            lru-cache: 10.4.3
            minipass: 7.1.2

    picocolors@1.1.1: {}

    picomatch@2.3.1: {}

    pidtree@0.6.0: {}

    pify@2.3.0: {}

    pirates@4.0.6: {}

    possible-typed-array-names@1.0.0: {}

    postcss-import@15.1.0(postcss@8.5.1):
        dependencies:
            postcss: 8.5.1
            postcss-value-parser: 4.2.0
            read-cache: 1.0.0
            resolve: 1.22.10

    postcss-js@4.0.1(postcss@8.5.1):
        dependencies:
            camelcase-css: 2.0.1
            postcss: 8.5.1

    postcss-load-config@4.0.2(postcss@8.5.1):
        dependencies:
            lilconfig: 3.1.3
            yaml: 2.7.0
        optionalDependencies:
            postcss: 8.5.1

    postcss-nested@6.2.0(postcss@8.5.1):
        dependencies:
            postcss: 8.5.1
            postcss-selector-parser: 6.1.2

    postcss-selector-parser@6.1.2:
        dependencies:
            cssesc: 3.0.0
            util-deprecate: 1.0.2

    postcss-value-parser@4.2.0: {}

    postcss@8.4.31:
        dependencies:
            nanoid: 3.3.8
            picocolors: 1.1.1
            source-map-js: 1.2.1

    postcss@8.5.1:
        dependencies:
            nanoid: 3.3.8
            picocolors: 1.1.1
            source-map-js: 1.2.1

    prelude-ls@1.2.1: {}

    prettier@3.4.2: {}

    prop-types@15.8.1:
        dependencies:
            loose-envify: 1.4.0
            object-assign: 4.1.1
            react-is: 16.13.1

    proxy-from-env@1.1.0: {}

    punycode@2.3.1: {}

    queue-microtask@1.2.3: {}

    react-countdown@2.3.6(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
        dependencies:
            prop-types: 15.8.1
            react: 19.0.0
            react-dom: 19.0.0(react@19.0.0)

    react-dom@19.0.0(react@19.0.0):
        dependencies:
            react: 19.0.0
            scheduler: 0.25.0

    react-hook-form@7.54.2(react@19.0.0):
        dependencies:
            react: 19.0.0

    react-is@16.13.1: {}

    react-number-format@5.4.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
        dependencies:
            react: 19.0.0
            react-dom: 19.0.0(react@19.0.0)

    react-remove-scroll-bar@2.3.8(@types/react@19.0.8)(react@19.0.0):
        dependencies:
            react: 19.0.0
            react-style-singleton: 2.2.3(@types/react@19.0.8)(react@19.0.0)
            tslib: 2.8.1
        optionalDependencies:
            '@types/react': 19.0.8

    react-remove-scroll@2.6.3(@types/react@19.0.8)(react@19.0.0):
        dependencies:
            react: 19.0.0
            react-remove-scroll-bar: 2.3.8(@types/react@19.0.8)(react@19.0.0)
            react-style-singleton: 2.2.3(@types/react@19.0.8)(react@19.0.0)
            tslib: 2.8.1
            use-callback-ref: 1.3.3(@types/react@19.0.8)(react@19.0.0)
            use-sidecar: 1.1.3(@types/react@19.0.8)(react@19.0.0)
        optionalDependencies:
            '@types/react': 19.0.8

    react-style-singleton@2.2.3(@types/react@19.0.8)(react@19.0.0):
        dependencies:
            get-nonce: 1.0.1
            react: 19.0.0
            tslib: 2.8.1
        optionalDependencies:
            '@types/react': 19.0.8

    react-textarea-autosize@8.5.6(@types/react@19.0.8)(react@19.0.0):
        dependencies:
            '@babel/runtime': 7.26.9
            react: 19.0.0
            use-composed-ref: 1.4.0(@types/react@19.0.8)(react@19.0.0)
            use-latest: 1.3.0(@types/react@19.0.8)(react@19.0.0)
        transitivePeerDependencies:
            - '@types/react'

    react@19.0.0: {}

    read-cache@1.0.0:
        dependencies:
            pify: 2.3.0

    readdirp@3.6.0:
        dependencies:
            picomatch: 2.3.1

    reflect.getprototypeof@1.0.10:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            es-object-atoms: 1.1.1
            get-intrinsic: 1.2.7
            get-proto: 1.0.1
            which-builtin-type: 1.2.1

    regenerator-runtime@0.14.1: {}

    regexp.prototype.flags@1.5.4:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-errors: 1.3.0
            get-proto: 1.0.1
            gopd: 1.2.0
            set-function-name: 2.0.2

    resolve-from@4.0.0: {}

    resolve-pkg-maps@1.0.0: {}

    resolve@1.22.10:
        dependencies:
            is-core-module: 2.16.1
            path-parse: 1.0.7
            supports-preserve-symlinks-flag: 1.0.0

    resolve@2.0.0-next.5:
        dependencies:
            is-core-module: 2.16.1
            path-parse: 1.0.7
            supports-preserve-symlinks-flag: 1.0.0

    restore-cursor@5.1.0:
        dependencies:
            onetime: 7.0.0
            signal-exit: 4.1.0

    reusify@1.0.4: {}

    rfdc@1.4.1: {}

    run-parallel@1.2.0:
        dependencies:
            queue-microtask: 1.2.3

    safe-array-concat@1.1.3:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            get-intrinsic: 1.2.7
            has-symbols: 1.1.0
            isarray: 2.0.5

    safe-push-apply@1.0.0:
        dependencies:
            es-errors: 1.3.0
            isarray: 2.0.5

    safe-regex-test@1.1.0:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            is-regex: 1.2.1

    scheduler@0.25.0: {}

    semver@6.3.1: {}

    semver@7.6.3: {}

    set-function-length@1.2.2:
        dependencies:
            define-data-property: 1.1.4
            es-errors: 1.3.0
            function-bind: 1.1.2
            get-intrinsic: 1.2.7
            gopd: 1.2.0
            has-property-descriptors: 1.0.2

    set-function-name@2.0.2:
        dependencies:
            define-data-property: 1.1.4
            es-errors: 1.3.0
            functions-have-names: 1.2.3
            has-property-descriptors: 1.0.2

    set-proto@1.0.0:
        dependencies:
            dunder-proto: 1.0.1
            es-errors: 1.3.0
            es-object-atoms: 1.1.1

    sharp@0.33.5:
        dependencies:
            color: 4.2.3
            detect-libc: 2.0.3
            semver: 7.6.3
        optionalDependencies:
            '@img/sharp-darwin-arm64': 0.33.5
            '@img/sharp-darwin-x64': 0.33.5
            '@img/sharp-libvips-darwin-arm64': 1.0.4
            '@img/sharp-libvips-darwin-x64': 1.0.4
            '@img/sharp-libvips-linux-arm': 1.0.5
            '@img/sharp-libvips-linux-arm64': 1.0.4
            '@img/sharp-libvips-linux-s390x': 1.0.4
            '@img/sharp-libvips-linux-x64': 1.0.4
            '@img/sharp-libvips-linuxmusl-arm64': 1.0.4
            '@img/sharp-libvips-linuxmusl-x64': 1.0.4
            '@img/sharp-linux-arm': 0.33.5
            '@img/sharp-linux-arm64': 0.33.5
            '@img/sharp-linux-s390x': 0.33.5
            '@img/sharp-linux-x64': 0.33.5
            '@img/sharp-linuxmusl-arm64': 0.33.5
            '@img/sharp-linuxmusl-x64': 0.33.5
            '@img/sharp-wasm32': 0.33.5
            '@img/sharp-win32-ia32': 0.33.5
            '@img/sharp-win32-x64': 0.33.5

    shebang-command@2.0.0:
        dependencies:
            shebang-regex: 3.0.0

    shebang-regex@3.0.0: {}

    side-channel-list@1.0.0:
        dependencies:
            es-errors: 1.3.0
            object-inspect: 1.13.3

    side-channel-map@1.0.1:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            get-intrinsic: 1.2.7
            object-inspect: 1.13.3

    side-channel-weakmap@1.0.2:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            get-intrinsic: 1.2.7
            object-inspect: 1.13.3
            side-channel-map: 1.0.1

    side-channel@1.1.0:
        dependencies:
            es-errors: 1.3.0
            object-inspect: 1.13.3
            side-channel-list: 1.0.0
            side-channel-map: 1.0.1
            side-channel-weakmap: 1.0.2

    signal-exit@4.1.0: {}

    simple-swizzle@0.2.2:
        dependencies:
            is-arrayish: 0.3.2

    slice-ansi@5.0.0:
        dependencies:
            ansi-styles: 6.2.1
            is-fullwidth-code-point: 4.0.0

    slice-ansi@7.1.0:
        dependencies:
            ansi-styles: 6.2.1
            is-fullwidth-code-point: 5.0.0

    source-map-js@1.2.1: {}

    stable-hash@0.0.4: {}

    streamsearch@1.1.0: {}

    string-argv@0.3.2: {}

    string-width@4.2.3:
        dependencies:
            emoji-regex: 8.0.0
            is-fullwidth-code-point: 3.0.0
            strip-ansi: 6.0.1

    string-width@5.1.2:
        dependencies:
            eastasianwidth: 0.2.0
            emoji-regex: 9.2.2
            strip-ansi: 7.1.0

    string-width@7.2.0:
        dependencies:
            emoji-regex: 10.4.0
            get-east-asian-width: 1.3.0
            strip-ansi: 7.1.0

    string.prototype.includes@2.0.1:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9

    string.prototype.matchall@4.0.12:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            es-object-atoms: 1.1.1
            get-intrinsic: 1.2.7
            gopd: 1.2.0
            has-symbols: 1.1.0
            internal-slot: 1.1.0
            regexp.prototype.flags: 1.5.4
            set-function-name: 2.0.2
            side-channel: 1.1.0

    string.prototype.repeat@1.0.0:
        dependencies:
            define-properties: 1.2.1
            es-abstract: 1.23.9

    string.prototype.trim@1.2.10:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-data-property: 1.1.4
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-object-atoms: 1.1.1
            has-property-descriptors: 1.0.2

    string.prototype.trimend@1.0.9:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-properties: 1.2.1
            es-object-atoms: 1.1.1

    string.prototype.trimstart@1.0.8:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-object-atoms: 1.1.1

    strip-ansi@6.0.1:
        dependencies:
            ansi-regex: 5.0.1

    strip-ansi@7.1.0:
        dependencies:
            ansi-regex: 6.1.0

    strip-bom@3.0.0: {}

    strip-final-newline@3.0.0: {}

    strip-json-comments@3.1.1: {}

    styled-jsx@5.1.6(react@19.0.0):
        dependencies:
            client-only: 0.0.1
            react: 19.0.0

    sucrase@3.35.0:
        dependencies:
            '@jridgewell/gen-mapping': 0.3.8
            commander: 4.1.1
            glob: 10.4.5
            lines-and-columns: 1.2.4
            mz: 2.7.0
            pirates: 4.0.6
            ts-interface-checker: 0.1.13

    supports-color@7.2.0:
        dependencies:
            has-flag: 4.0.0

    supports-preserve-symlinks-flag@1.0.0: {}

    swiper@11.2.2: {}

    tabbable@6.2.0: {}

    tailwindcss@3.4.17:
        dependencies:
            '@alloc/quick-lru': 5.2.0
            arg: 5.0.2
            chokidar: 3.6.0
            didyoumean: 1.2.2
            dlv: 1.1.3
            fast-glob: 3.3.3
            glob-parent: 6.0.2
            is-glob: 4.0.3
            jiti: 1.21.7
            lilconfig: 3.1.3
            micromatch: 4.0.8
            normalize-path: 3.0.0
            object-hash: 3.0.0
            picocolors: 1.1.1
            postcss: 8.5.1
            postcss-import: 15.1.0(postcss@8.5.1)
            postcss-js: 4.0.1(postcss@8.5.1)
            postcss-load-config: 4.0.2(postcss@8.5.1)
            postcss-nested: 6.2.0(postcss@8.5.1)
            postcss-selector-parser: 6.1.2
            resolve: 1.22.10
            sucrase: 3.35.0
        transitivePeerDependencies:
            - ts-node

    tapable@2.2.1: {}

    thenify-all@1.6.0:
        dependencies:
            thenify: 3.3.1

    thenify@3.3.1:
        dependencies:
            any-promise: 1.3.0

    to-regex-range@5.0.1:
        dependencies:
            is-number: 7.0.0

    ts-api-utils@2.0.0(typescript@5.7.3):
        dependencies:
            typescript: 5.7.3

    ts-interface-checker@0.1.13: {}

    tsconfig-paths@3.15.0:
        dependencies:
            '@types/json5': 0.0.29
            json5: 1.0.2
            minimist: 1.2.8
            strip-bom: 3.0.0

    tslib@2.8.1: {}

    type-check@0.4.0:
        dependencies:
            prelude-ls: 1.2.1

    type-fest@4.35.0: {}

    typed-array-buffer@1.0.3:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            is-typed-array: 1.1.15

    typed-array-byte-length@1.0.3:
        dependencies:
            call-bind: 1.0.8
            for-each: 0.3.4
            gopd: 1.2.0
            has-proto: 1.2.0
            is-typed-array: 1.1.15

    typed-array-byte-offset@1.0.4:
        dependencies:
            available-typed-arrays: 1.0.7
            call-bind: 1.0.8
            for-each: 0.3.4
            gopd: 1.2.0
            has-proto: 1.2.0
            is-typed-array: 1.1.15
            reflect.getprototypeof: 1.0.10

    typed-array-length@1.0.7:
        dependencies:
            call-bind: 1.0.8
            for-each: 0.3.4
            gopd: 1.2.0
            is-typed-array: 1.1.15
            possible-typed-array-names: 1.0.0
            reflect.getprototypeof: 1.0.10

    typescript@5.7.3: {}

    unbox-primitive@1.1.0:
        dependencies:
            call-bound: 1.0.3
            has-bigints: 1.1.0
            has-symbols: 1.1.0
            which-boxed-primitive: 1.1.1

    undici-types@6.19.8: {}

    uri-js@4.4.1:
        dependencies:
            punycode: 2.3.1

    use-callback-ref@1.3.3(@types/react@19.0.8)(react@19.0.0):
        dependencies:
            react: 19.0.0
            tslib: 2.8.1
        optionalDependencies:
            '@types/react': 19.0.8

    use-composed-ref@1.4.0(@types/react@19.0.8)(react@19.0.0):
        dependencies:
            react: 19.0.0
        optionalDependencies:
            '@types/react': 19.0.8

    use-debounce@10.0.4(react@19.0.0):
        dependencies:
            react: 19.0.0

    use-isomorphic-layout-effect@1.2.0(@types/react@19.0.8)(react@19.0.0):
        dependencies:
            react: 19.0.0
        optionalDependencies:
            '@types/react': 19.0.8

    use-latest@1.3.0(@types/react@19.0.8)(react@19.0.0):
        dependencies:
            react: 19.0.0
            use-isomorphic-layout-effect: 1.2.0(@types/react@19.0.8)(react@19.0.0)
        optionalDependencies:
            '@types/react': 19.0.8

    use-sidecar@1.1.3(@types/react@19.0.8)(react@19.0.0):
        dependencies:
            detect-node-es: 1.1.0
            react: 19.0.0
            tslib: 2.8.1
        optionalDependencies:
            '@types/react': 19.0.8

    util-deprecate@1.0.2: {}

    which-boxed-primitive@1.1.1:
        dependencies:
            is-bigint: 1.1.0
            is-boolean-object: 1.2.1
            is-number-object: 1.1.1
            is-string: 1.1.1
            is-symbol: 1.1.1

    which-builtin-type@1.2.1:
        dependencies:
            call-bound: 1.0.3
            function.prototype.name: 1.1.8
            has-tostringtag: 1.0.2
            is-async-function: 2.1.1
            is-date-object: 1.1.0
            is-finalizationregistry: 1.1.1
            is-generator-function: 1.1.0
            is-regex: 1.2.1
            is-weakref: 1.1.0
            isarray: 2.0.5
            which-boxed-primitive: 1.1.1
            which-collection: 1.0.2
            which-typed-array: 1.1.18

    which-collection@1.0.2:
        dependencies:
            is-map: 2.0.3
            is-set: 2.0.3
            is-weakmap: 2.0.2
            is-weakset: 2.0.4

    which-typed-array@1.1.18:
        dependencies:
            available-typed-arrays: 1.0.7
            call-bind: 1.0.8
            call-bound: 1.0.3
            for-each: 0.3.4
            gopd: 1.2.0
            has-tostringtag: 1.0.2

    which@2.0.2:
        dependencies:
            isexe: 2.0.0

    word-wrap@1.2.5: {}

    wrap-ansi@7.0.0:
        dependencies:
            ansi-styles: 4.3.0
            string-width: 4.2.3
            strip-ansi: 6.0.1

    wrap-ansi@8.1.0:
        dependencies:
            ansi-styles: 6.2.1
            string-width: 5.1.2
            strip-ansi: 7.1.0

    wrap-ansi@9.0.0:
        dependencies:
            ansi-styles: 6.2.1
            string-width: 7.2.0
            strip-ansi: 7.1.0

    yaml@2.7.0: {}

    yocto-queue@0.1.0: {}
