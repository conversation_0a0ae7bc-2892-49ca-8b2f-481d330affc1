import { AxiosError } from 'axios'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'

export class AuthenticationError extends Error {
    constructor(message: string) {
        super(message)
        this.name = 'AuthenticationError'
    }
}

export const handleError = (
    error: unknown,
    router: AppRouterInstance | null = null
) => {
    if (error instanceof AuthenticationError) {
        if (router) {
            router.push('/signin')
        }
        return {
            message: error.message,
            errors: null,
        }
    }

    if (error instanceof AxiosError) {
        if (error?.response?.data) {
            return {
                message: error.response.data.message as string,
                errors: error.response.data.errors || null,
            }
        }
        return {
            message: error?.message || 'An error occurred. Please try again.',
            errors: null,
        }
    }

    if (error instanceof Error) {
        return {
            message: error.message,
            errors: null,
        }
    }

    return {
        message: 'An error occurred',
        errors: null,
    }
}
