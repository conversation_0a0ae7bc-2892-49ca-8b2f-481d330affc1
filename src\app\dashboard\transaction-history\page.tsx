import DashboardPagesShell from '@/components/dashboard/DashboardPagesShell'
import TableFallback from '@/components/dashboard/fallbackUI/TableFallback'
import SearchAndFilter from '@/components/dashboard/SearchAndFilter'
import TableSection from '@/components/dashboard/transactionHistory/TableSection'
import { Suspense } from 'react'

export default async function TransactionHistory(props: {
    searchParams?: Promise<Param>
}) {
    const params = await props.searchParams
    return (
        <DashboardPagesShell
            innerComponentContent="Monitor your airtime, data, electricity, and cable payments"
            innerComponentHeading="Transaction History"
        >
            <div className="grow flex flex-col items-center">
                <SearchAndFilter />
                <Suspense
                    key={JSON.stringify(params)}
                    fallback={<TableFallback />}
                >
                    <TableSection params={params} />
                </Suspense>
            </div>
        </DashboardPagesShell>
    )
}
