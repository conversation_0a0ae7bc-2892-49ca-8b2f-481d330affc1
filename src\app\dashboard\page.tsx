import AccountAndBalances from '@/components/dashboard/overview/AccountAndBalances'
import { Greetings } from '@/components/dashboard/Greetings'
import NetworkCodes from '@/components/dashboard/overview/NetworkCodes'
import QuickActions from '@/components/dashboard/overview/QuickActions'
import RecentTransactions from '@/components/dashboard/overview/RecentTransactions'

export default async function Dashboard(props: {
    searchParams?: Promise<Param>
}) {
    const params = await props.searchParams
    return (
        <>
            <Greetings />

            <div className="flex flex-col gap-6 max-w-5xl px-4">
                <AccountAndBalances />
                <QuickActions />
                <div className="flex gap-6 flex-col md:flex-row max-w-full ">
                    <div className="basis-1/2 max-w-full">
                        <RecentTransactions params={params} />
                    </div>
                    <div className="flex flex-col gap-6 basis-1/2">
                        <NetworkCodes
                            heading="Know Your Data at a Glance"
                            content="Quickly check your data balance for any network"
                            table={[
                                { network: 'MTN', code: '*323#' },
                                { network: 'Airtel', code: '*323#' },
                                { network: 'Glo', code: '*127*0#' },
                                { network: '9mobile', code: '*228#' },
                            ]}
                        />
                        <NetworkCodes
                            heading="Stay on Top of Your Airtime"
                            content="Quickly check your data balance for any network"
                            table={[
                                { network: 'MTN', code: '*323#' },
                                { network: 'Airtel', code: '*323#' },
                                { network: 'Glo', code: '*127*0#' },
                                { network: '9mobile', code: '*228#' },
                            ]}
                        />
                    </div>
                </div>
            </div>
        </>
    )
}
