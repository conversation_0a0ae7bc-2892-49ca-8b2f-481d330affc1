import Step from './Step'
import Image from 'next/image'
import Link from 'next/link'

export default function ThreeEasy() {
    return (
        <div className="bg-[#F9F9F9]">
            <div className="px-4 py-[4.125rem] max-w-6xl mx-auto gap-11 md:gap-[2.875rem]  pb-[4.125rem] md:pb-[6.25rem] flex flex-col items-center">
                <div
                    className="flex flex-col items-center gap-4 text-center md:gap-3"
                    data-aos="fade-up"
                >
                    <h2
                        className={`font-brico font-semibold text-[1.75rem] leading-[2.0625rem] md:text-[2.75rem] md:leading-[3.25rem] md:text-[#101828] max-w-xl`}
                    >
                        Three Easy Steps to Seamless Payments{' '}
                    </h2>
                    <p className="leading-[1.3125rem] text-[#98A2B3] md:text-[1.5rem] md:leading-[2rem] md:text-[#475467]">
                        That’s it! No delays, no hassles—just simple, reliable
                        transactions.{' '}
                    </p>
                </div>
                {/*  */}
                <div className="flex flex-col items-center justify-between gap-8 md:flex-row md:items-start">
                    <Step
                        color="#815CEB"
                        img="/icons/cloud.svg"
                        content={`Go to your app store, search for "CIP," and download the app, or simply visit our website to get started.`}
                    >
                        <span>
                            Download the App or <br />
                            Visit Our Website
                        </span>
                    </Step>
                    {/*  */}
                    <Image
                        alt=""
                        width={210}
                        height={11}
                        src="/img/joinLine1.svg"
                        className="md:-rotate-90 md:-translate-y-1/4 h-[10rem] w-[1.375rem] relative"
                    />
                    <Step
                        color="#0C111D"
                        img="/icons/user-add.svg"
                        content={`Open the app or website, and complete the quick and easy sign-up process using your basic personal details.`}
                    >
                        <span>
                            Register for Free on <br /> CIP
                        </span>
                    </Step>{' '}
                    {/*  */}
                    <Image
                        alt=""
                        width={210}
                        height={11}
                        src="/img/joinLine2.svg"
                        className="md:-rotate-90 md:-translate-y-1/4 h-[10rem] w-[1.375rem] relative"
                    />
                    <Step
                        color="#00AA30"
                        img="/icons/money-add.svg"
                        content={`After signing in, you can add funds to your account and start
                  paying your bills effortlessly on either the app or the
                  website!`}
                    >
                        <span>
                            Add Funds and Pay
                            <br /> Your Bills
                        </span>
                    </Step>{' '}
                </div>
                {/*  */}
                <Link
                    href="/signup"
                    className={`font-vietnam rounded-[6.1875rem] md:rounded-[5rem] py-3.5 px-6 md:px-[3.75rem] md:py-5 bg-primary text-white`}
                >
                    Start exploring now
                </Link>
            </div>
        </div>
    )
}
