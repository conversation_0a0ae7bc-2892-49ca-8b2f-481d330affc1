export default function NetworkCodes({
    heading,
    content,
    table,
}: {
    heading: string
    content: string
    table: { network: string; code: string }[]
}) {
    return (
        <div className="px-4 py-6 md:p-[26px] flex flex-col gap-6 bg-white rounded-xl">
            <div className="gap-1 flex flex-col">
                <h3 className="text-[18px] font-medium text-[#182230]">
                    {heading}
                </h3>
                <p className="text-[#667085] text-sm">{content} </p>
                <div className="overflow-x-auto text-[#182230] text-sm pt-8 md:pt-[44px] border-t border-[F5F5F5] text-left">
                    <table className="table-auto w-full border-separate">
                        <thead>
                            <tr className="border-b border-[#EAECF0] flex rounded-t-lg bg-cipLight text-[#182230]">
                                <th className="w-full p-4 min-w-24">Network</th>
                                <th className="w-full p-4 min-w-24">
                                    USSD Code
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {table.map((network) => (
                                <tr
                                    key={network.network}
                                    className="border-b border-[#EAECF0] flex"
                                >
                                    <td className="w-full p-4 min-w-24">
                                        {network.network}
                                    </td>
                                    <td className="w-full p-4 min-w-24">
                                        {network.code}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    )
}
