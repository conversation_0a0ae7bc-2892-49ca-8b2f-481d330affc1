'use client'
import { useForm } from 'react-hook-form'
import React, { useEffect, useState } from 'react'
import Input from '@/components/inputs/Input'
import SelectInput from '@/components/inputs/SelectInput'
import InsertInput from '@/components/inputs/InsertInput'
import { enqueueSnackbar as notify } from 'notistack'
import apiRequest from '@/lib/auth/client/request'
import { handleError } from '@/lib/error'
import TransferCard from '@/components/anon/TransferCard'
import { Modal } from '@mantine/core'
import { CheckCircle } from 'lucide-react'
import { useDisclosure } from '@mantine/hooks'

interface FormData {
    biller: string
    paymentType: string
    meterNumber: string
    phone: string
    amount: string
    username?: string
    email: string
}

const options = [
    {
        id: 'ikeja-electric',
        name: 'IKEDC',
    },
    {
        id: 'eko-electric',
        name: 'EKEDC',
    },
    {
        id: 'portharcourt-electric',
        name: 'PHED',
    },
    {
        id: 'kaduna-electric',
        name: 'Ka<PERSON><PERSON>',
    },
    {
        id: 'jos-electric',
        name: 'J<PERSON>',
    },
    {
        id: 'ibadan-electric',
        name: 'IBEDC',
    },
    {
        id: 'kano-electric',
        name: 'KEDCO',
    },
    {
        id: 'enugu-electric',
        name: 'EEDC',
    },
    {
        id: 'abuja-electric',
        name: 'AEDC',
    },
    {
        id: 'benin-electric',
        name: 'BEDC',
    },
    {
        id: 'aba-electric',
        name: 'ABA',
    },
    {
        id: 'yola-electric',
        name: 'YEDC',
    },
]

export default function ActionForm() {
    const [isLoading, setIsLoading] = useState(false)
    const [minAmount, setMinAmount] = useState(500)
    const [username, setUsername] = useState('')
    const {
        register,
        handleSubmit,
        getValues,
        setValue,
        reset,
        watch,
        setError,
        formState: { errors, isSubmitting },
        trigger,
    } = useForm<FormData>()
    const [opened, { open, close }] = useDisclosure(false)
    const meterNumber = watch('meterNumber')
    const meterType = watch('paymentType')
    const biller = watch('biller')
    const [paymentData, setPaymentData] = useState<{
        walletData: WalletAccountData
        amountToPay: string
    } | null>(null)

    const onSubmit = async (data: FormData) => {
        try {
            const form_data = {
                meter_number: data.meterNumber,
                provider_id: data.biller,
                meter_type: data.paymentType,
                phone: data.phone,
                amount: parseInt(data.amount),
                email_address: data.email,
            }

            const response = await apiRequest(false).post(
                '/api/anon/electricity',
                form_data
            )
            const { data: responseData } = response.data
            const { amount: amountToPay, ...otherData } = responseData
            setPaymentData({
                walletData: otherData,
                amountToPay,
            })
        } catch (err: unknown) {
            const error: ApiWithFormError<FormData> = handleError(err)
            if (error.errors) {
                error.errors.forEach((e) => {
                    setError(e.path, {
                        message: e.message,
                    })
                })
            }
            if (error.message && !error.errors) {
                notify(error.message)
            }
        }
    }

    //validate meter
    const validateMeter = async () => {
        setUsername('')
        const form = {
            meter_number: meterNumber,
            provider_id: biller,
            meter_type: meterType,
        }
        const isValid = await trigger(['meterNumber', 'biller', 'paymentType'])
        if (!isValid) trigger(['meterNumber', 'biller', 'paymentType'])

        if (isValid) {
            try {
                setIsLoading(true)
                const response = await apiRequest(false).post(
                    `/api/electricity/validate`,
                    form
                )
                const { data } = response.data
                setUsername(data.name)
                setMinAmount(
                    Number(data.min_purchase) > 500
                        ? Number(data.min_purchase)
                        : 500
                )
            } catch (err: unknown) {
                const error: ApiWithFormError<FormData> = handleError(err)
                if (error.errors) {
                    error.errors.forEach((e) => {
                        setError(e.path, {
                            message: e.message,
                        })
                    })
                }
                if (error.message && !error.errors) {
                    notify(error.message, { variant: 'error' })
                }
            } finally {
                setIsLoading(false)
            }
        }
    }

    const resetter = (failed: boolean) => {
        setPaymentData(null)
        if (!failed) {
            reset()
            open()
        }
    }

    useEffect(() => {
        setUsername('')
    }, [meterNumber, setUsername, setValue])

    return (
        <>
            {paymentData?.walletData.reference ? (
                <TransferCard
                    amountToPay={paymentData.amountToPay}
                    walletData={paymentData.walletData}
                    resetter={resetter}
                />
            ) : (
                <>
                    <Modal
                        opened={opened}
                        onClose={close}
                        withCloseButton={false}
                        centered
                    >
                        <div className="w-full max-w-md bg-white overflow-hidden p-6">
                            <div className="flex flex-col items-center text-center">
                                <div className="mb-4 bg-green-100 p-3 rounded-full">
                                    <CheckCircle className="h-12 w-12 text-green-600" />
                                </div>

                                <h1 className="text-2xl font-bold text-gray-800 mb-2">
                                    Payment Successful!
                                </h1>
                                <p className="text-gray-600 mb-6">
                                    Thank you for your payment. <br />
                                    Your transaction is being processed. <br />
                                    We will notify you via email on the status
                                    of your transaction (usually takes about a
                                    minute).
                                </p>
                            </div>
                        </div>
                    </Modal>

                    <form
                        method="POST"
                        onSubmit={handleSubmit(onSubmit)}
                        className="flex flex-col gap-[2.75rem] max-w-md mx-auto md:mx-0"
                    >
                        <div className="flex flex-col gap-5 md:gap-7">
                            <SelectInput<FormData>
                                label={'Biller Name'}
                                inputOptions={options.map((option) => ({
                                    option: option.name,
                                    value: option.id,
                                }))}
                                registerName={'biller'}
                                register={register}
                                errors={errors}
                                validation={{
                                    required: {
                                        value: true,
                                        message: `Biller name is required`,
                                    },
                                }}
                            />
                            <SelectInput<FormData>
                                label={'Choose Payment Type'}
                                inputOptions={[
                                    { option: 'Prepaid', value: 'prepaid' },
                                    { option: 'Postpaid', value: 'postpaid' },
                                ]}
                                registerName={'paymentType'}
                                register={register}
                                errors={errors}
                                validation={{
                                    required: {
                                        value: true,
                                        message: `Payment type is required`,
                                    },
                                }}
                            />
                            <Input<FormData>
                                type={'number'}
                                label={'Meter Number'}
                                registerName={'meterNumber'}
                                placeholder={'Enter meter number'}
                                defaultValue=""
                                register={register}
                                errors={errors}
                                validation={{
                                    required: {
                                        value: true,
                                        message: `Meter number is required`,
                                    },
                                    minLength: {
                                        value: 8,
                                        message: 'Input a valid meter number',
                                    },
                                }}
                            />

                            {username && (
                                <>
                                    <Input<FormData>
                                        type={'text'}
                                        label={'Name on Meter'}
                                        registerName={'username'}
                                        placeholder={'username'}
                                        isDisabled={true}
                                        defaultValue={username}
                                        register={register}
                                        errors={errors}
                                        validation={{
                                            required: {
                                                value: false,
                                                message: `Username is required`,
                                            },
                                        }}
                                    />
                                    <Input<FormData>
                                        type={'tel'}
                                        label={'Phone Number'}
                                        registerName={'phone'}
                                        placeholder={'Enter your phone number'}
                                        register={register}
                                        errors={errors}
                                        validation={{
                                            required: {
                                                value: true,
                                                message: `Phone number is required`,
                                            },
                                            pattern: {
                                                value: /^(?:\+?234|0)[789][01]\d{8}$/,
                                                message:
                                                    'Please enter a valid Nigerian phone number',
                                            },
                                        }}
                                    />
                                    <InsertInput<FormData>
                                        label={'Amount'}
                                        inputOptions={[
                                            { option: '₦500', value: 500 },
                                            { option: '₦1000', value: 1000 },
                                            { option: '₦2000', value: 2000 },
                                            { option: '₦5000', value: 5000 },
                                            { option: '₦10000', value: 10000 },
                                        ]}
                                        placeholder="Enter amount"
                                        getValues={getValues}
                                        watch={watch}
                                        setValue={setValue}
                                        registerName={'amount'}
                                        register={register}
                                        errors={errors}
                                        trigger={trigger}
                                        validation={{
                                            required: {
                                                value: true,
                                                message: `Amount is required`,
                                            },
                                            min: {
                                                value: minAmount,
                                                message: `You can't purchase electricity less than ₦${minAmount} for this meter`,
                                            },
                                        }}
                                    />

                                    <Input<FormData>
                                        type={'email'}
                                        label={'Email Address'}
                                        register={register}
                                        registerName={'email'}
                                        placeholder={
                                            'Token will be sent to this email'
                                        }
                                        errors={errors}
                                        validation={{
                                            required: {
                                                value: true,
                                                message: `Email address is required`,
                                            },
                                            pattern: {
                                                value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                                                message:
                                                    'Please enter a valid email address',
                                            },
                                        }}
                                    />
                                </>
                            )}
                        </div>

                        <div className="flex flex-col gap-4">
                            {username ? (
                                <button
                                    disabled={username ? false : true}
                                    type="submit"
                                    className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] ${!username && 'opacity-20 hover:opacity-20'}`}
                                >
                                    {isSubmitting ? (
                                        <span>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                className="lucide lucide-loader-circle animate-spin"
                                            >
                                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                            </svg>
                                        </span>
                                    ) : (
                                        'Buy now'
                                    )}
                                </button>
                            ) : (
                                <button
                                    type="button"
                                    onClick={validateMeter}
                                    disabled={
                                        meterNumber?.length < 8 || isLoading
                                    }
                                    className="font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] disabled:opacity-60 hover:disabled:opacity-60 disabled:cursor-not-allowed"
                                >
                                    {isLoading ? (
                                        <span>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                className="lucide lucide-loader-circle animate-spin"
                                            >
                                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                            </svg>
                                        </span>
                                    ) : (
                                        'Validate Meter Number'
                                    )}
                                </button>
                            )}
                        </div>
                    </form>
                </>
            )}
        </>
    )
}
