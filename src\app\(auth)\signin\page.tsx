import Link from 'next/link'
import SignInForm from '@/components/auth/SignInForm'

export default function SignUp() {
    return (
        <div className="flex md:max-w-[65%] lg:max-w-[75%] mx-auto gap-8 flex-col">
            <div className="flex flex-col gap-[.5rem]">
                <h2 className="font-brico font-semibold text-[1.5rem] leading-[1.75rem] md:font-bold md:text-[1.875rem] md:leading-[2.25rem] text-[#101828]">
                    {`Welcome back, we've missed you!`}
                </h2>
                <p className="leading-[1.3125rem] text-[#475467]">
                    Sign in now to access all your services in one place
                </p>
            </div>

            {/* Form goes here */}
            <SignInForm />

            <p className="text-[#475467] leading-[1.3125rem]">
                {`Don't have an account? `}
                <Link href="/signup" className="text-primary">
                    Sign up
                </Link>
            </p>
        </div>
    )
}
