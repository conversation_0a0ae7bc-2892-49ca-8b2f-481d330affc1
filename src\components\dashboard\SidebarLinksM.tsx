import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { SetStateAction } from 'react'

// For mobile
export default function SidebarLinksM({
    heading,
    href,
    icons,
    altIcons,
    setShowSideBar,
}: {
    heading: string
    href: string
    icons: string
    altIcons: string
    setShowSideBar: React.Dispatch<SetStateAction<boolean>>
}) {
    const path = usePathname()
    return (
        <Link
            key={heading}
            onClick={() => setShowSideBar(false)}
            href={href}
            className={`${path === href && 'bg-primary text-white'} group flex items-center py-2 px-3 gap-2 rounded-[.375rem] ${path !== href && 'hover:bg-blue-50'} text-[#475467]`}
        >
            <div className="w-6">
                <Image
                    src={path === href ? altIcons : icons}
                    alt={icons}
                    width={12}
                    height={12}
                    className="w-full h-auto"
                />
            </div>
            <span>{heading}</span>
        </Link>
    )
}
