import ResendEmailButton from '@/components/auth/ResendEmail'

export default function VerifyEmail() {
    return (
        <div className="flex md:max-w-[86%] mx-auto gap-8 flex-col justify-center items-center h-full">
            <div className="flex flex-col gap-[1.2rem] text-center font-general">
                <h2 className="font-brico text-[1.875rem] font-bold">
                    Verification Failed
                </h2>
                <p className="text-[#182230] text-[1.25rem]">
                    The verification link is invalid or expired. Please check
                    your inbox (and spam folder) for the latest email.
                </p>
                <p className="text-[#475467] mt-[48px] w-[90%] mx-auto">
                    If you haven&apos;t received the email, click the button
                    below to resend it
                </p>
                <ResendEmailButton />
            </div>
        </div>
    )
}
