'use client'

import {
    RegisterOptions,
    UseFormRegister,
    FieldValues,
    Path,
    FieldErrors,
    UseFormSetValue,
    UseFormGetValues,
    PathValue,
    UseFormWatch,
} from 'react-hook-form'

type InputProps<T extends FieldValues> = {
    inputOptions: { option: string; value: number }[]
    label?: string
    placeholder: string
    registerName: Path<T>
    register: UseFormRegister<T>
    setValue: UseFormSetValue<T>
    getValues: UseFormGetValues<T>
    watch: UseFormWatch<T> // Add watch to make it controlled
    validation?: RegisterOptions<T, Path<T>>
    errors?: FieldErrors<T>
    trigger: (fields?: Path<T>[]) => Promise<boolean>
}

export default function InsertInput<T extends FieldValues>({
    label,
    inputOptions,
    placeholder,
    registerName,
    register,
    setValue,
    getValues,
    watch, // Receive watch
    validation = {},
    errors,
    trigger,
}: InputProps<T>) {
    // Watch the input value to make it controlled
    const value = watch(registerName)

    return (
        <div className="gap-[.375rem] flex flex-col">
            {label && (
                <label
                    htmlFor={`input${registerName}`}
                    className="font-medium text-sm leading-[1.1875rem]"
                >
                    {label}
                </label>
            )}
            <input
                type="number"
                id={`input${registerName}`}
                {...register(registerName, {
                    ...validation,
                    onChange: async (e) => {
                        // Convert to number on change
                        const numValue =
                            e.target.value === ''
                                ? 0
                                : parseFloat(e.target.value)
                        if (numValue < 0) {
                            setValue(registerName, 0 as PathValue<T, Path<T>>)
                            return
                        }
                        setValue(
                            registerName,
                            numValue as PathValue<T, Path<T>>
                        )
                        //trigger validation
                        await trigger([registerName]) // Ensure to import and use trigger from useForm
                    },
                })}
                value={value ?? ''} // Make it controlled
                placeholder={placeholder}
                onWheel={(e) => e.currentTarget.blur()} // Prevent scroll on input
                className="w-full border border-[#D0D5DD] px-4 py-[.875rem] rounded-lg placeholder:text-[#667085] outline-none appearance-none outline-0 ring-0 "
            />
            {errors?.[registerName] && (
                <span className="text-sm text-[#475467]">
                    {errors[registerName]?.message as string}
                </span>
            )}
            <div className="flex items-center flex-wrap gap-5">
                {inputOptions.map((option, index) => (
                    <button
                        onClick={(e) => {
                            e.preventDefault()
                            const currentValue =
                                Number(getValues(registerName)) || 0
                            setValue(
                                registerName,
                                (currentValue + option.value) as PathValue<
                                    T,
                                    Path<T>
                                >
                            )
                        }}
                        key={`${option.value}${index}`}
                        className="py-1 px-2 rounded-md bg-cipLight text-primary"
                    >
                        {option.option}
                    </button>
                ))}
            </div>
        </div>
    )
}
