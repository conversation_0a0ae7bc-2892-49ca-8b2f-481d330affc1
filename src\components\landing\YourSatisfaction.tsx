'use client'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Pagination, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import Image from 'next/image'

const Testimonial = ({
    content,
    img,
    username,
    state,
}: {
    content: string
    img: string
    username: string
    state: string
}) => {
    return (
        <div className="flex flex-col gap-7 justify-between py-5">
            <Image
                alt="star"
                width={116}
                height={21}
                src="/img/Stars.svg"
                className="w-[7.25rem] h-auto max-w-full"
            />

            <h2 className="text-[#101828]  text-[1.25rem] leading-[1.6875rem] font-medium  md:text-[2.5rem] md:leading-[3.3rem]">
                {content}
            </h2>

            <div className="flex flex-row gap-4">
                <div className="w-[3.75rem] h-[3.75rem] rounded-full flex items-center justify-center">
                    <Image
                        alt=""
                        src={img}
                        width={56}
                        height={56}
                        className="w-[4.0625rem] h-[4.0625rem]"
                    />
                </div>
                <div className="flex flex-col justify-between gap-[.125rem]">
                    <p
                        className={`font-vietnam font-semibold leading-[21px] md:text-[1.125rem] md:leading-[1.4375rem] text-[#101828]`}
                    >
                        {username}
                    </p>
                    <p className={`font-vietnam leading-[1.2813rem]`}>
                        {state}
                    </p>
                </div>
            </div>
        </div>
    )
}

export default function YourSatisfaction() {
    const testimonials = [
        {
            content:
                'Fast, easy, and reliable! I can recharge my phone and pay my bills in seconds',
            username: 'Adebayo O.',
            state: 'Lagos State',
            img: '/img/testUser.svg',
        },
        {
            content:
                'Fast, easy, and reliable! I can recharge my phone and pay my bills in seconds',
            username: 'Adebayo O.1',
            state: 'Ogun State',
            img: '/img/testUser.svg',
        },
        {
            content:
                'Fast, easy, and reliable! I can recharge my phone and pay my bills in seconds',
            username: 'Adebayo O.2',
            state: 'Ekiti State',
            img: '/img/testUser.svg',
        },
        {
            content:
                'Fast, easy, and reliable! I can recharge my phone and pay my bills in seconds',
            username: 'Adebayo O.3',
            state: 'Kano State',
            img: '/img/testUser.svg',
        },
    ]

    return (
        <div className="px-4 py-[4.125rem] max-w-6xl mx-auto gap-11 md:gap-[2.875rem] flex flex-col items-center">
            <div
                className="flex flex-col items-center max-w-xl gap-4 mx-auto text-center md:gap-3"
                data-aos="fade-up"
            >
                <h2
                    className={`font-brico font-semibold text-[1.75rem] leading-[2.0625rem] md:text-[2.75rem] md:leading-[3.25rem] md:text-[#101828] lg:whitespace-nowrap`}
                >
                    Your Satisfaction Is Our{' '}
                    <span className="relative">
                        <span className="relative z-[1]">Success</span>
                        <Image
                            alt=""
                            src="/img/pow.svg"
                            width={105}
                            height={79}
                            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 z-[-1] h-full w-auto max-h-full"
                        />
                    </span>
                </h2>
                <p className="leading-[1.3125rem] text-[#98A2B3] md:text-[1.5rem] md:leading-[2rem] md:text-[#475467]">
                    {`Real reviews from customers who’ve experienced the ease and speed of our services.`}{' '}
                </p>
            </div>
            {/*  */}
            <div className="flex flex-col md:flex-row justify-between items-center gap-8 md:gap-[2.75rem] overflow-hidden max-w-full">
                <div className="basis-3/5 overflow-hidden max-w-full">
                    <Swiper
                        modules={[Pagination, Autoplay]}
                        spaceBetween={50}
                        slidesPerView={1}
                        pagination={{ clickable: true }}
                        autoplay={{ delay: 2500, disableOnInteraction: false }}
                        className=""
                    >
                        {testimonials.map((testimonial) => (
                            <SwiperSlide key={testimonial.username}>
                                <Testimonial
                                    img={testimonial.img}
                                    content={testimonial.content}
                                    state={testimonial.state}
                                    username={testimonial.username}
                                />
                            </SwiperSlide>
                        ))}
                    </Swiper>
                </div>
                <div className="basis-2/5 hidden md:block" data-aos="fade-up">
                    <Image
                        alt=""
                        src="/img/usersCluster.webp"
                        width={472}
                        height={406}
                        className="w-full h-auto max-w-full"
                    />
                </div>
            </div>
        </div>
    )
}
