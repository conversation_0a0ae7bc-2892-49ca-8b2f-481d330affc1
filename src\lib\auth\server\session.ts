'use server'
import {
    isValidSession,
    SESSION_KEY,
    sessionDecoder,
    sessionEncoder,
} from '@/lib/common'
import { cookies } from 'next/headers'

async function getSession() {
    const session = (await cookies()).get(SESSION_KEY)
    if (!session) return null
    const decoded_session = sessionDecoder(session.value)
    if (!isValidSession(decoded_session)) return null
    return decoded_session
}

async function setSession(sessionData: SessionData) {
    const encoded_session = sessionEncoder(sessionData)
    const { cookies: dynamicCookies } = await import('next/headers')
    const cookiesStore = await dynamicCookies()
    const expires = sessionData.token.refreshExpiresIn
    cookiesStore.set(SESSION_KEY, encoded_session, {
        expires: new Date(expires),
    })
    return encoded_session
}

async function clearSession() {
    const cookiesStore = await cookies()
    cookiesStore.delete(SESSION_KEY)
}

export { getSession, setSession, clearSession }
