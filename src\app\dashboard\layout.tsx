import HeaderSidebar from '@/components/dashboard/HeaderSidebar'
import ScrollToTop from '@/components/dashboard/ScrollToTop'
import FlashNotifications from '@/components/FlashNotifications'
import ToastWrapper from '@/components/ToastWrapper'

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            <HeaderSidebar>{children}</HeaderSidebar>
            <ScrollToTop />
            <ToastWrapper />
            <FlashNotifications />
        </>
    )
}
