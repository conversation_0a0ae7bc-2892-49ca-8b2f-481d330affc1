import Image from 'next/image'
import IndividualFAQ from './IndividualFAQ'

export default function CuriousMinds() {
    return (
        <div className="px-4 py-[4.125rem] max-w-6xl mx-auto gap-11 md:gap-[2.875rem]  pb-[4.125rem] md:pb-[6.25rem] flex flex-col items-center">
            <div
                className="flex flex-col items-center gap-4 mx-auto text-center md:gap-3"
                data-aos="fade-up"
            >
                <h2
                    className={`font-brico font-semibold text-[1.75rem] leading-[2.0625rem] md:text-[2.75rem] md:leading-[3.25rem] md:text-[#101828]`}
                >
                    Curious Minds, Clear{' '}
                    <span className="relative">
                        Answers{' '}
                        <Image
                            src="/img/fancyUnderline.svg"
                            alt=""
                            width={224}
                            height={10}
                            className="absolute left-0 right-0 h-auto top-full"
                        />
                    </span>
                </h2>
                <p className="leading-[1.3125rem] text-[#98A2B3] md:text-[1.5rem] md:leading-[2rem] md:text-[#475467]">
                    Find clarity and confidence with answers to your common
                    questions
                </p>
            </div>
            {/*  */}
            <div className="flex flex-col items-center w-full gap-4">
                {[
                    {
                        heading:
                            'Is Airtime and Data available for all networks?',
                        content:
                            'Yes, CIPTOPUP offers Airtime and Data for all major networks in Nigeria.',
                    },
                    {
                        heading:
                            'Which button should I click to get started - Sign Up or Login?',
                        content:
                            'To get started, click on the "Sign Up" button to create a new account. <br />Once your account is activated, you can use the "Login" button to access your dashboard.',
                    },
                    {
                        heading:
                            'How secure is my transaction history, and can I access it anytime?',
                        content:
                            'Absolutely! Your transaction history is 100% secure and can be accessed anytime, any day through your dashboard.',
                    },
                    {
                        heading: 'How do I pay for Data or Airtime?',
                        content: `You have two flexible options: <br />
                            - Buy Now (No Registration Required): Transfer directly to the instant account number generated for you. <br />
                            - Registered Users: Sign up and fund your wallet using the personalized account number generated upon
                            registration.`,
                    },
                    {
                        heading:
                            'Do I need to activate my account to access the dashboard?',
                        content:
                            'Yes. You must activate your account to gain full access to the dashboard and services.',
                    },
                    {
                        heading:
                            'Can I share data I purchased with friends from my SIM?',
                        content:
                            'Unfortunately, data sharing is currently not supported due to network restrictions. We will update you if this feature becomes available.',
                    },
                    {
                        heading:
                            'Can my data bundle be rolled over or topped up?',
                        content:
                            'Yes! If you purchase a new data bundle before the current one expires, your unused data will roll over automatically.',
                    },
                ].map((faq) => (
                    <IndividualFAQ
                        key={faq.heading}
                        content={faq.content}
                        heading={faq.heading}
                    />
                ))}
            </div>
        </div>
    )
}
