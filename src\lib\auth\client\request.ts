'use client'
import axios from 'axios'
import { getSession, setSession } from './session'
import { BASE_URL } from '@/lib/common'
import { AuthenticationError } from '@/lib/error'

function apiRequest(auth_required: boolean = true) {
    const session = getSession()
    if (auth_required && !session) {
        throw new AuthenticationError('Please login again')
    }
    const headers = {
        'Content-Type': 'application/json',
        Authorization: session?.token
            ? `Bearer ${session.token.accessToken}`
            : '',
    }
    const instance = axios.create({
        baseURL: BASE_URL,
        headers: headers,
    })
    instance.interceptors.response.use(
        (response) => response,
        (error) => {
            if (error?.response?.status === 401) {
                throw new AuthenticationError('Please login again')
            }
            throw error
        }
    )

    // refresh token when it about to expire
    instance.interceptors.request.use(async (config) => {
        try {
            if (session?.token?.accessExpiresIn) {
                const now = new Date().getTime()
                const expires = new Date(
                    session.token.accessExpiresIn
                ).getTime()
                if (expires - now < 1000 * 60 * 5) {
                    const request = await axios.post(
                        `${BASE_URL}/api/auth/refresh`,
                        {
                            refreshToken: session.token.refreshToken,
                        },
                        {
                            headers: {
                                'Content-Type': 'application/json',
                            },
                        }
                    )
                    const { data } = request.data
                    session.token.accessToken = data.token.accessToken
                    session.token.accessExpiresIn = data.token.accessExpiresIn
                    session.token.refreshToken = data.token.refreshToken
                    session.token.refreshExpiresIn = data.token.refreshExpiresIn
                    setSession(session)
                    config.headers.Authorization = `Bearer ${session.token.accessToken}`
                }
            }
        } catch (error) {
            console.log(error)
        }
        return config
    })
    return instance
}

export default apiRequest
