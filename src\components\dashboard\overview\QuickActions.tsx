import Image from 'next/image'
import Link from 'next/link'

export default function QuickActions() {
    return (
        <div className="px-4 py-6 md:p-[26px] flex flex-col gap-6 bg-white rounded-xl">
            <div className="gap-1 flex flex-col">
                <h3 className="text-[18px] font-medium text-[#182230]">
                    Quick Actions
                </h3>
                <p className="text-[#667085] text-sm">
                    Access your favorite services in a flash
                </p>
                <div className="pt-8 md:pt-[44px] border-t border-[F5F5F5] grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 justify-between text-center">
                    {[
                        {
                            icons: '/icons/airtimeTopupQuick.svg',
                            heading: 'Airtime Top-up',
                            href: '/dashboard/airtime-top-up',
                        },
                        {
                            icons: '/icons/airtimeToCashQuick.svg',
                            heading: 'Airtime To Cash',
                            href: '/dashboard',
                        },
                        {
                            icons: '/icons/dataTopupQuick.svg',
                            heading: 'Data Top-up',
                            href: '/dashboard/data-top-up',
                        },
                        {
                            icons: '/icons/electricityBillQuick.svg',
                            heading: 'Electricity Bill',
                            href: '/dashboard/electricity-bill',
                        },
                        {
                            icons: '/icons/cableTvSubscriptionQuick.svg',
                            heading: 'Cable TV Subscription',
                            href: '/dashboard/cable-tv-subscription',
                        },
                        {
                            icons: '/icons/fundWalletQuick.svg',
                            heading: 'Fund Wallet',
                            href: '/dashboard/fund',
                        },
                    ].map((link) => (
                        <Link
                            key={link.heading}
                            href={link.href}
                            className={`flex flex-col items-center py-2 px-3 gap-[.375rem] text-[#0C111D]`}
                        >
                            <div className="p-[.625rem] rounded-full bg-primary">
                                <div className="w-6">
                                    <Image
                                        src={link.icons}
                                        alt={link.icons}
                                        width={12}
                                        height={12}
                                        className="w-full h-auto"
                                    />
                                </div>
                            </div>
                            <span>{link.heading}</span>
                        </Link>
                    ))}
                </div>
                {/* 32 44 */}
            </div>
        </div>
    )
}
