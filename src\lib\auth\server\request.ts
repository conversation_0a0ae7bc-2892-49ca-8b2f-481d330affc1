'use server'
import axios from 'axios'
import { getSession } from './session'
import { BASE_URL } from '@/lib/common'
import { AuthenticationError } from '@/lib/error'

async function apiRequest(auth_required: boolean = true) {
    // enforce server side usage
    if (typeof window !== 'undefined') {
        throw new Error('This function is only available on the server side')
    }

    const session = await getSession()
    if (auth_required && !session) {
        throw new AuthenticationError('Authentication required')
    }
    const headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session.token.accessToken}`,
    }

    const instance = axios.create({
        baseURL: BASE_URL,
        headers: headers,
    })

    instance.interceptors.response.use(
        (response) => response,
        (error) => {
            if (error?.response?.status === 401) {
                throw new AuthenticationError('Authentication required')
            }
            throw error
        }
    )

    return instance
}

export default apiRequest
