// import Link from 'next/link'
import ResetForm from '@/components/auth/ResetForm'
import { notFound } from 'next/navigation'

export default async function Reset(props: {
    searchParams?: Promise<{
        token?: string
    }>
}) {
    const searchParams = await props?.searchParams
    const token = searchParams?.token || ''

    if (!token) notFound()

    return (
        <div className="flex md:max-w-[65%] lg:max-w-[75%] mx-auto gap-8 flex-col">
            <div className="flex flex-col gap-[.5rem]">
                <h2 className="font-brico font-semibold text-[1.5rem] leading-[1.75rem] md:font-bold md:text-[1.875rem] md:leading-[2.25rem] text-[#101828]">
                    {`Reset your password`}
                </h2>
                <p className="leading-[1.3125rem] text-[#475467]">
                    {`Set a new password that you'll use to access your account going forward`}
                </p>
            </div>

            {/* Form goes here */}
            <ResetForm />
        </div>
    )
}
