'use client'
import { useForm } from 'react-hook-form'
import React, { useState } from 'react'
import Input from '@/components/inputs/Input'
import SelectInput from '@/components/inputs/SelectInput'
import apiRequest from '@/lib/auth/client/request'
import { handleError } from '@/lib/error'
import { enqueueSnackbar as notify } from 'notistack'
import { getDisplayNairaAmount } from '@/utilities'
import TransferCard from '@/components/anon/TransferCard'
import { Modal } from '@mantine/core'
import { CheckCircle } from 'lucide-react'
import { useDisclosure } from '@mantine/hooks'
import { NO_SMARTCARD, NO_SUBTYPE } from '@/components/constants'

interface FormData {
    biller: string
    smartCardNumber: string
    plan: string
    subscriptionType: string
    phone: string
    email: string
    username?: string
}

export default function ActionForm() {
    const [options, setOptions] = useState<
        | {
              name: string
              price: number
              code: string
          }[]
        | null
    >(null)
    const [isLoading, setIsLoading] = useState<{
        smartCardLoading: boolean
        plansLoading: boolean
    }>({
        smartCardLoading: false,
        plansLoading: false,
    })
    const [username, setUsername] = useState('')
    const {
        register,
        handleSubmit,
        setValue,
        reset,
        watch,
        setError,
        formState: { errors, isSubmitting },
        trigger,
    } = useForm<FormData>()

    const smartCardNumber = watch('smartCardNumber')
    const biller = watch('biller')
    const email = watch('email')
    const plan = watch('plan')

    const [paymentData, setPaymentData] = useState<{
        walletData: WalletAccountData
        amountToPay: string
    } | null>(null)
    const [opened, { open, close }] = useDisclosure(false)

    const onSubmit = async (data: FormData) => {
        try {
            const form_data = {
                biller: data.biller,
                smartCardNumber: NO_SMARTCARD.includes(data.biller)
                    ? data.phone
                    : data.smartCardNumber,
                planCode: data.plan.split('|')[0],
                subscriptionType: NO_SUBTYPE.includes(data.biller)
                    ? undefined
                    : data.subscriptionType,
                phoneNumber: data.phone,
                emailAddress: data.email,
            }

            const response = await apiRequest(false).post(
                '/api/anon/tv',
                form_data
            )
            const { data: responseData } = response.data
            const { amount: amountToPay, ...otherData } = responseData
            setPaymentData({
                walletData: otherData,
                amountToPay,
            })
        } catch (err: unknown) {
            const error: ApiWithFormError<FormData> = handleError(err)
            if (error.errors) {
                error.errors.forEach((e) => {
                    setError(e.path, {
                        message: e.message,
                    })
                })
            }
            if (error.message && !error.errors) {
                notify(error.message)
            }
        }
    }

    //validate Smartcard number
    const validateSmartCardNumber = async () => {
        setUsername('')
        const form = {
            smartCardNumber: smartCardNumber,
            biller: biller,
        }
        const isValid = await trigger(['smartCardNumber', 'biller'])
        if (!isValid) trigger(['smartCardNumber', 'biller'])

        if (isValid) {
            try {
                setIsLoading((prev) => ({ ...prev, smartCardLoading: true }))
                const response = await apiRequest(false).post(
                    `/api/tv/verify`,
                    form
                )
                const { data } = response.data
                setUsername(data.name)
            } catch (err: unknown) {
                const error: ApiWithFormError<FormData> = handleError(err)
                if (error.errors) {
                    error.errors.forEach((e) => {
                        setError(e.path, {
                            message: e.message,
                        })
                    })
                }
                if (error.message && !error.errors) {
                    notify(error.message, { variant: 'error' })
                }
            } finally {
                setIsLoading((prev) => ({ ...prev, smartCardLoading: false }))
            }
        }
    }

    const fetchTvPlans = async (biller: string) => {
        setIsLoading((prev) => ({ ...prev, plansLoading: true }))
        setValue('plan', '')
        try {
            const response = await apiRequest(false).get(
                `/api/tv?biller=${biller}`
            )
            const { data } = response.data
            setOptions(data)
        } catch (err: unknown) {
            const error: ApiWithError = handleError(err)
            if (error.message && !error.errors) {
                notify(error.message)
            }
        } finally {
            setIsLoading((prev) => ({ ...prev, plansLoading: false }))
        }
    }

    const resetter = (failed: boolean) => {
        setPaymentData(null)
        if (!failed) {
            reset()
            open()
        }
    }

    const disableValidate = !(
        smartCardNumber?.length > 8 &&
        !isLoading.smartCardLoading &&
        Boolean(biller) &&
        Boolean(plan)
    )

    return (
        <>
            {paymentData?.walletData.reference ? (
                <TransferCard
                    amountToPay={paymentData.amountToPay}
                    walletData={paymentData.walletData}
                    resetter={resetter}
                />
            ) : (
                <>
                    <Modal
                        opened={opened}
                        onClose={close}
                        withCloseButton={false}
                        centered
                    >
                        <div className="w-full max-w-md bg-white overflow-hidden p-6">
                            <div className="flex flex-col items-center text-center">
                                <div className="mb-4 bg-green-100 p-3 rounded-full">
                                    <CheckCircle className="h-12 w-12 text-green-600" />
                                </div>

                                <h1 className="text-2xl font-bold text-gray-800 mb-2">
                                    Payment Successful!
                                </h1>
                                <p className="text-gray-600 mb-6">
                                    Thank you for your payment. <br />
                                    Your transaction is being processed. <br />
                                    We will notify you via email on the status
                                    of your transaction (usually takes about a
                                    minute).
                                </p>
                            </div>
                        </div>
                    </Modal>

                    <form
                        method="POST"
                        onSubmit={handleSubmit(onSubmit)}
                        className="flex flex-col gap-[2.75rem] max-w-md mx-auto md:mx-0"
                    >
                        <div className="flex flex-col gap-5 md:gap-7">
                            <SelectInput<FormData>
                                label={'Biller Name'}
                                inputOptions={[
                                    { option: 'DSTV', value: 'DSTV' },
                                    { option: 'GOTV', value: 'GOTV' },
                                    { option: 'STARTIMES', value: 'STARTIMES' },
                                    { option: 'SHOWMAX', value: 'SHOWMAX' },
                                ]}
                                onInput={(e) => {
                                    const biller = e.currentTarget.value
                                    setUsername('')
                                    setValue('biller', biller)
                                    setOptions(null)
                                    if (biller) {
                                        fetchTvPlans(biller)
                                    }
                                }}
                                registerName={'biller'}
                                register={register}
                                errors={errors}
                                validation={{
                                    required: {
                                        value: true,
                                        message: `Biller name is required`,
                                    },
                                }}
                            />
                            <SelectInput<FormData>
                                label={'Choose Plan'}
                                placeholder={
                                    isLoading.plansLoading ? 'Please wait' : ''
                                }
                                disabled={isLoading.plansLoading || !options}
                                inputOptions={
                                    options &&
                                    options.map((option) => ({
                                        option: `${option.name.split(/\sN\d+/)[0]} @ ${getDisplayNairaAmount(option.price)}`,
                                        value: `${option.code}|${option.name.split(/\sN\d+/)[0]} @ ${getDisplayNairaAmount(option.price)}`,
                                    }))
                                }
                                registerName={'plan'}
                                register={register}
                                errors={errors}
                                validation={{
                                    required: {
                                        value: true,
                                        message: 'Please choose a plan',
                                    },
                                }}
                            />

                            {!NO_SMARTCARD.includes(biller) && (
                                <Input<FormData>
                                    type={'number'}
                                    label={'Smart Card Number'}
                                    registerName={'smartCardNumber'}
                                    placeholder={'Enter smart card number'}
                                    register={register}
                                    errors={errors}
                                    validation={{
                                        required: {
                                            value: true,
                                            message: `Smart card number is required`,
                                        },
                                        minLength: {
                                            value: 8,
                                            message:
                                                'Input a valid meter number',
                                        },
                                    }}
                                    onInput={() => setUsername('')}
                                />
                            )}

                            {(username || NO_SMARTCARD.includes(biller)) && (
                                <>
                                    {!NO_SMARTCARD.includes(biller) && (
                                        <Input<FormData>
                                            type={'text'}
                                            label={'Account Name'}
                                            registerName={'username'}
                                            placeholder={'username'}
                                            isDisabled={true}
                                            defaultValue={username}
                                            register={register}
                                            errors={errors}
                                            validation={{
                                                required: {
                                                    value: false,
                                                    message:
                                                        'Username is required',
                                                },
                                            }}
                                        />
                                    )}

                                    {!NO_SUBTYPE.includes(biller) && (
                                        <SelectInput<FormData>
                                            label={'Subscription Type'}
                                            placeholder="Choose subscription type"
                                            inputOptions={[
                                                {
                                                    option: 'I want to change bouquet',
                                                    value: 'CHANGE',
                                                },
                                                {
                                                    option: 'I want to keep my current bouquet',
                                                    value: 'RENEW',
                                                },
                                            ]}
                                            registerName={'subscriptionType'}
                                            register={register}
                                            errors={errors}
                                            validation={{
                                                required: {
                                                    value: true,
                                                    message: `Subscription type is required`,
                                                },
                                            }}
                                        />
                                    )}

                                    <Input<FormData>
                                        type={'tel'}
                                        label={`Phone Number${biller === 'SHOWMAX' ? ' Attached to Account' : ''} `}
                                        registerName={'phone'}
                                        placeholder={'Enter your phone number'}
                                        register={register}
                                        errors={errors}
                                        validation={{
                                            required: {
                                                value: true,
                                                message: `Phone number is required`,
                                            },
                                            pattern: {
                                                value: /^(?:\+?234|0)[789][01]\d{8}$/,
                                                message:
                                                    'Please enter a valid Nigerian phone number',
                                            },
                                        }}
                                    />

                                    <Input<FormData>
                                        type={'email'}
                                        label={'Email Address'}
                                        register={register}
                                        registerName={'email'}
                                        placeholder={'Get updates via email'}
                                        errors={errors}
                                        validation={{
                                            required: {
                                                value: true,
                                                message: `Email address is required`,
                                            },
                                            pattern: {
                                                value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                                                message:
                                                    'Please enter a valid email address',
                                            },
                                        }}
                                    />
                                </>
                            )}
                        </div>

                        <div className="flex flex-col gap-4">
                            {username || NO_SMARTCARD.includes(biller) ? (
                                <button
                                    disabled={
                                        NO_SMARTCARD.includes(biller)
                                            ? isSubmitting || !email
                                            : isSubmitting ||
                                              !username ||
                                              !email
                                    }
                                    type="submit"
                                    className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] disabled:opacity-20
                                ${isSubmitting ? ' animate-pulse' : null}
                        `}
                                >
                                    {isSubmitting
                                        ? 'Please wait...'
                                        : 'Buy now'}
                                </button>
                            ) : (
                                <button
                                    type="button"
                                    onClick={validateSmartCardNumber}
                                    disabled={disableValidate}
                                    className="font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] disabled:opacity-60 hover:disabled:opacity-60 disabled:cursor-not-allowed"
                                >
                                    {isLoading.smartCardLoading ? (
                                        <span>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                className="lucide lucide-loader-circle animate-spin"
                                            >
                                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                            </svg>
                                        </span>
                                    ) : (
                                        'Validate Smart Card Number'
                                    )}
                                </button>
                            )}
                        </div>
                    </form>
                </>
            )}
        </>
    )
}
