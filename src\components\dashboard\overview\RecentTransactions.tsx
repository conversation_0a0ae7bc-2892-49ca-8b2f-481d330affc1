import { Suspense } from 'react'
import TableSection from './TableSection'
import TableFallback from '../fallbackUI/TableFallback'

export default async function RecentTransactions({
    params,
}: {
    params: Param
}) {
    return (
        <div className="px-4 py-6 md:p-[26px] flex flex-col gap-6 bg-white rounded-xl max-w-full">
            <div className="gap-1 flex flex-col">
                <h3 className="text-[18px] font-medium text-[#182230]">
                    Your Recent Transactions
                </h3>
                <p className="text-[#667085] text-sm">
                    Stay on top of your spending
                </p>
                <Suspense
                    key={JSON.stringify(params)}
                    fallback={<TableFallback />}
                >
                    <TableSection params={params} />
                </Suspense>
            </div>
        </div>
    )
}
