'use client'
import {
    isValidSession,
    SESSION_KEY,
    sessionDecoder,
    sessionEncoder,
} from '@/lib/common'
import { usePathname } from 'next/navigation'
import { useState, useEffect } from 'react'

function getCookie(name: string): string | null {
    const cookies = document.cookie.split('; ')
    for (const cookie of cookies) {
        const [key, value] = cookie.split('=')
        if (key === name) {
            return decodeURIComponent(value)
        }
    }
    return null
}

function setCookie(name: string, value: string, milliseconds?: number): void {
    let expires = ''
    if (milliseconds) {
        const date = new Date()
        date.setTime(date.getTime() + milliseconds)
        expires = `; expires=${date.toUTCString()}`
    }
    document.cookie = `${name}=${encodeURIComponent(value)}${expires}; path=/`
}

function deleteCookie(name: string): void {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
}

function getSession(): SessionData | null {
    const session = getCookie(SESSION_KEY)
    if (!session) return null
    const decodedSession = sessionDecoder(session)
    if (!isValidSession(decodedSession)) return null
    return decodedSession
}

function setSession(sessionData: SessionData): void {
    const encodedSession = sessionEncoder(sessionData)
    const expires = sessionData.token.refreshExpiresIn
    const milliseconds = new Date(expires).getTime() - Date.now()
    setCookie(SESSION_KEY, encodedSession, milliseconds)
}

function clearSession(): void {
    deleteCookie(SESSION_KEY)
}

const useSession = (): SessionObject => {
    const pathname = usePathname()
    const [isLoading, setIsLoading] = useState(true)
    const [session, setSession] = useState<SessionData | null>(null)

    useEffect(() => {
        setSession(getSession())
        setIsLoading(false)
    }, [pathname])

    const isAuthenticated = session !== null

    return { isLoading, session, isAuthenticated }
}

export { getSession, setSession, clearSession, useSession }
