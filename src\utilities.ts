import { NGN } from '@dinero.js/currencies'
import { Dinero, dinero, toDecimal, toSnapshot } from 'dinero.js'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

dayjs.extend(utc)
dayjs.extend(timezone)

const money = (amount: number) => {
    return dinero({ amount: amount, currency: NGN })
}

const getAmount = (money: Dinero<number>) => {
    return toSnapshot(money).amount
}

const getDisplayNairaAmount = (amount: number) => {
    const formattedAmount = new Intl.NumberFormat('en-NG', {
        style: 'currency',
        currency: 'NGN',
        minimumFractionDigits: 2,
    }).format(Number(toDecimal(money(amount))))

    return formattedAmount
}

const getDisplayAmount = (amount: number) => {
    const formattedAmount = new Intl.NumberFormat('en-NG', {
        minimumFractionDigits: 2,
    }).format(Number(toDecimal(money(amount))))

    return formattedAmount
}

const formatDate = (dateString?: string | null): string => {
    const d = dayjs(dateString).tz('Africa/Lagos')
    return d.isValid() ? d.format('h:mmA - D/M/YY').toLowerCase() : '-'
}

const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
        case 'success':
            return '#079455'
        case 'initiated':
            return '#787f91'
        case 'pending':
        case 'reversed':
            return '#F79009'
        default:
            return '#F04438' //failed
    }
}

const toTitleCase = (sentence: string): string => {
    if (!sentence) return ''
    return sentence.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase())
}

const truncate = (str: string, length: number) => {
    if (str.length <= length) return str
    return str.slice(0, length) + '...'
}

export {
    money,
    getAmount,
    getDisplayAmount,
    getDisplayNairaAmount,
    formatDate,
    getStatusColor,
    toTitleCase,
    truncate,
}
