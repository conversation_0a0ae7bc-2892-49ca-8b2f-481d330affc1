import { toTitleCase } from '@/utilities'

const getBadge = (position: number) => {
    if (position === 1) return '🥇'
    if (position === 2) return '🥈'
    if (position === 3) return '🥉'
    return
}

const getFirstName = (name: string) => {
    const names = name.split(' ')
    return names.length > 0 ? toTitleCase(names[0]) : toTitleCase(name)
}

const getNameTag = (name: string, you: boolean) => {
    const firstChar = name.charAt(0).toUpperCase()
    if (you) {
        return (
            <span className="inline-flex w-10 h-10 items-center justify-center rounded-full bg-primary font-semibold text-[1rem] text-white">
                {firstChar}
            </span>
        )
    } else {
        return (
            <span className="inline-flex w-10 h-10 items-center justify-center rounded-full bg-[#F0F5FF] font-semibold text-[1rem] text-primary">
                {firstChar}
            </span>
        )
    }
}

function TableSection({ users }: { users: RewardData[] }) {
    return (
        <div className="overflow-x-auto text-[#182230] text-sm py-8 md:py-[44px] text-left">
            <table className="table-auto border-collapse w-full">
                <thead>
                    <tr className="border-b border-[#EAECF0] bg-[#F2F4F7]">
                        <th className="p-4 min-w-6 first:rounded-tl-lg">
                            Rank
                        </th>
                        <th className="p-4 min-w-[10rem]">User</th>
                        <th className="p-4 min-w-20 last:rounded-tr-lg">
                            Points
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {users.map((user: RewardData, index) => {
                        const isYou = user.you
                        const nextUserIsYou =
                            index < users.length - 1 && users[index + 1].you

                        return (
                            <tr
                                key={index}
                                className={
                                    'border-b border-[#EAECF0] text-[#475467]' +
                                    (isYou
                                        ? ' bg-[#F0F5FF] font-semibold !border-none'
                                        : '') +
                                    (nextUserIsYou ? ' !border-b-0' : '')
                                }
                            >
                                <td
                                    className={
                                        'p-4 min-w-6 space-x-2' +
                                        (user.you &&
                                            ' text-primary font-semibold rounded-l-xl bg-[#F0F5FF]')
                                    }
                                >
                                    <span>{getBadge(user.position)}</span>
                                    <span>{user.position}</span>
                                </td>
                                <td
                                    className={
                                        'p-4 min-w-[10rem] gap-3 flex items-center ' +
                                        (user.you &&
                                            ' text-primary font-semibold bg-[#F0F5FF]')
                                    }
                                >
                                    <span>
                                        {getNameTag(user.name, user.you)}
                                    </span>
                                    <span>{getFirstName(user.name)}</span>
                                </td>
                                <td
                                    className={
                                        'p-4 min-w-20' +
                                        (user.you &&
                                            ' text-primary font-semibold rounded-r-xl bg-[#F0F5FF]')
                                    }
                                >
                                    {user.points} Points
                                </td>
                            </tr>
                        )
                    })}
                </tbody>
            </table>
        </div>
    )
}

export default TableSection
