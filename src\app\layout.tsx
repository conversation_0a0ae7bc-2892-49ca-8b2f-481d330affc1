import type { Metadata } from 'next'
import './globals.css'
import { CSSProperties } from 'react'
import { general, inter, brico, helv, vietnam } from './fonts'
import { MantineProviders } from '@/components/MantineProvider'
import ToastWrapper from '@/components/ToastWrapper'
import { ThemeProvider } from 'next-themes'

export const metadata: Metadata = {
    title: 'Cip Topup',
    description: 'Cip Topup',
}

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode
}>) {
    return (
        <html lang="en" suppressHydrationWarning>
            <head>
                <meta name="color-scheme" content="light" />
            </head>
            <body
                className={`${general.variable} ${inter.variable} ${brico.variable} ${helv.variable} ${vietnam.variable} antialiased font-general dark:bg-white`}
                style={{ '--pColor': '#004EEC' } as CSSProperties}
            >
                <ThemeProvider attribute="class" enableSystem={false}>
                    <MantineProviders>{children}</MantineProviders>
                </ThemeProvider>
                <ToastWrapper />
            </body>
        </html>
    )
}
