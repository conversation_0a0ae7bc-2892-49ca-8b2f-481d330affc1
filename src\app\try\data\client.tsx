'use client'
import { useForm } from 'react-hook-form'
import React, { useEffect, useState } from 'react'
import Input from '@/components/inputs/Input'
import SelectInput from '@/components/inputs/SelectInput'
import { enqueueSnackbar as notify } from 'notistack'
import apiRequest from '@/lib/auth/client/request'
import { handleError } from '@/lib/error'
import TransferCard from '@/components/anon/TransferCard'
import { Modal } from '@mantine/core'
import { CheckCircle } from 'lucide-react'
import { useDisclosure } from '@mantine/hooks'

interface FormData {
    network: string
    phone: string
    plan: string
    email: string
    type: string
}

const networkPrefixes: Record<string, string[]> = {
    MTN: [
        '0803',
        '0806',
        '0703',
        '0706',
        '0813',
        '0816',
        '0810',
        '0814',
        '0903',
        '0906',
        '0913',
        '0702',
        '0704',
    ],
    AIRTEL: [
        '0802',
        '0808',
        '0708',
        '0812',
        '0701',
        '0902',
        '0901',
        '0907',
        '0912',
    ],
    '9MOBILE': ['0809', '0818', '0817', '0908', '0909'],
    GLO: ['0805', '0807', '0705', '0811', '0815', '0905', '0915'],
}

export default function ActionForm() {
    const [options, setOptions] = useState<
        | {
              id: string
              name: string
              network: string
              price: number
              size: string
              type: string
              validity: string
          }[]
        | null
    >(null)
    const [isLoading, setIsLoading] = useState(false)
    const [paymentData, setPaymentData] = useState<{
        walletData: WalletAccountData
        amountToPay: string
    } | null>(null)

    const {
        register,
        handleSubmit,
        reset,
        watch,
        setValue,
        setError,
        clearErrors,
        formState: { errors, isSubmitting },
    } = useForm<FormData>()

    const network = watch('network')
    const phone = watch('phone')
    const type = watch('type')
    const [opened, { open, close }] = useDisclosure(false)

    const onSubmit = async (data: FormData) => {
        const [plan_id] = data.plan.split('|')
        const form_data = {
            plan_id,
            phone_number: data.phone,
            email_address: data.email,
        }

        try {
            const response = await apiRequest(false).post(
                '/api/anon/data',
                form_data
            )
            const { data: responseData } = response.data
            const { amount: amountToPay, ...otherData } = responseData
            setPaymentData({
                walletData: otherData,
                amountToPay,
            })
        } catch (err: unknown) {
            const error: ApiWithFormError<FormData> = handleError(err)
            if (error.errors) {
                error.errors.forEach((e) => {
                    setError(e.path, {
                        message: e.message,
                    })
                })
            }
            if (error.message && !error.errors) {
                notify(error.message)
            }
        }
    }

    // throw warning when right number is not selected
    useEffect(() => {
        if (network && phone) {
            const allowedPrefixes = networkPrefixes[network] || []
            const phonePrefix = phone.replace(/^\+234/, '0').slice(0, 4) // Normalize +234 format

            if (
                allowedPrefixes.length &&
                !allowedPrefixes.includes(phonePrefix)
            ) {
                setError('phone', {
                    type: 'custom',
                    message: `Crosscheck if this is a/an ${network} number`,
                })
            } else {
                clearErrors('phone')
            }
        }
    }, [network, phone, setError, clearErrors])

    //fetch data plans on user network select
    useEffect(() => {
        const fetchDataPlans = async (network: string, type: string) => {
            setIsLoading(true)
            setValue('plan', '')
            try {
                const response = await apiRequest(false).get(
                    `api/data/plans?network=${network}&type=${type}` // type is either AWOOF, GIFTING or SME
                )
                const { data } = response.data
                setOptions(data)
            } catch (err: unknown) {
                const error: ApiWithError = handleError(err)
                if (error.message && !error.errors) {
                    notify(error.message)
                }
            } finally {
                setIsLoading(false)
            }
        }

        if (network && type) fetchDataPlans(network, type)
    }, [network, type, setValue])

    const resetter = (failed: boolean) => {
        setPaymentData(null)
        if (!failed) {
            reset()
            open()
        }
    }

    return (
        <>
            {paymentData?.walletData.reference ? (
                <TransferCard
                    amountToPay={paymentData.amountToPay}
                    walletData={paymentData.walletData}
                    resetter={resetter}
                />
            ) : (
                <>
                    <Modal
                        opened={opened}
                        onClose={close}
                        withCloseButton={false}
                        centered
                    >
                        <div className="w-full max-w-md bg-white overflow-hidden p-6">
                            <div className="flex flex-col items-center text-center">
                                <div className="mb-4 bg-green-100 p-3 rounded-full">
                                    <CheckCircle className="h-12 w-12 text-green-600" />
                                </div>

                                <h1 className="text-2xl font-bold text-gray-800 mb-2">
                                    Payment Successful!
                                </h1>
                                <p className="text-gray-600 mb-6">
                                    Thank you for your payment. <br />
                                    Your transaction is being processed. <br />
                                    We will notify you via email on the status
                                    of your transaction (usually takes about a
                                    minute).
                                </p>
                            </div>
                        </div>
                    </Modal>
                    <form
                        method="POST"
                        onSubmit={handleSubmit(onSubmit)}
                        className="flex flex-col gap-[2.75rem] max-w-md mx-auto md:mx-0"
                    >
                        <div className="flex flex-col gap-5 md:gap-7">
                            <SelectInput<FormData>
                                label={'Choose Network'}
                                inputOptions={[
                                    { option: 'MTN', value: 'MTN' },
                                    { option: 'GLO', value: 'GLO' },
                                    { option: 'AIRTEL', value: 'AIRTEL' },
                                    { option: '9MOBILE', value: '9MOBILE' },
                                ]}
                                registerName={'network'}
                                register={register}
                                errors={errors}
                                validation={{
                                    required: {
                                        value: true,
                                        message: `Network is required`,
                                    },
                                }}
                            />
                            <SelectInput<FormData>
                                label={'Choose Data Type'}
                                inputOptions={[
                                    { option: 'Awoof', value: 'AWOOF' },
                                    { option: 'Gifting', value: 'GIFTING' },
                                    { option: 'SME', value: 'SME' },
                                    { option: 'DataShare', value: 'DATASHARE' },
                                ]}
                                registerName={'type'}
                                register={register}
                                errors={errors}
                                validation={{
                                    required: {
                                        value: true,
                                        message: `Data type is required`,
                                    },
                                }}
                            />
                            <SelectInput<FormData>
                                label={'Choose Plan'}
                                placeholder={isLoading ? 'Please wait' : ''}
                                disabled={isLoading}
                                inputOptions={
                                    options &&
                                    options.map((option) => ({
                                        option: `${option.name} - ${option.type} - ${option.validity} @ ₦${option.price / 100}`,
                                        value: `${option.id}|${option.name} - ${option.type} - ${option.validity} @ ₦${option.price / 100}|₦${option.price / 100}`,
                                    }))
                                }
                                registerName={'plan'}
                                register={register}
                                errors={errors}
                                validation={{
                                    required: {
                                        value: true,
                                        message: `Plan is required`,
                                    },
                                }}
                            />
                            <Input<FormData>
                                type={'tel'}
                                label={'Phone Number'}
                                registerName={'phone'}
                                placeholder={'Enter your phone number'}
                                register={register}
                                errors={errors}
                                validation={{
                                    required: {
                                        value: true,
                                        message: `Phone number is required`,
                                    },
                                    pattern: {
                                        value: /^(?:\+?234|0)[789][01]\d{8}$/,
                                        message:
                                            'Please enter a valid Nigerian phone number',
                                    },
                                }}
                            />

                            <Input<FormData>
                                type={'email'}
                                label={'Email Address'}
                                register={register}
                                registerName={'email'}
                                placeholder={'Enter your email address'}
                                errors={errors}
                                validation={{
                                    required: {
                                        value: true,
                                        message: `Email address is required`,
                                    },
                                    pattern: {
                                        value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                                        message:
                                            'Please enter a valid email address',
                                    },
                                }}
                            />
                        </div>

                        <div className="flex flex-col gap-4">
                            <button
                                disabled={isSubmitting}
                                type="submit"
                                className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem] disabled:opacity-20
                    ${isSubmitting ? ' animate-pulse' : null}
                        `}
                            >
                                {isSubmitting ? 'Please wait...' : 'Buy now'}
                            </button>
                        </div>
                    </form>
                </>
            )}
        </>
    )
}
