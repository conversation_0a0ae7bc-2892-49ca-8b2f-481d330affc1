import Image from 'next/image'
import Link from 'next/link'
import TryIt from './TryIt'

export default function Everything() {
    return (
        <div className="px-4 py-[2.375rem] max-w-6xl mx-auto gap-11 md:gap-[2.875rem]  pb-[4.125rem] md:pb-[6.25rem] flex flex-col">
            <div
                className="flex flex-col items-center gap-4 text-center md:gap-3"
                data-aos="fade-up"
            >
                <h2
                    className={`font-brico font-semibold text-[1.75rem] leading-[2.0625rem] md:text-[2.75rem] md:leading-[3.25rem] md:text-[#101828]`}
                >
                    Everything You Need, All in One Place
                </h2>
                <p className="max-w-[40.625rem] leading-[1.3125rem] text-[#98A2B3] md:text-[1.5rem] md:leading-[2rem] md:text-[#475467]">
                    Stay connected and powered with our fast, reliable, and
                    secure services designed for your everyday needs
                </p>
            </div>

            <div className="flex flex-col gap-8">
                <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                    <div
                        className="rounded-xl md:rounded-[1.5rem] bg-[#F7F7F7] p-5 md:p-7 flex flex-col gap-[3.5625rem] items-start justify-between"
                        data-aos="fade-up"
                    >
                        <div className="flex flex-col gap-6">
                            <div className="flex flex-col items-start gap-2">
                                <h2
                                    className={`font-brico relative text-[#101828] font-semibold md:text-[1.5rem]`}
                                >
                                    Airtime & Data Top-up
                                </h2>
                                <ul className="space-y-2 ">
                                    {[
                                        'Top up any mobile network in seconds with zero hassle',
                                        'Enjoy instant confirmation and secure payments',
                                    ].map((perk) => (
                                        <li
                                            key={perk}
                                            className="text-[#344054] text-[.875rem]  md:text-[1rem]"
                                        >
                                            {perk}
                                        </li>
                                    ))}
                                </ul>
                            </div>
                            <div className="max-w-[14.375rem] md:max-w-full">
                                <Image
                                    alt="networks"
                                    src="/img/networkCluster.svg"
                                    height={342}
                                    width={72.5}
                                    className="w-full h-auto max-w-full"
                                />
                            </div>
                        </div>
                        <TryIt color="#004EEC" href="/try/data" />
                    </div>
                    {[
                        {
                            icon: '/icons/spark.svg',
                            tryIcon: 'icons/trySpark.svg',
                            link: '/try/electricity',
                            color: '#FF7502',
                            heading: 'Electricity Payments',
                            perks: [
                                'Seamlessly pay for prepaid or postpaid electricity across major providers',
                                'Enjoy uninterrupted power without stress',
                            ],
                        },
                        {
                            icon: '/icons/cable.svg',
                            tryIcon: 'icons/tryCable.svg',
                            link: '/try/cable',
                            color: '#0E8E32',
                            heading: 'Cable Subscriptions',
                            perks: [
                                'Renew your favorite TV subscriptions',
                                'Access uninterrupted entertainment with instant activation',
                            ],
                        },
                    ].map((service) => (
                        <div
                            key={service.heading}
                            className="rounded-xl md:rounded-[1.5rem] bg-[#F7F7F7] p-5 md:p-7 flex flex-col gap-[4.1875rem] items-start justify-between"
                            data-aos="fade-up"
                        >
                            <div className="flex flex-col gap-6">
                                <div
                                    className="rounded-full w-[3.8125rem] h-[3.8125rem] flex items-center justify-center"
                                    style={{ backgroundColor: service.color }}
                                >
                                    <Image
                                        src={service.icon}
                                        alt=""
                                        width={52}
                                        height={52}
                                        className="w-8 aspect-square"
                                    />
                                </div>
                                <div className="flex flex-col items-start gap-2">
                                    <h2
                                        className={`font-brico relative text-[#101828] font-semibold md:text-[1.5rem]`}
                                    >
                                        {service.heading}
                                    </h2>
                                    <ul className="space-y-2 ">
                                        {service.perks.map((perk) => (
                                            <li
                                                key={perk}
                                                className="text-[#344054] text-[.875rem]  md:text-[1rem]"
                                            >
                                                {perk}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </div>
                            <TryIt
                                color={service.color}
                                href={service.link}
                                key={service.link}
                            />
                        </div>
                    ))}
                </div>
                {/* third */}
                <div className="flex flex-col gap-8 md:gap-5 lg:flex-row">
                    {/*  */}
                    <div
                        className="rounded-xl md:rounded-[1.5rem] p-4 bg-[#F5F5F5] w-full md:basis-3/5"
                        data-aos="fade-up"
                    >
                        <Image
                            src="/img/conPayment.webp"
                            alt=""
                            width={609}
                            height={344}
                            className="relative object-cover w-full h-full max-h-full rounded-md lg:w-auto md:rounded-xl"
                        />
                        <div className="absolute top-7 left-7 bottom-7 w-1/2 md:w-[45%] bg-white flex flex-col justify-between gap-4 rounded-md md:rounded-xl p-2 md:p-[.875rem]">
                            <div className="flex flex-col gap-1 text-left">
                                <h3
                                    className={`font-brico font-semibold leading-[1.1875rem] md:text-[1.75rem] md:leading-[2.0625rem] text-[#101828]`}
                                >
                                    Convenient payments, zero extra effort
                                </h3>
                                <p className="text-[10px] leading-[13px] md:text-[1.25rem] md:leading-[1.6875rem] text-[#475467]">
                                    Intuitive design and multiple payment
                                    options
                                </p>
                            </div>
                            <Link
                                href="mailto:<EMAIL>"
                                target="_blank"
                                className="bg-[#01DD3F] text-black gap-1 p-2 md:gap-2 text-[.4375rem] leading-[.5625rem] flex items-center md:text-[1rem] md:leading-[1.3125rem] rounded-[.3125rem] md:rounded-[.625rem] overflow-hidden"
                            >
                                <Image
                                    alt="mail"
                                    src="/icons/greenMail.svg"
                                    height={32}
                                    width={32}
                                    className="w-[.625rem] aspect-square md:w-8"
                                />
                                <span><EMAIL></span>
                            </Link>
                        </div>
                    </div>

                    <div
                        className="flex flex-row items-center justify-center gap-8 lg:gap-5 lg:flex-col lg:justify-between shrink-0"
                        data-aos="fade-up"
                    >
                        {/* mobile */}
                        {['/img/fastPay.svg', '/img/quickSecure.svg'].map(
                            (image) => (
                                <Image
                                    key={image}
                                    src={image}
                                    alt=""
                                    width={147}
                                    height={177}
                                    className="h-auto w-[9.1875rem] max-w-full block lg:hidden"
                                />
                            )
                        )}
                        {/* desktop */}
                        {['/img/fastPay1.svg', '/img/quickSecure1.svg'].map(
                            (image) => (
                                <Image
                                    key={image}
                                    src={image}
                                    alt=""
                                    width={103}
                                    height={177}
                                    className="hidden w-auto h-full max-h-full lg:block"
                                />
                            )
                        )}
                    </div>

                    <div className="rounded-xl md:rounded-[1.5rem] p-4 bg-[#F5F5F5] w-full md:basis-2/5 flex flex-col gap-5">
                        <p
                            className={`font-brico text-[1.375rem] leading-[1.625rem] md:text-[2rem] md:leading-[2.375rem] font-bold text-center px-[.8125rem] py-2 rounded-[.5419rem] md:rounded-[.625rem] bg-[#E7C500] text-black whitespace-nowrap`}
                        >
                            Stay connected, always!
                        </p>
                        <Image
                            src="/img/stayConnected.webp"
                            alt="stay connected"
                            width={429}
                            height={375}
                            className="w-full h-auto max-w-full"
                            data-aos="fade-up"
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}
