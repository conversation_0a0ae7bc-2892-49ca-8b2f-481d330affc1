import React from 'react'
import PageHeadingAndInfo from './PageHeadingAndInfo'

export default function DashboardPagesShell({
    children,
    innerComponentHeading,
    innerComponentContent,
}: {
    children: React.ReactNode
    innerComponentContent: string
    innerComponentHeading: string
}) {
    return (
        <div className="flex flex-col h-full">
            <div className="hidden md:block md:sticky top-0 z-[3]">
                <PageHeadingAndInfo
                    content={innerComponentContent}
                    heading={innerComponentHeading}
                />
            </div>

            <div className="grow h-full md:pl-[2.25rem] pt-[1.625rem] md:pt-0">
                <div className="flex flex-col max-w-5xl px-4 py-5 md:p-[3.125rem] bg-white h-full md:rounded-xl">
                    <div className="block md:hidden">
                        <PageHeadingAndInfo
                            content={innerComponentContent}
                            heading={innerComponentHeading}
                        />
                    </div>
                    {children}
                </div>
            </div>
        </div>
    )
}
