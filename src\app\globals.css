@tailwind base;
@tailwind components;
@tailwind utilities;

:root{
    --swiper-navigation-size: 15px !important;
}

a:hover{
    opacity: 75%;
}

button:hover{
    opacity: 85%;
}

.swiper-pagination{
    margin: 0 auto !important;
    position: relative !important;
}

.swiper-button-prev, .swiper-button-next{
    color: #004EEC !important;
}

.swiper-pagination-bullet-active{
    background: #004EEC !important;
}

.debug{
    outline: 1px solid red;
}

/* disable wheel in number input */
\input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* For Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

.bell {
  animation: jingle 0.6s ease-in-out infinite;
  animation-delay: 0s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-play-state: running;
  transform-origin: top center;
}
@keyframes jingle {
  0% { transform: rotate(0deg); }
  20% { transform: rotate(15deg); }
  40% { transform: rotate(-15deg); }
  60% { transform: rotate(10deg); }
  80% { transform: rotate(-10deg); }
  100% { transform: rotate(0deg); }
}



@keyframes lightning-1 {
  0% {
    top: 0;
    left: 0;
    transform: translate(-50%, -50%);
  }
  25% {
    top: 0;
    right: 0;
    left: auto;
    transform: translate(50%, -50%);
  }
  50% {
    bottom: 0;
    right: 0;
    top: auto;
    left: auto;
    transform: translate(50%, 50%);
  }
  75% {
    bottom: 0;
    left: 0;
    right: auto;
    top: auto;
    transform: translate(-50%, 50%);
  }
  100% {
    top: 0;
    left: 0;
    bottom: auto;
    right: auto;
    transform: translate(-50%, -50%);
  }
}

@keyframes lightning-2 {
  0% {
    top: 25%;
    left: 0;
    transform: translate(-50%, -50%);
  }
  25% {
    top: 0;
    right: 25%;
    left: auto;
    transform: translate(50%, -50%);
  }
  50% {
    bottom: 25%;
    right: 0;
    top: auto;
    left: auto;
    transform: translate(50%, 50%);
  }
  75% {
    bottom: 0;
    left: 25%;
    right: auto;
    top: auto;
    transform: translate(-50%, 50%);
  }
  100% {
    top: 25%;
    left: 0;
    bottom: auto;
    right: auto;
    transform: translate(-50%, -50%);
  }
}

@keyframes lightning-3 {
  0% {
    top: 50%;
    left: 0;
    transform: translate(-50%, -50%);
  }
  25% {
    top: 0;
    right: 50%;
    left: auto;
    transform: translate(50%, -50%);
  }
  50% {
    bottom: 50%;
    right: 0;
    top: auto;
    left: auto;
    transform: translate(50%, 50%);
  }
  75% {
    bottom: 0;
    left: 50%;
    right: auto;
    top: auto;
    transform: translate(-50%, 50%);
  }
  100% {
    top: 50%;
    left: 0;
    bottom: auto;
    right: auto;
    transform: translate(-50%, -50%);
  }
}

.animate-lightning-1 {
  animation: lightning-1 3s linear infinite;
}

.animate-lightning-2 {
  animation: lightning-2 4s linear infinite 0.5s;
}

.animate-lightning-3 {
  animation: lightning-3 2.5s linear infinite 1s;
}

.animate-spin-slow {
  animation: spin 8s linear infinite;
}