import { AnimatePresence, motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { RefObject, SetStateAction, useState } from 'react'
import SidebarLinksD from './SidebarLinksD'
import SidebarLinksM from './SidebarLinksM'
import { clearSession } from '@/lib/auth/client/session'
import { useRouter } from 'next/navigation'

const main = [
    {
        icons: '/icons/dashboard.svg',
        altIcons: '/icons/dashboardWhite.svg',
        heading: 'Dashboard',
        href: '/dashboard',
    },
    {
        icons: '/icons/fundWallet.svg',
        altIcons: '/icons/fundWalletWhite.svg',
        heading: 'Fund wallet',
        href: '/dashboard/fund',
    },
    {
        icons: '/icons/transactionHistory.svg',
        altIcons: '/icons/transactionHistoryWhite.svg',
        heading: 'Transaction history',
        href: '/dashboard/transaction-history',
    },
    // {
    //     icons: '/icons/topUpHistory.svg',
    //     altIcons: '/icons/topUpHistoryWhite.svg',
    //     heading: 'Funding History',
    //     href: '/dashboard/top-up-history',
    // },
    {
        icons: '/icons/reward.svg',
        altIcons: '/icons/rewardAlt.svg',
        heading: 'Rewards',
        href: '/dashboard/rewards',
    },
]

const services = [
    {
        icons: '/icons/airtimeTopup.svg',
        altIcons: '/icons/airtimeTopupWhite.svg',
        heading: 'Airtime Top-up',
        href: '/dashboard/airtime-top-up',
    },
    // {
    //     icons: '/icons/airtimeToCash.svg',
    //     altIcons: '/icons/airtimeToCashWhite.svg',
    //     heading: 'Airtime To Cash',
    //     href: '/dashboard/airtime-to-cash',
    // },
    {
        icons: '/icons/dataTopup.svg',
        altIcons: '/icons/dataTopupWhite.svg',
        heading: 'Data Top-up',
        href: '/dashboard/data-top-up',
    },
    {
        icons: '/icons/electricityBill.svg',
        altIcons: '/icons/electricityBillWhite.svg',
        heading: 'Electricity Bill',
        href: '/dashboard/electricity-bill',
    },
    {
        icons: '/icons/cableTvSubscription.svg',
        altIcons: '/icons/cableTvSubscriptionWhite.svg',
        heading: 'Cable TV Subscription',
        href: '/dashboard/cable-tv-subscription',
    },
]

export default function Sidebar({
    showSideBar,
    setShowSideBar,
    sidebar,
}: {
    showSideBar: boolean
    setShowSideBar: React.Dispatch<SetStateAction<boolean>>
    sidebar: RefObject<HTMLDivElement | null>
}) {
    const [isExpanded, setIsExpanded] = useState(true)

    const router = useRouter()

    const logout = () => {
        clearSession()
        router.push('/signin')
    }

    return (
        <>
            {/* For Mobile */}
            {showSideBar && (
                <AnimatePresence>
                    <div className="flex justify-between md:hidden fixed top-0 bottom-0 right-0 left-0 bg-[#000000cc] items-start z-[70]">
                        <div className="w-3/4 h-full bg-white">
                            <motion.div
                                ref={sidebar}
                                initial={{ opacity: 0, x: '-80%' }}
                                animate={{ opacity: 1, x: 0 }}
                                exit={{ opacity: 0.5, x: '-80%' }}
                                className="w-full bg-white overflow-y-auto h-full flex flex-col gap-8 py-4 px-4"
                            >
                                {/*  */}
                                <div className="-top-4 z-20 bg-white sticky flex">
                                    <div className="p-4 flex items-center gap-5 justify-between w-full">
                                        <Link
                                            href="/"
                                            className="w-[5.5rem] block"
                                        >
                                            <Image
                                                alt="Logo"
                                                src="/img/logo.png"
                                                width={85.6}
                                                height={32}
                                                className="w-full h-auto max-w-full"
                                            />
                                        </Link>
                                    </div>
                                </div>
                                <div className="flex flex-col gap-8">
                                    <div className="flex flex-col gap-1">
                                        <p className="py-2 px-3 gap-2 rounded-[.375rem]">
                                            Main
                                        </p>
                                        {main.map((link) => (
                                            // transport
                                            <SidebarLinksM
                                                heading={link.heading}
                                                altIcons={link.altIcons}
                                                key={link.heading}
                                                icons={link.icons}
                                                href={link.href}
                                                setShowSideBar={setShowSideBar}
                                            />
                                        ))}
                                    </div>
                                    {/*  */}
                                    <div className="flex flex-col gap-1">
                                        <p className="py-2 px-3 gap-2 rounded-[.375rem]">
                                            Our Services
                                        </p>
                                        {services.map((link) => (
                                            <SidebarLinksM
                                                heading={link.heading}
                                                altIcons={link.altIcons}
                                                key={link.heading}
                                                icons={link.icons}
                                                href={link.href}
                                                setShowSideBar={setShowSideBar}
                                            />
                                        ))}
                                    </div>
                                    {/*  */}
                                    <div className="flex flex-col gap-1">
                                        <p className="py-2 px-3 gap-2 rounded-[.375rem]">
                                            Settings
                                        </p>

                                        <SidebarLinksM
                                            heading={'My Account'}
                                            icons={'/icons/myAccount.svg'}
                                            altIcons={
                                                '/icons/myAccountWhite.svg'
                                            }
                                            href={'/dashboard/account'}
                                            setShowSideBar={setShowSideBar}
                                        />
                                        <SidebarLinksM
                                            heading={'Refer and earn'}
                                            icons={'/icons/referral.svg'}
                                            altIcons={
                                                '/icons/referralWhite.svg'
                                            }
                                            href={'/dashboard/referral'}
                                            setShowSideBar={setShowSideBar}
                                        />
                                        <button
                                            onClick={logout}
                                            className={`group flex items-center py-2 px-3 gap-2 rounded-[.375rem] hover:bg-primary hover:text-white text-[#475467]`}
                                        >
                                            <div className="w-6">
                                                <Image
                                                    src={'/icons/logout.svg'}
                                                    alt={'/icons/logout.svg'}
                                                    width={12}
                                                    height={12}
                                                    className="w-full h-auto group-hover:hidden"
                                                />
                                                <Image
                                                    src={
                                                        '/icons/logoutWhite.svg'
                                                    }
                                                    alt={
                                                        '/icons/logoutWhite.svg'
                                                    }
                                                    width={12}
                                                    height={12}
                                                    className="w-full h-auto hidden group-hover:block"
                                                />
                                            </div>
                                            <span>Logout</span>
                                        </button>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                        {/* cancel goes here */}
                        <div
                            className="w-8 m-2"
                            onClick={() => setShowSideBar(false)}
                        >
                            <Image
                                src="/icons/dashboardCancel.svg"
                                width={24}
                                height={24}
                                alt="cancel"
                                className="w-full h-auto"
                            />
                        </div>
                    </div>
                </AnimatePresence>
            )}

            {/* For Desktop */}
            <motion.div className="hidden md:flex bg-white overflow-y-auto max-h-screen h-full flex-col gap-8 py-4 px-4">
                {/*  */}
                <div className="-top-4 z-20 bg-white sticky flex">
                    <div
                        className={`${!isExpanded && 'flex-col'} p-4 flex items-center gap-5 justify-between w-full`}
                    >
                        {isExpanded ? (
                            <Link href="/" className="w-[5.5rem] block">
                                <Image
                                    alt="Logo"
                                    src="/img/logo.png"
                                    width={85.6}
                                    height={32}
                                    className="w-full h-auto max-w-full"
                                />
                            </Link>
                        ) : (
                            <Link href="/" className="w-8 block">
                                <Image
                                    alt="Logo"
                                    src="/icons/CIP.svg"
                                    width={24}
                                    height={24}
                                    className="w-full h-auto max-w-full"
                                />
                            </Link>
                        )}
                        <div
                            className="w-[1.3125rem] cursor-pointer"
                            onClick={() => setIsExpanded((value) => !value)}
                        >
                            <Image
                                src={
                                    isExpanded
                                        ? '/icons/collapse.svg'
                                        : '/icons/expand.svg'
                                }
                                alt="collapse/expand"
                                height={25}
                                width={25}
                                className="w-full h-auto"
                            />
                        </div>
                    </div>
                </div>
                <div className="flex flex-col gap-8">
                    <div className="flex flex-col gap-1">
                        <p
                            className={`${!isExpanded && 'hidden'} py-2 px-3 gap-2 rounded-[.375rem]`}
                        >
                            Main
                        </p>
                        {main.map((link) => (
                            // transport
                            <SidebarLinksD
                                heading={link.heading}
                                altIcons={link.altIcons}
                                key={link.heading}
                                icons={link.icons}
                                href={link.href}
                                isExpanded={isExpanded}
                            />
                        ))}
                    </div>
                    {/*  */}
                    <div className="flex flex-col gap-1">
                        <p
                            className={`${!isExpanded && 'hidden'} py-2 px-3 gap-2 rounded-[.375rem]`}
                        >
                            Our Services
                        </p>
                        {services.map((link) => (
                            <SidebarLinksD
                                heading={link.heading}
                                altIcons={link.altIcons}
                                key={link.heading}
                                icons={link.icons}
                                href={link.href}
                                isExpanded={isExpanded}
                            />
                        ))}
                    </div>
                    {/*  */}
                    <div className="flex flex-col gap-1">
                        <p
                            className={`${!isExpanded && 'hidden'} py-2 px-3 gap-2 rounded-[.375rem]`}
                        >
                            Settings
                        </p>
                        <SidebarLinksD
                            heading={'My Account'}
                            icons={'/icons/myAccount.svg'}
                            altIcons={'/icons/myAccountWhite.svg'}
                            href={'/dashboard/account'}
                            isExpanded={isExpanded}
                        />
                        <SidebarLinksD
                            heading={'Refer and earn'}
                            icons={'/icons/referral.svg'}
                            altIcons={'/icons/referralWhite.svg'}
                            href={'/dashboard/referral'}
                            isExpanded={isExpanded}
                        />
                        <button
                            className={`${!isExpanded && 'justify-center'} group flex items-center py-2 px-3 gap-2 rounded-[.375rem] hover:bg-primary hover:text-white text-[#475467]`}
                            onClick={logout}
                        >
                            <div className="w-6">
                                <Image
                                    src={'/icons/logout.svg'}
                                    alt={'/icons/logout.svg'}
                                    width={12}
                                    height={12}
                                    className="w-full h-auto group-hover:hidden"
                                />
                                <Image
                                    src={'/icons/logoutWhite.svg'}
                                    alt={'/icons/logoutWhite.svg'}
                                    width={12}
                                    height={12}
                                    className="w-full h-auto hidden group-hover:block"
                                />
                            </div>
                            <span className={`${!isExpanded && 'hidden'}`}>
                                Logout
                            </span>
                        </button>
                    </div>
                </div>
            </motion.div>
        </>
    )
}
