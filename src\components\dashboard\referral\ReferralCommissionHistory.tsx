'use client'

import apiRequest from '@/lib/auth/client/request'
import { handleError } from '@/lib/error'
import { Loader } from 'lucide-react'
import { enqueueSnackbar as notify } from 'notistack'
import { useState } from 'react'

export default function ReferralCommissionHistory({
    referralsInitialData,
}: {
    referralsInitialData: ReferralResponse
}) {
    const [referrals, setReferrals] = useState<Referral[]>(
        referralsInitialData.referrals
    )
    const [paginationData, setPaginationData] = useState<Pagination>(
        referralsInitialData.pagination
    )
    const [isLoading, setIsLoading] = useState(false)

    const loadMoreReferrals = async () => {
        if (!paginationData.has_next) return
        try {
            setIsLoading(true)
            const response = await apiRequest().get('/api/users/referrals', {
                params: {
                    page: paginationData.current_page + 1,
                    page_size: paginationData.page_size,
                },
            })

            const { referrals: newReferrals, pagination } = response.data
                .data as ReferralResponse
            setReferrals((prev) => [...prev, ...newReferrals])
            setPaginationData(pagination)
        } catch (err) {
            const error: ApiWithError = handleError(err)
            if (error.message && !error.errors) {
                notify(error.message)
            }
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className="rounded-3xl md:p-[2.125rem] flex flex-col bg-white p-6 gap-8 items-center">
            <div className="flex flex-col gap-1 w-full">
                <h2 className="font-brico text-[#182230] text-[1.125rem] md:text-[1.25rem] font-medium">
                    Referral Commission History
                </h2>
                <p className="text-[#475467]">Track Your Rewards</p>
            </div>
            <div className="overflow-x-auto text-[#182230] text-sm py-8 md:py-[44px] border-t border-[F5F5F5] text-left w-full">
                <table className="table-auto w-full border-separate">
                    <thead className="*:overflow-hidden">
                        <tr className="border-b border-[#EAECF0] flex rounded-t-lg bg-[#F2F4F7]">
                            <th className="w-full p-4 min-w-24">Name</th>
                            <th className="w-full p-4 min-w-24">Email</th>
                            <th className="w-full p-4 min-w-24">
                                Date Registered
                            </th>
                            <th className="w-full p-4 min-w-24">
                                Account status
                            </th>
                            <th className="w-full p-4 min-w-24">
                                Commission Status
                            </th>
                        </tr>
                    </thead>
                    <tbody className="*:overflow-hidden">
                        {referrals.map((referral) => {
                            return (
                                <tr
                                    key={referral.email}
                                    className="border-b border-[#EAECF0] flex"
                                >
                                    <td className="w-full p-4 min-w-24">
                                        {referral.full_name || 'N/A'}
                                    </td>
                                    <td className="w-full p-3 break-all min-w-20">
                                        {referral.email}
                                    </td>
                                    <td className="w-full p-4 min-w-24">
                                        {new Date(
                                            referral.date_created
                                        ).toLocaleDateString()}
                                    </td>
                                    <td className="w-full p-4 min-w-24">
                                        {referral.is_verified ? (
                                            <span className="text-green-600">
                                                Verified
                                            </span>
                                        ) : (
                                            <span className="text-red-600">
                                                Not Verified
                                            </span>
                                        )}
                                    </td>
                                    <td className="w-full p-4 min-w-24">
                                        {referral.claimed ? (
                                            <span className="text-green-600">
                                                Paid
                                            </span>
                                        ) : (
                                            <span className="text-red-600">
                                                Not Paid Yet
                                            </span>
                                        )}
                                    </td>
                                </tr>
                            )
                        })}
                    </tbody>
                </table>
            </div>

            {paginationData.has_next && (
                <button
                    onClick={loadMoreReferrals}
                    disabled={!paginationData.has_next || isLoading}
                    type="button"
                    className="flex text-sm font-medium items-center gap-1 text-primary border border-primary rounded-lg p-[.625rem] px-6"
                >
                    <span className="min-w-[4.5rem] flex items-center justify-center">
                        {isLoading ? (
                            <Loader className="w-4 h-4 animate-spin" />
                        ) : (
                            'Load More'
                        )}
                    </span>
                </button>
            )}
        </div>
    )
}
