'use server'
import apiRequest from '@/lib/auth/server/request'

export const fetchCommissionData = async () => {
    const client = await apiRequest()
    const summaryPromise = client.get('/api/users/referrals/summary')
    const referralsPromise = client.get('/api/users/referrals')
    const [summaryResponse, referralsResponse] = await Promise.all([
        summaryPromise,
       referralsPromise,
   ])
   const summaryData = summaryResponse?.data?.data as ReferralSummary
   const referralsData = referralsResponse?.data?.data as ReferralResponse
   if (!summaryData || !referralsData) {
       throw new Error('Failed to fetch referral data')
   }
   return { summaryData, referralsData }
}
