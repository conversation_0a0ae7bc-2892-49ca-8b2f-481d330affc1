import DashboardPagesShell from '@/components/dashboard/DashboardPagesShell'

export default function Terms() {
    return (
        <DashboardPagesShell
            innerComponentContent="Understand how to earn rewards fairly and transparently"
            innerComponentHeading="Referral Program Terms & Conditions"
        >
            {/* px-4 py-[1.5rem] */}
            <div className="max-w-xs md:max-w-2xl gap-[1.75rem] md:gap-8 flex flex-col bg-white">
                {[
                    {
                        heading: 'Introduction',
                        content: [
                            'By participating in our referral program, you agree to the following terms and conditions. Please read carefully before sharing your referral link',
                        ],
                    },
                    {
                        heading: 'Eligibility',
                        content: [
                            'The referral program is open to all registered users.',
                            'Only valid transactions qualify for rewards.',
                        ],
                    },
                    {
                        heading: 'How Rewards Work',
                        content: [
                            'You earn a reward when a referred user signs up and completes a qualifying transaction.',
                            'Bonuses are credited to your wallet immediately.',
                            'Rewards may be used for transactions or withdrawn (if applicable).',
                        ],
                    },
                    {
                        heading: 'Prohibited Activities',
                        content: [
                            'Self-referrals are not allowed.',
                            'Spamming, fraudulent activities, or misuse will result in disqualification.',
                        ],
                    },
                    {
                        heading: 'Reward Limitations',
                        content: [
                            'The company reserves the right to modify reward amounts or eligibility criteria.',
                            'Bonuses are non-transferable and cannot be exchanged for cash unless stated otherwise.',
                        ],
                    },
                    {
                        heading: 'Program Changes & Termination',
                        content: [
                            'We may update or discontinue the referral program at any time.',
                            'Any changes will be communicated in advance.',
                        ],
                    },
                ].map((detail) => (
                    <div key={detail.heading} className="flex flex-col gap-2">
                        <h3 className="text-[#182230] font-mediumn text-[1.125rem] md:text-[1.5rem]">
                            {detail.heading}
                        </h3>
                        <p className="text-[#475467]">{detail.content}</p>
                    </div>
                ))}
                <div className="flex flex-col gap-2">
                    <h3 className="text-[#182230] font-mediumn text-[1.125rem] md:text-[1.5rem]">
                        Contact & Support
                    </h3>
                    <p className="text-[#475467]">
                        If you have any questions about our referral program,
                        please contact{' '}
                        <a
                            href="mailto:<EMAIL>"
                            className="text-primary"
                        >
                            <EMAIL>.
                        </a>
                    </p>
                </div>
            </div>
        </DashboardPagesShell>
    )
}
