import Link from 'next/link'
import Image from 'next/image'
import ToastWrapper from '@/components/ToastWrapper'

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            <div className="max-w-[92rem] mx-auto">
                <div className="flex flex-col h-screen max-h-screen md:flex-row">
                    {/*  */}
                    <div className="px-4 py-6 gap-[2.75rem] overflow-y-auto flex flex-col h-full justify-between lg:basis-1/2 mx-auto max-w-6xl">
                        <div className="flex-1 flex flex-col gap-[2rem] max-w-5xl mx-auto">
                            <Link href="/" className="w-[5.5rem] block">
                                <Image
                                    alt="Logo"
                                    src="/img/logo.png"
                                    width={85.6}
                                    height={32}
                                    className="w-full h-auto max-w-full"
                                />
                            </Link>
                            {children}
                        </div>

                        <div className="items-center justify-between hidden gap-5 md:flex">
                            <p className="font-vietnam text-[#475467] text-sm leading-[1.0625rem]">
                                © CIP
                            </p>
                            <a
                                href="mailto:<EMAIL>"
                                className="font-vietnam text-[#475467] text-sm leading-[1.0625rem]"
                            >
                                <EMAIL>
                            </a>
                        </div>
                    </div>

                    <div className="hidden lg:flex basis-1/2 bg-[#F9FAFB] items-center justify-center overflow-hidden p-4">
                        <Image
                            alt="preview"
                            src="/img/landingTop.svg"
                            height={633}
                            width={556}
                            priority
                            className="w-full h-auto max-w-[500px]"
                        />
                    </div>
                </div>
            </div>
            <ToastWrapper />
        </>
    )
}
