import Image from 'next/image'
import React from 'react'

export default function Step({
    color,
    img,
    children,
    content,
}: {
    color: string
    img: string
    children: React.ReactNode
    content: string
}) {
    return (
        <div
            className="gap-6 md:gap-8 flex flex-col items-center text-center"
            data-aos="fade-up"
        >
            <div
                className="w-[60px] h-[60px] md:w-[5.625rem] md:h-[5.625rem] rounded-[.8125rem] flex items-center justify-center"
                style={{ backgroundColor: color }}
            >
                <Image
                    alt=""
                    src={img}
                    width={32}
                    height={32}
                    className="w-6 md:w-8 aspect-square"
                />
            </div>
            <div className="gap-2 md:gap-3 justify-between flex-col flex items-center">
                <h3
                    className={`font-brico font-semibold text-[1.5rem] leading-[1.75rem]`}
                >
                    {children}
                </h3>
                <p className="text-[#333333] leading-[1.625rem] md:text-[1.25rem]">
                    {content}
                </p>
            </div>
        </div>
    )
}
