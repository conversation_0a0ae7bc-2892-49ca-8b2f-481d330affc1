'use client'
import Input from '@/components/inputs/Input'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import EditInfo from './EditInfo'
import apiRequest from '@/lib/auth/client/request'
import { handleError } from '@/lib/error'
import { enqueueSnackbar as notify } from 'notistack'
import { useDisclosure } from '@mantine/hooks'
import { Modal } from '@mantine/core'
import { CheckCircle } from 'lucide-react'
import { revalidateDashboard } from '../../action/action'
import { getDisplayNairaAmount, truncate } from '@/utilities'
import { RotateCcw } from 'lucide-react'
import Link from 'next/link'

interface FormData {
    fName?: string
    phonenumber: string
    api_webhook_url?: string
}

interface FormData1 {
    oldPassword: string
    newPassword: string
    reNewPassword: string
}

interface UserInfo {
    name: string
    value: string | number
    edit?: boolean
    registerName?: 'fName' | 'phonenumber' | 'api_webhook_url'
}

const sectionArray: ['Profile', 'Security'] = ['Profile', 'Security']

interface FetchedData {
    email: string
    full_name: string
    notifications: {
        email_enabled: boolean
        push_enabled: boolean
        sms_enabled: boolean
    }
    phonenumber: string
    wallet_balance: number
    api_webhook_url?: string
}

export default function UserInfo({
    fetchedData,
}: {
    fetchedData: FetchedData
}) {
    const [section, setSection] = useState<'Profile' | 'Security'>('Profile')
    const [pNotify, setPNotify] = useState(
        fetchedData.notifications.push_enabled
    )
    const [eNotify, setENotify] = useState(
        fetchedData.notifications.email_enabled
    )
    const [sNotify, setSNotify] = useState(
        fetchedData.notifications.sms_enabled
    )
    const [showChangePassword, setShowChangePassword] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [generatingApiKey, setGeneratingApiKey] = useState(false)
    const [isSubmittingChangePassword, setIsSubmittingChangePassword] =
        useState(false)
    const [revertEditInput, setRevertEditInput] = useState(false) // the value don't matter we just need it change to trigger clearing
    const [opened, { open, close }] = useDisclosure(false)
    const [opened1, { open: open1, close: close1 }] = useDisclosure(false)
    const [apikey, setApiKey] = useState('XXXXXXXXXXXXXXXXXXXXX')
    const [webhook_secret, setWebhookSecret] = useState('XXXXXXXXXXXXXXXXXXXXX')
    const {
        handleSubmit,
        register,
        reset,
        setError,
        formState: { errors },
    } = useForm<FormData>()

    const {
        handleSubmit: handleSubmit1,
        register: register1,
        formState: { errors: errors1 },
        reset: reset1,
        watch: watch1,
    } = useForm<FormData1>()

    const password = watch1('newPassword')

    const onSubmit = async (data: FormData) => {
        setIsSubmitting(true)
        const form = {
            ...(data.fName && { full_name: data.fName }),
            ...(data.phonenumber && { phonenumber: data.phonenumber }),
            ...(data.api_webhook_url && {
                api_webhook_url: data.api_webhook_url,
            }),
        }
        try {
            await apiRequest().patch(`/api/users/profile`, form)
            reset()
            open1()
            revalidateDashboard()
            setRevertEditInput((value) => !value)
        } catch (err: unknown) {
            const error: ApiWithFormError<FormData> = handleError(err)
            if (error.errors) {
                error.errors.forEach((e) => {
                    setError(e.path, {
                        message: e.message,
                    })
                })
            }
            if (error.message && !error.errors) {
                notify(error.message, { variant: 'error' })
            }
        } finally {
            setIsSubmitting(false)
        }
    }

    const changePassword = async (data: FormData1) => {
        setIsSubmittingChangePassword(true)
        const form = {
            old_password: data.oldPassword,
            new_password: data.newPassword,
        }
        try {
            await apiRequest().post(`/api/auth/change-password`, form)
            setShowChangePassword(false)
            reset1()
            open()
        } catch (err: unknown) {
            const error: ApiWithError = handleError(err)
            if (error.message && !error.errors) {
                notify(error.message, { variant: 'error' })
            }
        } finally {
            setIsSubmittingChangePassword(false)
        }
    }

    const userInfo: UserInfo[] = [
        {
            name: 'Email:',
            value: fetchedData.email,
        },
        {
            name: 'Full Name:',
            value: fetchedData.full_name,
            edit: true,
            registerName: 'fName',
        },
        {
            name: 'Wallet Balance:',
            value: getDisplayNairaAmount(fetchedData.wallet_balance),
        },
        {
            name: 'Phone Number:',
            value: fetchedData.phonenumber,
            edit: true,
            registerName: 'phonenumber',
        },
    ]

    const devOptions: UserInfo[] = [
        {
            name: 'Webhook URL:',
            value: truncate(fetchedData.api_webhook_url || 'Not set', 18),
            edit: true,
            registerName: 'api_webhook_url',
        },
    ]

    const generateApiKey = async () => {
        setGeneratingApiKey(true)
        try {
            const response = await apiRequest().get(
                '/api/users/apikey/regenerate'
            )
            const { data } = response.data
            setApiKey(data.api_key)
            setWebhookSecret(data.webhook_secret)
            notify(
                'Please copy your API key now, it will not be shown again!',
                { variant: 'success' }
            )
        } catch (err: unknown) {
            const error: ApiWithError = handleError(err)
            if (error.message && !error.errors) {
                notify(error.message, { variant: 'error' })
            }
        } finally {
            setGeneratingApiKey(false)
        }
    }
    return (
        <div className="flex flex-col gap-[4.0625rem] items-center">
            <Modal
                opened={opened}
                onClose={close}
                withCloseButton={false}
                centered
            >
                <div className="w-full max-w-md bg-white overflow-hidden p-6">
                    <div className="flex flex-col items-center text-center">
                        <div className="mb-4 bg-green-100 p-3 rounded-full">
                            <CheckCircle className="h-12 w-12 text-green-600" />
                        </div>

                        <h1 className="text-2xl font-bold text-gray-800 mb-2">
                            Password Change Successful!
                        </h1>
                    </div>
                </div>
            </Modal>
            <Modal
                opened={opened1}
                onClose={close1}
                withCloseButton={false}
                centered
            >
                <div className="w-full max-w-md bg-white overflow-hidden p-6">
                    <div className="flex flex-col items-center text-center">
                        <div className="mb-4 bg-green-100 p-3 rounded-full">
                            <CheckCircle className="h-12 w-12 text-green-600" />
                        </div>

                        <h1 className="text-2xl font-bold text-gray-800 mb-2">
                            User Details Edited Successfully!
                        </h1>
                    </div>
                </div>
            </Modal>
            <div className="rounded-[3.75rem] p-2 bg-[#F2F4F7] border border-[#D0D5DD] flex flex-row items-center">
                {sectionArray.map((option) => (
                    <div
                        key={option}
                        className={`py-2 px-8 text-sm leading-[1.1812rem] cursor-pointer ${option === section ? 'text-white bg-primary rounded-[6.1875rem] font-medium' : 'text-[#475467]'}`}
                        onClick={() => setSection(option)}
                    >
                        {option}
                    </div>
                ))}
            </div>
            <div className="w-full">
                {section === 'Profile' && (
                    <form
                        onSubmit={handleSubmit(onSubmit)}
                        method="POST"
                        className="flex flex-col gap-[6.25rem] items-center"
                    >
                        <div className="flex flex-col gap-[3.125rem] w-full">
                            <div className="flex flex-col gap-6">
                                <h3 className="text-[#98A2B3] font-medium text-[1.65rem] md:text-[1.875rem] leading-normal md:leading-[2.5313rem]">
                                    Account and Information
                                </h3>
                                <div className="flex flex-col gap-8 max-w-md">
                                    {userInfo.map((setting) => (
                                        <div
                                            key={setting.name}
                                            className={`flex justify-between gap-5 items-center`}
                                        >
                                            <p className="text-[#182230] font-medium">
                                                {setting.name}
                                            </p>
                                            {setting.edit &&
                                            setting.registerName ? (
                                                <EditInfo
                                                    name={setting.value}
                                                    revertEditInput={
                                                        revertEditInput
                                                    }
                                                >
                                                    <Input
                                                        type="text"
                                                        placeholder={`New ${setting.name}`}
                                                        register={register}
                                                        registerName={
                                                            setting.registerName
                                                        }
                                                        errors={errors}
                                                        validation={{
                                                            required: {
                                                                value: true,
                                                                message: `${setting.name} required`,
                                                            },
                                                        }}
                                                    />
                                                </EditInfo>
                                            ) : (
                                                <p className="text-[#667085] break-words">
                                                    {setting.value}
                                                </p>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>
                            <div className="flex flex-col gap-6">
                                <h3 className="text-[#98A2B3] font-medium text-[1.65rem] md:text-[1.875rem] leading-normal md:leading-[2.5313rem]">
                                    Notification settings
                                </h3>
                                <div className="flex flex-col gap-8 max-w-[13.5rem]">
                                    {[
                                        {
                                            name: 'Push Notification',
                                            active: pNotify,
                                            setValue: setPNotify,
                                        },
                                        {
                                            name: 'Email Notification',
                                            active: eNotify,
                                            setValue: setENotify,
                                        },
                                        {
                                            name: 'SMS Notification',
                                            active: sNotify,
                                            setValue: setSNotify,
                                        },
                                    ].map((setting) => (
                                        <div
                                            key={setting.name}
                                            className={`flex justify-between gap-6 items-center`}
                                        >
                                            <p className="text-[#182230] font-medium">
                                                {setting.name}
                                            </p>
                                            <div
                                                className={`flex rounded-xl p-[.125rem] w-[2.75rem] ${setting.active ? 'justify-end bg-primary' : 'justify-start bg-[#F2F4F7]'} cursor-pointer`}
                                                onClick={() =>
                                                    setting.setValue(
                                                        (value) => !value
                                                    )
                                                }
                                            >
                                                <div className="w-5 h-5 rounded-full bg-white shadow-sm"></div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            <div className="flex flex-col gap-6 max-w-md">
                                <h3 className="text-[#98A2B3] font-medium text-[1.65rem] md:text-[1.875rem] leading-normal md:leading-[2.5313rem]">
                                    Developers Options
                                </h3>
                                <p className="text-[#667085] text-sm">
                                    This section is for developers who want to
                                    integrate with our API. Please refer to{' '}
                                    <Link
                                        href="https://cyberbeats.notion.site/CIP-Documentation-201fd6c960a280c6b185d9a9807c3a0c"
                                        className="text-blue-600 hover:underline"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                    >
                                        our documentation
                                    </Link>{' '}
                                    for more information.
                                </p>
                                <div className="flex flex-col gap-4 mt-4">
                                    <p
                                        className="text-[#182230] font-medium text-[1.125rem] leading-[1.5188rem] flex items-center gap-4 w-full justify-between
                                        flex-wrap
                                    "
                                    >
                                        <span>API Key:</span>
                                        <span className="flex items-center gap-2">
                                            <code className="text-[#182230] font-sm text-wrap break-all">
                                                {apikey}
                                            </code>
                                            <RotateCcw
                                                className={`text-primary cursor-pointer hover:text-blue-600 transition-colors 
                                            ${generatingApiKey ? 'animate-spin' : ''}`}
                                                onClick={generateApiKey}
                                            />
                                        </span>
                                    </p>
                                    <p
                                        className="text-[#182230] font-medium text-[1.125rem] leading-[1.5188rem] flex items-center gap-4 w-full justify-between
                                        flex-wrap"
                                    >
                                        <span>Webhook Secret: </span>
                                        <code className="text-[#182230] font-sm text-wrap break-all">
                                            {webhook_secret}
                                        </code>
                                    </p>

                                    {devOptions.map((setting) => (
                                        <div
                                            key={setting.name}
                                            className={`flex justify-between gap-5 items-center`}
                                        >
                                            <p className="text-[#182230] font-medium">
                                                {setting.name}
                                            </p>
                                            {setting.edit &&
                                            setting.registerName ? (
                                                <EditInfo
                                                    name={setting.value}
                                                    revertEditInput={
                                                        revertEditInput
                                                    }
                                                >
                                                    <Input
                                                        type="url"
                                                        placeholder={`New ${setting.name}`}
                                                        register={register}
                                                        registerName={
                                                            setting.registerName
                                                        }
                                                        errors={errors}
                                                        validation={{
                                                            required: {
                                                                value: true,
                                                                message: `${setting.name} required`,
                                                            },
                                                        }}
                                                    />
                                                </EditInfo>
                                            ) : (
                                                <p className="text-[#667085] break-words">
                                                    {setting.value}
                                                </p>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                        <button
                            type="button"
                            className="max-w-xs w-full border border-primary text-primary p-2 text-sm font-medium rounded flex items-center justify-center"
                            onClick={handleSubmit(onSubmit)}
                        >
                            {isSubmitting ? (
                                <span>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        className="lucide lucide-loader-circle animate-spin"
                                    >
                                        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                    </svg>
                                </span>
                            ) : (
                                'Save Changes'
                            )}
                        </button>
                    </form>
                )}
                {section === 'Security' && (
                    <form
                        method="POST"
                        className="max-w-xl flex flex-col gap-6"
                    >
                        <h3 className="text-[#98A2B3] font-medium text-[1.65rem] md:text-[1.875rem] leading-normal md:leading-[2.5313rem]">
                            Security settings
                        </h3>
                        {showChangePassword ? (
                            <div className="flex flex-col gap-[2.75rem] max-w-md mx-auto md:mx-0 w-full">
                                <div className="flex flex-col gap-5 md:gap-7">
                                    <Input
                                        label="Old Password"
                                        type="password"
                                        placeholder="Input old password"
                                        register={register1}
                                        registerName="oldPassword"
                                        errors={errors1}
                                        validation={{
                                            required: {
                                                value: true,
                                                message: `This input is required`,
                                            },
                                        }}
                                    />
                                    <Input
                                        label="New Password"
                                        type="password"
                                        placeholder="Input new password"
                                        register={register1}
                                        registerName="newPassword"
                                        errors={errors1}
                                        validation={{
                                            required: {
                                                value: true,
                                                message: `This input is required`,
                                            },
                                            minLength: {
                                                value: 8,
                                                message:
                                                    'Password must be at least 8 characters',
                                            },
                                        }}
                                    />
                                    <Input
                                        label="Confirm New Password"
                                        type="password"
                                        placeholder="Confirm new password"
                                        register={register1}
                                        registerName="reNewPassword"
                                        errors={errors1}
                                        validation={{
                                            required: {
                                                value: true,
                                                message: `This input is required`,
                                            },
                                            validate: (value) =>
                                                value === password ||
                                                'Passwords do not match',
                                        }}
                                    />
                                </div>

                                <div className="flex flex-col-reverse gap-5 md:flex-row md:items-center">
                                    <button
                                        type="button"
                                        onClick={() =>
                                            setShowChangePassword(false)
                                        }
                                        className={`font-vietnam py-3 px-4 rounded-lg bg-white text-primary w-full flex items-center justify-center leading-[1.5rem]`}
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        type="button"
                                        onClick={handleSubmit1(changePassword)}
                                        className={`font-vietnam py-3 px-4 rounded-lg bg-primary text-white w-full flex items-center justify-center leading-[1.5rem]`}
                                    >
                                        {isSubmittingChangePassword ? (
                                            <span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="24"
                                                    height="24"
                                                    viewBox="0 0 24 24"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    strokeWidth="2"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    className="lucide lucide-loader-circle animate-spin"
                                                >
                                                    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                                </svg>
                                            </span>
                                        ) : (
                                            'Change'
                                        )}
                                    </button>
                                </div>
                            </div>
                        ) : (
                            <div className="flex gap-5 justify-between flex-wrap">
                                <div className="flex flex-col gap-2">
                                    <p className="text-[#182230] font-medium text-[1.125rem] leading-[1.5188rem]">
                                        Update your password
                                    </p>
                                    <p className="text-[#667085] text-sm">
                                        Secure your account by choosing a new
                                        password
                                    </p>
                                </div>
                                <button
                                    type="button"
                                    className="text-sm font-medium text-primary"
                                    onClick={() => setShowChangePassword(true)}
                                >
                                    Change Password
                                </button>
                            </div>
                        )}
                    </form>
                )}
            </div>
        </div>
    )
}
